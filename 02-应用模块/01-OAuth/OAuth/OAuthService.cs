using Common.Core.Manager.User;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Furion.SpecificationDocument;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Service;
using IotPlatform.Core.Service.Auth.Dto;
using Systems.Core;
using Systems.Core.Config;
using Systems.Core.OnlineUser;
using Systems.Entity;
using Systems.Entity.Dto;

namespace OAuth;

/// <summary>
///     身份认证模块
/// </summary>
[ApiDescriptionSettings(Tag = "OAuth", Order = 100)]
public class OAuthService : IDynamicApiController, ITransient
{
    private readonly IUserManager _userManager;
    private readonly ISqlSugarRepository<SysUser> _sysUserRep;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly SysMenuService _sysMenuService;
    private readonly SysConfigService _sysConfigService;
    private readonly SysOnlineUserService _sysOnlineUserService;
    private readonly SysServiceService _sysAppService; // 系统应用服务

    public OAuthService(IUserManager userManager,
        ISqlSugarRepository<SysUser> sysUserRep,
        IHttpContextAccessor httpContextAccessor,
        SysMenuService sysMenuService,
        SysConfigService sysConfigService, SysServiceService sysAppService, SysOnlineUserService sysOnlineUserService)
    {
        _userManager = userManager;
        _sysUserRep = sysUserRep;
        _httpContextAccessor = httpContextAccessor;
        _sysMenuService = sysMenuService;
        _sysConfigService = sysConfigService;
        _sysAppService = sysAppService;
        _sysOnlineUserService = sysOnlineUserService;
    }

    /// <summary>
    ///     账号密码登录
    /// </summary>
    /// <param name="input"></param>
    /// <remarks>用户名/密码：superadmin/123456</remarks>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/login")]
    [DisplayName("账号密码登录")]
    public async Task<LoginOutput> Login([Required] LoginInput input)
    {
        //// 可以根据域名获取具体租户
        //var host = _httpContextAccessor.HttpContext.Request.Host;

        // 账号是否存在
        SysUser user = await _sysUserRep.AsQueryable().Includes(t => t.SysOrg).ClearFilter().FirstAsync(u => u.Account.Equals(input.Account));
        _ = user ?? throw Oops.Oh(ErrorCode.D0009);

        // 账号是否被冻结
        if (!user.Enable)
        {
            throw Oops.Oh(ErrorCode.D1017);
        }

        // 租户是否被禁用
        SysTenant tenant = await _sysUserRep.AsSugarClient().Queryable<SysTenant>().FirstAsync(u => u.Id == user.TenantId);
        if (tenant != null && !tenant.Status)
        {
            throw Oops.Oh(ErrorCode.Z1003);
        }

        // 密码是否正确
        if (!user.Password.Equals(input.Password))
        {
            throw Oops.Oh(ErrorCode.D1000);
        }

        return await CreateToken(user);
    }

    /// <summary>
    ///     生成Token令牌
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<LoginOutput> CreateToken(SysUser user)
    {
        // 单用户登录
        await _sysOnlineUserService.SingleLogin(user.Id);
        
        // 生成Token令牌
        int tokenExpire = await _sysConfigService.GetTokenExpire();
        string accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            {ClaimConst.UserId, user.Id},
            {ClaimConst.TenantId, user.TenantId},
            {ClaimConst.Account, user.Account},
            {ClaimConst.RealName, user.Name},
            {ClaimConst.AdminType, user.AdminType},
            {ClaimConst.OrgId, user.OrgId},
            {ClaimConst.OrgName, user.SysOrg?.Name}
        }, tokenExpire);

        // 生成刷新Token令牌
        int refreshTokenExpire = await _sysConfigService.GetRefreshTokenExpire();
        string refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, refreshTokenExpire);

        // 设置响应报文头
        _httpContextAccessor.HttpContext.SetTokensOfResponseHeaders(accessToken, refreshToken);

        // Swagger Knife4UI-AfterScript登录脚本
        // ke.global.setAllHeader('Authorization', 'Bearer ' + ke.response.headers['access-token']);

        return new LoginOutput
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken
        };
    }

    /// <summary>
    ///     获取登录账号
    /// </summary>
    /// <returns></returns>
    [HttpGet("/getLoginUser")]
    [DisplayName("获取登录账号")]
    public async Task<dynamic> GetUserInfo()
    {
        SysUser user = await _sysUserRep.GetFirstAsync(u => u.Id == _userManager.UserId) ?? throw Oops.Oh(ErrorCode.D1011).StatusCode(401);
        // 获取机构
        SysOrg org = await _sysUserRep.AsSugarClient().Queryable<SysOrg>().FirstAsync(u => u.Id == user.OrgId);
        // 获取拥有按钮权限集合
        List<string> buttons = await _sysMenuService.GetOwnBtnPermList();
        // 具备服务（多系统，默认激活一个，可根据系统切换菜单）,返回的结果中第一个为激活的系统
        List<ServiceOutput> services = await _sysAppService.GetLoginServices(user.Id);
        // 应用
        List<SysApp> apps = await _sysUserRep.AsSugarClient().Queryable<SysApp>()
            .Where(w => w.Status == MetaDataStatusEnum.Online)
            .OrderBy(w => w.Sort)
            .ToListAsync();
        foreach (SysApp app in apps)
        {
            if (app.Client?.DevType == "INTEGRATION" || !(app.Client?.ServiceId > 0))
            {
                continue;
            }

            foreach (ServiceOutput service in services.Where(w => w.Id == app.Client.ServiceId))
            {
                service.Display = false;
            }
        }
        return new
        {
            user.Id,
            user.Account,
            RealName = user.Name,
            user.AdminType,
            user.Avatar,
            user.OrgId,
            OrgName = org?.Name,
            Buttons = buttons,
            Services = services,
            Apps = apps
        };
    }

    /// <summary>
    ///     获取刷新Token
    /// </summary>
    /// <param name="accessToken"></param>
    /// <returns></returns>
    [DisplayName("获取刷新Token")]
    public string GetRefreshToken([FromQuery] string accessToken)
    {
        int refreshTokenExpire = _sysConfigService.GetRefreshTokenExpire().GetAwaiter().GetResult();
        return JWTEncryption.GenerateRefreshToken(accessToken, refreshTokenExpire);
    }

    /// <summary>
    ///     退出登录
    /// </summary>
    [HttpPost("/logout")]
    [DisplayName("退出登录")]
    public void Logout()
    {
        if (string.IsNullOrWhiteSpace(_userManager.Account))
        {
            throw Oops.Oh(ErrorCode.D1011);
        }
#if DEBUG
        _httpContextAccessor.HttpContext.SignoutToSwagger();
#endif
    }

    /// <summary>
    ///     Swagger登录检查
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/swagger/checkUrl")]
    [NonUnify]
    [DisplayName("Swagger登录检查")]
    public int SwaggerCheckUrl()
    {
        return _httpContextAccessor.HttpContext.User.Identity.IsAuthenticated ? 200 : 401;
    }

    /// <summary>
    ///     Swagger登录提交
    /// </summary>
    /// <param name="auth"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/swagger/submitUrl")]
    [NonUnify]
    [DisplayName("Swagger登录提交")]
    public async Task<int> SwaggerSubmitUrl([FromForm] SpecificationAuth auth)
    {
        try
        {
            await Login(new LoginInput
            {
                Account = auth.UserName,
                Password = MD5Encryption.Encrypt(auth.Password)
            });

            return 200;
        }
        catch (Exception)
        {
            return 401;
        }
    }
}