using Common.Models;
using Common.Security;
using IotPlatform.Core.Enum;

namespace Systems.Core;

/// <summary>
///     系统菜单服务
/// </summary>
[ApiDescriptionSettings("系统服务",Order = 450)]
public class SysMenuService : IDynamicApiController, ITransient
{
    private readonly IUserManager _userManager;
    private readonly ISqlSugarRepository<SysMenu> _sysMenuRep;
    private readonly SysRoleMenuService _sysRoleMenuService;
    private readonly SysUserRoleService _sysUserRoleService;

    public SysMenuService(IUserManager userManager,
        ISqlSugarRepository<SysMenu> sysMenuRep,
        SysRoleMenuService sysRoleMenuService,
        SysUserRoleService sysUserRoleService)
    {
        _userManager = userManager;
        _sysMenuRep = sysMenuRep;
        _sysRoleMenuService = sysRoleMenuService;
        _sysUserRoleService = sysUserRoleService;
    }

    /// <summary>
    ///     获取用户菜单所属的应用编码集合
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<List<string>> GetUserMenuAppCodeList(long userId)
    {
        List<long> roleIdList = await _sysUserRoleService.GetUserRoleIdList(userId);
        List<long> menuIdList = await _sysRoleMenuService.GetRoleMenuIdList(roleIdList);
        return await _sysMenuRep.AsQueryable().Where(u => menuIdList.Contains(u.Id))
            .Where(u => u.Status == true)
            .Select(u => u.Application).ToListAsync();
    }

    /// <summary>
    ///     获取用户AntDesign菜单集合
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="appCode"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<List<AntDesignTreeNode>> GetLoginMenusAntDesign(long userId, string appCode)
    {
        List<SysMenu> sysMenuList;
        // 管理员则展示所有系统菜单
        if (_userManager.IsAdministrator)
        {
            sysMenuList = await _sysMenuRep.AsQueryable()
                .Where(u => u.Status == true)
                .Where(u => u.Application == appCode)
                .Where(u => u.Type != MenuEnum.Btn)
                //.Where(u => u.Weight != (int)MenuWeight.DEFAULT_WEIGHT)
                .OrderBy(u => u.Sort).OrderBy(u => u.Id).ToListAsync();
        }
        else
        {
            // 非管理员则获取自己角色所拥有的菜单集合
            List<long> roleIdList = await _sysUserRoleService.GetUserRoleIdList(userId);
            List<long> menuIdList = await _sysRoleMenuService.GetRoleMenuIdList(roleIdList);
            sysMenuList = await _sysMenuRep.AsQueryable()
                .Where(u => menuIdList.Contains(u.Id))
                .Where(u => u.Status == true)
                .Where(u => u.Application == appCode)
                .Where(u => u.Type != MenuEnum.Btn)
                .OrderBy(u => u.Sort).OrderBy(u => u.Id).ToListAsync();
        }

        // 转换成登录菜单
        List<AntDesignTreeNode> antDesignTreeNodes = sysMenuList.Select(u => new AntDesignTreeNode
        {
            Id = u.Id,
            Pid = u.Pid,
            Path = u.OpenType == MenuOpenEnum.OUTER ? u.Link : u.Router,
            Name = u.Code,
            Component = u.Component,
            Application = u.Application,
            Sort = u.Sort,
            Redirect = u.OpenType == MenuOpenEnum.OUTER ? u.Link : u.Redirect,
            Meta = new Meta
            {
                Title = u.Name,
                Icon = u.Icon,
                Show = u.Visible,
                Link = u.Link,
                Target = u.OpenType == MenuOpenEnum.OUTER ? "_blank" : "",
                ModuleId = u.ModuleId
            }
        }).ToList();

        List<AntDesignTreeNode> orderAntDesignTreeNodes = antDesignTreeNodes.OrderBy(o => o.Application).ThenBy(p => p.Sort).ToList();
        return orderAntDesignTreeNodes;
    }

    /// <summary>
    ///     系统菜单列表（树表）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysMenu/list")]
    [DisplayName("系统菜单列表（树表）")]
    public async Task<List<SysMenu>> GetMenuList([FromQuery] GetMenuListInput input)
    {
        ISugarQueryable<SysMenu> menus = _sysMenuRep.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.Application?.Trim()), w => w.Application == input.Application.Trim())
            .WhereIF(!string.IsNullOrEmpty(input.Name?.Trim()), w => w.Name.Contains(input.Name))
            .Where(u => u.Status == true).OrderBy(u => u.Sort);
        return await menus.ToTreeAsync(u => u.Children, u => u.Pid, 0);
    }

    /// <summary>
    ///     根据应用编码判断该机构下是否有状态为正常的菜单
    /// </summary>
    /// <param name="appCode"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<bool> HasMenu(string appCode)
    {
        return await _sysMenuRep.IsAnyAsync(u => u.Application == appCode && u.Status != true);
    }

    /// <summary>
    ///     获取用户菜单集合
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="appCode"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<List<Shortcut>> GetLoginMenusAppCode(long userId, string appCode)
    {
        List<SysMenu> sysMenuList;
        // 管理员则展示所有系统菜单
        if (_userManager.IsAdministrator)
        {
            sysMenuList = await _sysMenuRep.AsQueryable()
                .Where(u => u.Status == true)
                .Where(w => w.Component != "")
                .Where(u => u.Application == appCode)
                .Where(u => u.Type != MenuEnum.Btn)
                .OrderBy(u => u.Sort).OrderBy(u => u.Id).ToListAsync();
        }
        else
        {
            // 非管理员则获取自己角色所拥有的菜单集合
            List<long> roleIdList = await _sysUserRoleService.GetUserRoleIdList(userId);
            List<long> menuIdList = await _sysRoleMenuService.GetRoleMenuIdList(roleIdList);
            sysMenuList = await _sysMenuRep.AsQueryable()
                .Where(u => menuIdList.Contains(u.Id))
                .Where(u => u.Status == true)
                .Where(w => w.Component != "")
                .Where(u => u.Application == appCode)
                .Where(u => u.Type != MenuEnum.Btn)
                .OrderBy(u => u.Sort).OrderBy(u => u.Id).ToListAsync();
        }

        return sysMenuList.Select(u => new Shortcut
        {
            Id = u.Id,
            Name = u.Name,
            Code = u.Code,
            Router = u.Router
        }).ToList();
    }

    /// <summary>
    ///     获取登录菜单树
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取登录菜单树")]
    public async Task<List<SysMenu>> GetLoginMenuTree()
    {
        if (_userManager.IsAdministrator)
        {
            List<SysMenu> menuList = await _sysMenuRep.AsQueryable()
                .Where(u => u.Type != MenuEnum.Btn && u.Status == true)
                .OrderBy(u => new {u.Id}).ToTreeAsync(u => u.Children, u => u.Pid, 0);
            return menuList;
        }

        List<long> menuIdList = await GetMenuIdList();
        List<SysMenu> menuTree = await _sysMenuRep.AsQueryable()
            .Where(u => u.Status == true)
            .OrderBy(u => new {u.Id}).ToTreeAsync(u => u.Children, u => u.Pid, 0, menuIdList.Select(d => (object) d).ToArray());
        DeleteBtnFromMenuTree(menuTree);
        return menuTree;
    }

    /// <summary>
    ///     删除登录菜单树里面的按钮
    /// </summary>
    private void DeleteBtnFromMenuTree(List<SysMenu> menuList)
    {
        if (menuList == null)
        {
            return;
        }

        for (int i = menuList.Count - 1; i >= 0; i--)
        {
            SysMenu menu = menuList[i];
            if (menu.Type == MenuEnum.Btn)
            {
                menuList.Remove(menu);
            }
            else if (menu.Children.Count > 0)
            {
                DeleteBtnFromMenuTree(menu.Children);
            }
        }
    }

    /// <summary>
    ///     获取菜单列表
    /// </summary>
    /// <returns></returns>
    [AllowAnonymous]
    [DisplayName("获取菜单列表")]
    public async Task<List<SysMenu>> GetList([FromQuery] MenuInput input)
    {
        List<long> menuIdList = _userManager.IsAdministrator ? new List<long>() : await GetMenuIdList();

        // 有筛选条件时返回list列表（防止构造不出树）
        if (!string.IsNullOrWhiteSpace(input.Name) || input.Type is > 0)
        {
            return await _sysMenuRep.AsQueryable()
                .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name))
                .WhereIF(input.Type is > 0, u => u.Type == input.Type)
                .WhereIF(menuIdList.Count > 1, u => menuIdList.Contains(u.Id))
                .OrderBy(u => u.Id).ToListAsync();
        }

        return _userManager.IsAdministrator
            ? await _sysMenuRep.AsQueryable().OrderBy(u => u.Id).ToTreeAsync(u => u.Children, u => u.Pid, 0)
            : await _sysMenuRep.AsQueryable()
                .OrderBy(u => u.Id).ToTreeAsync(u => u.Children, u => u.Pid, 0, menuIdList.Select(d => (object) d).ToArray()); // 角色菜单授权时
    }

    /// <summary>
    ///     增加系统菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysMenu/add")]
    [DisplayName("增加系统菜单")]
    public async Task AddMenu(AddMenuInput input)
    {
        bool isExist = await _sysMenuRep.IsAnyAsync(u => u.Code == input.Code); // u.Name == input.Name
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D4000);
        }

        SysMenu sysMenu = input.Adapt<SysMenu>();
        // 校验参数
        CheckMenuParam(sysMenu);

        SysMenu menu = input.Adapt<SysMenu>();
        menu.Pids = await CreateNewPids(input.Pid);
        menu.Status = true;
        await _sysMenuRep.InsertAsync(menu);

        // 清除缓存
        DeleteMenuCache();
    }

    /// <summary>
    ///     创建Pids格式
    ///     如果pid是0顶级节点，pids就是 [0];
    ///     如果pid不是顶级节点，pids就是 pid菜单的 pids + [pid] + ,
    /// </summary>
    /// <param name="pid"></param>
    /// <returns></returns>
    public async Task<string> CreateNewPids(long pid)
    {
        if (pid == 0L)
        {
            return "[0],";
        }

        SysMenu pmenu = await _sysMenuRep.AsQueryable().FirstAsync(u => u.Id == pid);
        return pmenu?.Pids + "[" + pid + "],";
    }

    /// <summary>
    ///     删除系统菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysMenu/delete")]
    [DisplayName("删除系统菜单")]
    public async Task DeleteMenu(BaseId input)
    {
        List<long> childIdList = await _sysMenuRep.AsQueryable().Where(u => u.Pids.Contains(input.Id.ToString()))
            .Select(u => u.Id).ToListAsync();
        childIdList.Add(input.Id);

        List<SysMenu> menus = await _sysMenuRep.AsQueryable().Where(u => childIdList.Contains(u.Id)).ToListAsync();
        await _sysMenuRep.DeleteAsync(menus);
        List<long> menuIdList = menus.Select(u => u.Id).ToList();
        // 级联删除角色菜单数据
        await _sysRoleMenuService.DeleteRoleMenuByMenuIdList(menuIdList);
        // 清除缓存
        DeleteMenuCache();
    }

    /// <summary>
    ///     更新系统菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysMenu/edit")]
    [DisplayName("更新系统菜单")]
    public async Task UpdateMenu(UpdateMenuInput input)
    {
        // Pid和Id不能一致，一致会导致无限递归
        if (input.Id == input.Pid)
        {
            throw Oops.Oh(ErrorCode.D4006);
        }

        bool isExist = await _sysMenuRep.IsAnyAsync(u => u.Code == input.Code && u.Id != input.Id); // u.Name == input.Name
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D4000);
        }

        // 校验参数
        CheckMenuParam(input.Adapt<SysMenu>());
        // 如果是编辑，父id不能为自己的子节点
        List<long> childIdList = await _sysMenuRep.AsQueryable().Where(u => u.Pids.Contains(input.Id.ToString()))
            .Select(u => u.Id).ToListAsync();
        if (childIdList.Contains(input.Pid))
        {
            throw Oops.Oh(ErrorCode.D4006);
        }

        SysMenu oldMenu = await _sysMenuRep.GetFirstAsync(u => u.Id == input.Id);

        // 生成新的pids
        string newPids = await CreateNewPids(input.Pid);

        // 是否更新子应用的标识
        bool updateSubAppsFlag = false;
        // 是否更新子节点的pids的标识
        bool updateSubPidsFlag = false;

        // 如果应用有变化
        if (input.Application != oldMenu.Application)
        {
            // 父节点不是根节点不能移动应用
            if (oldMenu.Pid != 0L)
            {
                throw Oops.Oh(ErrorCode.D4007);
            }

            updateSubAppsFlag = true;
        }

        // 父节点有变化
        if (input.Pid != oldMenu.Pid)
        {
            updateSubPidsFlag = true;
        }

        // 开始更新所有子节点的配置
        if (updateSubAppsFlag || updateSubPidsFlag)
        {
            // 查找所有叶子节点，包含子节点的子节点
            SysMenu menu = oldMenu;
            List<SysMenu> menuList = await _sysMenuRep.AsQueryable().Where(u => u.Pids.Contains(menu.Id.ToString())).ToListAsync();
            // 更新所有子节点的应用为当前菜单的应用
            if (menuList.Count > 0)
            {
                // 更新所有子节点的application
                if (updateSubAppsFlag)
                {
                    menuList.ForEach(u => { u.Application = input.Application; });
                }

                // 更新所有子节点的pids
                if (updateSubPidsFlag)
                {
                    menuList.ForEach(u =>
                    {
                        // 子节点pids组成 = 当前菜单新pids + 当前菜单id + 子节点自己的pids后缀
                        string oldParentCodesPrefix = oldMenu.Pids + "[" + oldMenu.Id + "],";
                        string oldParentCodesSuffix = u.Pids[oldParentCodesPrefix.Length..];
                        string menuParentCodes = newPids + "[" + oldMenu.Id + "]," + oldParentCodesSuffix;
                        u.Pids = menuParentCodes;
                    });
                }
            }
            // 更新子菜单
            await _sysMenuRep.AsSugarClient().Updateable(menuList).ExecuteCommandAsync();
        }

        // 更新当前菜单
        oldMenu = input.Adapt<SysMenu>();
        oldMenu.Pids = newPids;
        await _sysMenuRep.AsSugarClient().Updateable(oldMenu).ExecuteCommandAsync();

        // 清除缓存
        DeleteMenuCache();
    }

    /// <summary>
    ///     增加和编辑时检查菜单数据
    /// </summary>
    /// <param name="menu"></param>
    private static void CheckMenuParam(SysMenu menu)
    {
        string permission = menu.Permission;
        if (menu.Type == MenuEnum.Btn)
        {
            menu.Name = null;
            menu.Component = null;
            menu.Icon = null;
            menu.Redirect = null;
            if (string.IsNullOrEmpty(permission))
                throw Oops.Oh(ErrorCode.D4003);
            if (!permission.Contains(':'))
                throw Oops.Oh(ErrorCode.D4004);
        }
        else
        {
            menu.Permission = null;
        }
    }

    /// <summary>
    ///     获取用户拥有按钮权限集合（缓存）
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取按钮权限集合")]
    public async Task<List<string>> GetOwnBtnPermList()
    {
        List<long> menuIdList = _userManager.IsAdministrator ? new List<long>() : await GetMenuIdList();
        List<string>? permissions = menuIdList.Count > 0 || _userManager.IsAdministrator
            ? await _sysMenuRep.AsQueryable()
                .Where(u => u.Type == MenuEnum.Btn)
                .WhereIF(menuIdList.Count > 0, u => menuIdList.Contains(u.Id))
                .Select(u => u.Permission).ToListAsync()
            : new List<string>();

        return permissions;
    }

    /// <summary>
    ///     获取系统所有按钮权限集合（缓存）
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task<List<string>> GetAllBtnPermList()
    {
        List<string> permissions = await _sysMenuRep.AsQueryable()
            .Where(u => u.Type == MenuEnum.Btn)
            .Select(u => u.Permission).ToListAsync();

        return permissions;
    }

    /// <summary>
    ///     清除菜单和按钮缓存
    /// </summary>
    private void DeleteMenuCache()
    {
        // _sysCacheService.RemoveByPrefixKey(CacheConst.KeyUserMenu);
        // _sysCacheService.RemoveByPrefixKey(CacheConst.KeyUserButton);
    }

    /// <summary>
    ///     获取当前用户菜单Id集合
    /// </summary>
    /// <returns></returns>
    private async Task<List<long>> GetMenuIdList()
    {
        List<long> roleIdList = await _sysUserRoleService.GetUserRoleIdList(_userManager.UserId);
        return await _sysRoleMenuService.GetRoleMenuIdList(roleIdList);
    }

    /// <summary>
    ///     获取系统菜单树，用于新增、编辑时选择上级节点
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysMenu/tree")]
    [DisplayName("获取系统菜单树，用于新增、编辑时选择上级节点")]
    public async Task<dynamic> GetMenuTree([FromQuery] GetMenuTreeInput input)
    {
        ISugarQueryable<SysMenu> menus = _sysMenuRep.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.Application), u => u.Application == input.Application)
            .Where(u => u.Status == true)
            .Where(u => u.Type == MenuEnum.DIR || u.Type == MenuEnum.MENU)
            .OrderBy(u => u.Sort);
        return await menus.ToTreeAsync(u => u.Children, u => u.Pid, 0);
    }

    /// <summary>
    ///     服务菜单树-功能设计-同步菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysMenu/application/tree")]
    [DisplayName("服务菜单树-功能设计-同步菜单")]
    [Obsolete]
    public async Task<dynamic> GetApplicationMenuTree([FromQuery] GetMenuTreeInput input)
    {
        List<SysMenu>? menus = await _sysMenuRep.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.Application), u => u.Application == input.Application)
            .Where(u => u.Status == true)
            .Where(u => u.Type == MenuEnum.DIR)
            .OrderBy(u => u.Sort)
            .ToListAsync();

        List<SysService>? applicationList = await _sysMenuRep.AsSugarClient().Queryable<SysService>().Where(w => menus.Select(s => s.Application).Contains(w.Code)).ToListAsync();
        List<dynamic> output = new List<dynamic>();
        foreach (IGrouping<string, SysMenu> item in menus.GroupBy(g => g.Application))
        {
            SysService? application = applicationList.FirstOrDefault(u => u.Code == item.Key);
            List<SysMenu> menuChildren = menus.Where(w => w.Application == item.Key).ToList();
            output.Add(new
            {
                id = application?.Id,
                fullName = application?.Name ?? "",
                icon = "",
                type = 0,
                systemId = item.Key,
                parentId = 0,
                hasChildren = menuChildren.Any(),
                code = "",
                children = menuChildren.Select(s => new
                {
                    id = s.Id,
                    fullName = s.Name,
                    icon =s.Icon,
                    type = 1,
                    systemId = item.Key,
                    parentId = application?.Id,
                    hasChildren = false,
                    children = new List<dynamic>(),
                    num = 0,
                    code = s.Code,
                    isLeaf = true
                }),
                num = 0,
                isLeaf = false
            });
        }

        return output;
    }

    /// <summary>
    ///     服务菜单树-功能设计-同步菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysMenu/application/tree/v2")]
    [DisplayName("服务菜单树-功能设计-同步菜单")]
    public async Task<dynamic> GetApplicationMenuTreeNew([FromQuery] GetMenuTreeInput input)
    {
        List<SysService>? applicationList = await _sysMenuRep.AsSugarClient().Queryable<SysService>().Where(w => w.Status == true).ToListAsync();
        
        List<SysMenu>? menus = await _sysMenuRep.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.Application), u => u.Application == input.Application)
            .Where(u => u.Status == true)
            .Where(u => u.Type == MenuEnum.DIR)
            .OrderBy(u => u.Sort)
            .ToTreeAsync(u => u.Children, u => u.Pid, 0);
        // 返回对象
        List<dynamic> output = new List<dynamic>();
        foreach (var application in applicationList)
        {
            List<SysMenu> menuChildren = menus.Where(w => w.Application == application.Code).ToList();
            output.Add(new
            {
                id = application?.Id,
                fullName = application?.Name ?? "",
                icon = "",
                type = 0,
                systemId = application?.Code,
                parentId = 0,
                hasChildren = menuChildren.Any(),
                code = "",
                children = MapSysMenuToMenuDto(menuChildren,application),
                num = 0,
                isLeaf = false
            });
        }
        return output;
    }
    
    public dynamic MapSysMenuToMenuDto(List<SysMenu> menuChildren, SysService sysService)
    {
        return menuChildren.Select(s => new 
        {
            id = s.Id,
            fullName = s.Name,
            icon = s.Icon,
            type = 1,
            systemId = sysService?.Code,
            parentId = sysService?.Id,
            hasChildren = s.Children != null && s.Children.Count != 0,
            children = s.Children != null ? MapSysMenuToMenuDto(s.Children, sysService) : null,
            num = 0,
            code = s.Code,
            isLeaf =  true
        }).ToList();
    }
    
    /// <summary>
    ///     获取系统菜单树，用于给角色授权时选择
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysMenu/treeForGrant")]
    [DisplayName("获取系统菜单树，用于给角色授权时选择")]
    public async Task<dynamic> TreeForGrant([FromQuery] TreeForGrantInput input)
    {
        List<long> menuIdList = new();
        if (!_userManager.IsAdministrator)
        {
            List<long> roleIdList = await _sysUserRoleService.GetUserRoleIdList(_userManager.UserId);
            menuIdList = await _sysRoleMenuService.GetRoleMenuIdList(roleIdList);
        }

        ISugarQueryable<SysService> apps = _sysMenuRep.AsSugarClient().Queryable<SysService>()
            .Where(u => u.Status == true)
            .WhereIF(!string.IsNullOrEmpty(input.Application), u => u.Code == input.Application.Trim());
        List<TreeForGrantMenuOutput> appList = await apps.OrderBy(u => u.Sort).Select(u => new TreeForGrantMenuOutput
        {
            Code = u.Code,
            Name = u.Name,
            Id = u.Id,
            Menus = new List<SysMenu>()
        }).ToListAsync();

        foreach (TreeForGrantMenuOutput app in appList)
        {
            ISugarQueryable<SysMenu> menus = _sysMenuRep.AsQueryable()
                .Where(w => w.Application == app.Code)
                .Where(u => u.Status == true)
                .WhereIF(menuIdList.Count > 0, u => menuIdList.Contains(u.Id))
                .OrderBy(u => u.Sort);
            app.Menus = await menus.ToTreeAsync(u => u.Children, u => u.Pid, 0);
        }

        // 过滤没有菜单权限的app
        List<TreeForGrantMenuOutput> outputs = appList.Where(w => w.Menus.Any()).ToList();
        return outputs;
    }

    /// <summary>
    ///     根据系统应用切换菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/sysMenu/change")]
    [DisplayName("根据系统应用切换菜单")]
    public async Task<List<AntDesignTreeNode>> ChangeAppMenu(ChangeAppMenuInput input)
    {
        return await GetLoginMenusAntDesign(_userManager.UserId, input.Application);
    }

    /// <summary>
    ///     修改菜单状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysMenu/changeStatus")]
    [DisplayName("修改菜单状态")]
    public async Task ChangeMenuStatus(UpdateMenuStatusInput input)
    {
        try
        {
            SysMenu? menu = await _sysMenuRep.GetFirstAsync(u => u.Id == input.Id);
            if (menu == null)
            {
                throw Oops.Oh(ErrorCode.D1002);
            }

            menu.Visible = input.Status;
            await _sysMenuRep.UpdateAsync(menu);
            // 清除缓存
            DeleteMenuCache();
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    #region public

    /// <summary>
    /// 根据功能主键Id反向查找上级节点树 
    /// </summary>
    [NonAction]
    public async Task<string> GetMenuTreeByModuleId(long moduleId)
    {
        SysMenu? menu = await _sysMenuRep.GetFirstAsync(u => u.ModuleId == moduleId);
        if (menu == null)
        {
            return "";
        }

        List<SysMenu>? menus = await _sysMenuRep.AsQueryable().ToParentListAsync(it => it.Pid, menu.Id);
        List<GetMenuTreeByModuleIdOutput> menusMap = menus.Select(s => new GetMenuTreeByModuleIdOutput
        {
            id = s.Id.ToString(),
            name = s.Name,
            parentId = s.Pid.ToString()
        }).ToList();
        var service = await _sysMenuRep.AsSugarClient().Queryable<SysService>().Where(w => w.Code == menu.Application).FirstAsync();
        var path = "";
        if (service != null)
            path = service.Name;
        var tree = menusMap.ToTree().FirstOrDefault();
        path += BuildPathString(tree, "");
        return path;
    }
    #endregion

    #region Private

    /// <summary>
    /// 根据菜单树生成完整菜单中文名称路径
    /// </summary>
    /// <param name="menu"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    private static string BuildPathString(GetMenuTreeByModuleIdOutput menu, string path)
    {
        // 将当前节点的 name 拼接到路径中
        string currentPath = path + "/" + menu.name;

        if (menu.children == null || menu.children.Count == 0)
        {
            // 当前节点没有子节点，返回当前路径
            return currentPath;
        }

        // 递归遍历子节点，并将子节点的路径拼接到当前路径中
        foreach (GetMenuTreeByModuleIdOutput child in menu.children)
        {
            currentPath = BuildPathString(child, currentPath);
        }

        return currentPath;
    }
    
    #endregion
    
}