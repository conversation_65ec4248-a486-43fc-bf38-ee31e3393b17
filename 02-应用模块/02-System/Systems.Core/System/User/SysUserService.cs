using Common.Models;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Systems.Core.Config;
using DateTime = System.DateTime;

namespace Systems.Core.User;

/// <summary>
///     系统用户服务
/// </summary>
[ApiDescriptionSettings("系统服务",Order = 490)]
public class SysUserService : IDynamicApiController, ITransient
{
    private readonly IUserManager _userManager;
    private readonly ISqlSugarRepository<SysUser> _sysUserRep;
    private readonly SysOrgService _sysOrgService;
    private readonly SysUserRoleService _sysUserRoleService;
    private readonly SysConfigService _sysConfigService;

    public SysUserService(IUserManager userManager,
        ISqlSugarRepository<SysUser> sysUserRep,
        SysOrgService sysOrgService,
        SysUserRoleService sysUserRoleService,
        SysConfigService sysConfigService)
    {
        _userManager = userManager;
        _sysUserRep = sysUserRep;
        _sysOrgService = sysOrgService;
        _sysUserRoleService = sysUserRoleService;
        _sysConfigService = sysConfigService;
    }

    /// <summary>
    ///     获取用户分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysUser/page")]
    public async Task<SqlSugarPagedList<UserOutput>> Page([FromQuery] PageUserInput input)
    {
        // 获取用户拥有的机构集合
        List<long> userOrgIdList = await _sysOrgService.GetUserOrgIdList();
        List<long> orgList = null;
        if (input.OrgId > 0) // 指定机构查询时
        {
            orgList = await _sysOrgService.GetChildIdListWithSelfById(input.OrgId);
            orgList = _userManager.IsAdministrator ? orgList : orgList.Where(u => userOrgIdList.Contains(u)).ToList();
        }
        else // 各管理员只能看到自己机构下的用户列表
        {
            orgList = _userManager.IsAdministrator ? new List<long>() : userOrgIdList;
        }

        return await _sysUserRep.AsQueryable()
            .LeftJoin<SysOrg>((u, a) => u.OrgId == a.Id)
            .Where(u => u.AdminType != AdminTypeEnum.SuperAdmin)
            .WhereIF(orgList != null, u => orgList.Contains(u.OrgId))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Account), u => u.Account.Contains(input.Account))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), u => u.Name.Contains(input.RealName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), u => u.Phone.Contains(input.Phone))
            .OrderBy(u => u.Id)
            .Select((u, a) => new UserOutput
            {
                OrgName = a.Name,
                RoleName = SqlFunc.Subqueryable<SysUserRole>().LeftJoin<SysRole>((m, n) => m.SysRoleId == n.Id).Where(m => m.SysUserId == u.Id).SelectStringJoin((m, n) => n.Name, ",")
            }, true)
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     下拉查询用户
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysUser/select")]
    public async Task<List<UserSelectOutput>> UserSelect()
    {
        List<UserSelectOutput> users = await _sysUserRep.AsQueryable()
            .Where(x => x.AdminType != AdminTypeEnum.SuperAdmin)
            .Select<UserSelectOutput>()
            .ToListAsync();
        return users;
    }

    /// <summary>
    ///     增加用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysUser/add")]
    public async Task AddUser(AddUserInput input)
    {
        bool isExist = await _sysUserRep.IsAnyAsync(u => u.Account == input.Account);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1003);
        }

        SysUser user = input.Adapt<SysUser>();
        user.Password = input.Password;
        if (string.IsNullOrEmpty(user.Name))
        {
            user.Name = user.Account;
        }

        if (string.IsNullOrEmpty(user.NickName))
        {
            user.NickName = user.Account;
        }

        await _sysUserRep.InsertAsync(user);
    }

    /// <summary>
    ///     删除用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysUser/delete")]
    public async Task DeleteUser(BaseId input)
    {
        SysUser user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id);
        if (user == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        switch (user.AdminType)
        {
            case AdminTypeEnum.SuperAdmin:
                throw Oops.Oh(ErrorCode.D1014);
            case AdminTypeEnum.Admin:
                throw Oops.Oh(ErrorCode.D1018);
        }

        if (user.Id == _userManager.UserId)
        {
            throw Oops.Oh(ErrorCode.D1001);
        }

        // 删除用户
        await _sysUserRep.DeleteAsync(user);
        // 删除用户角色
        await _sysUserRoleService.DeleteUserRoleByUserId(input.Id);
    }

    /// <summary>
    ///     更新用户
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysUser/edit")]
    public async Task UpdateUser(UpdateUserInput input)
    {
        // 排除自己并且判断与其他是否相同
        bool isExist = await _sysUserRep.IsAnyAsync(u => u.Account == input.Account && u.Id != input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1003);
        }

        SysUser user = input.Adapt<SysUser>();
        await _sysUserRep.AsUpdateable(user).IgnoreColumns(w => new {w.Password, w.Enable, w.AdminType}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     查看用户
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysUser/detail")]
    public async Task<SysUser> GetUser(long id)
    {
        SysUser user = await _sysUserRep.AsQueryable().Where(u => u.Id == id)
            .Includes(w => w.SysOrg)
            .FirstAsync();
        if (user == null)
            throw Oops.Oh(ErrorCode.D1002);
        return user;
    }

    /// <summary>
    ///     修改用户状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysUser/enable")]
    public async Task ChangeUserStatus(EnableInput<long> input)
    {
        SysUser user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id);
        if (user.AdminType == AdminTypeEnum.SuperAdmin)
        {
            throw Oops.Oh(ErrorCode.D1015);
        }

        user.Enable = input.Enable;
        await _sysUserRep.UpdateAsync(user);
    }

    /// <summary>
    ///     授权用户角色
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysUser/grantRole")]
    public async Task GrantUserRole(UpdateUserRoleDataInput input)
    {
        try
        {
            SysUser user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id);
            switch (user.AdminType)
            {
                case AdminTypeEnum.SuperAdmin:
                    throw Oops.Oh(ErrorCode.D1022);
                // case AdminTypeEnum.Admin:
                //     throw Oops.Oh(ErrorCode.D1008);
                case AdminTypeEnum.None:
                default:
                    await _sysUserRoleService.GrantRole(input);
                    break;
            }
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    ///     更新用户信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysUser/updateInfo")]
    public async Task UpdateUserInfo(UpdateUserBaseInfoInput input)
    {
        SysUser user = await _sysUserRep.GetSingleAsync(u => u.Id == input.Id);
        if (user == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        user.NickName = input.NickName;
        user.Phone = input.Phone;
        user.Avatar = input.Avatar;
        user.Name = input.Name;
        user.JobNum = input.JobNum;
        await _sysUserRep.UpdateAsync(user);
    }

    /// <summary>
    ///     修改用户密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysUser/updatePwd")]
    public async Task UpdateUserPwd(ChangePasswordUserInput input)
    {
        SysUser user = await _sysUserRep.GetSingleAsync(u => u.Id == input.Id);
        if (user == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        if (input.Password != user.Password)
        {
            throw Oops.Oh(ErrorCode.D1004);
        }

        user.Password = input.NewPassword;
        await _sysUserRep.AsUpdateable(user).UpdateColumns(w => w.Password).ExecuteCommandAsync();
    }

    /// <summary>
    ///     获取用户拥有角色
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysUser/ownRole")]
    public async Task<List<long>> GetUserOwnRole([FromQuery] BaseId input)
    {
        return await _sysUserRoleService.GetUserRoleIdList(input.Id);
    }

    /// <summary>
    ///     设置用户状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置用户状态")]
    public async Task<int> SetStatus(UserInput input)
    {
        SysUser user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCode.D0009);
        if (user.AdminType == AdminTypeEnum.SuperAdmin)
        {
            throw Oops.Oh(ErrorCode.D1015);
        }

        user.Enable = input.Status;
        return await _sysUserRep.AsUpdateable(user).UpdateColumns(u => new {Status = u.Enable}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     重置用户密码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysUser/resetPwd")]
    public async Task<string?> ResetPwd(ResetPwdUserInput input)
    {
        SysUser user = await _sysUserRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCode.D0009);
        string? password = await _sysConfigService.GetConfigValue<string>(CommonConst.SysPassword);
        user.Password = MD5Encryption.Encrypt(password);
        await _sysUserRep.AsUpdateable(user).UpdateColumns(u => u.Password).ExecuteCommandAsync();
        return password;
    }

    /// <summary>
    ///     获取机构成员列表.
    /// </summary>
    /// <param name="organizeId">机构ID.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/sysUser/ImUser/Selector/{organizeId}")]
    public async Task<dynamic> GetOrganizeMemberList(long organizeId, [FromBody] PageInputBase input)
    {
        List<OrganizeMemberListOutput>? output = new();
        output = await _sysUserRep.AsQueryable()
            .Where(w => w.OrgId == organizeId)
            .WhereIF(!input.keyword.IsNullOrEmpty(), u => u.Account.Contains(input.keyword) || u.Name.Contains(input.keyword))
            .Where(u => u.Enable == true).OrderBy(o => o.Id)
            .Select(u => new OrganizeMemberListOutput
            {
                id = u.Id.ToString(),
                fullName = u.Name,
                enabledMark = u.Enable == true ? 1 : 0,
                icon = "icon-ym icon-ym-tree-user2",
                headIcon = "/api/File/Image/userAvatar/", //SqlFunc.MergeString("/api/File/Image/userAvatar/", u.HeadIcon),
                isLeaf = true,
                hasChildren = false,
                type = "user",
                isAdministrator = u.AdminType == AdminTypeEnum.SuperAdmin ? 1 : 0
            }).ToListAsync();

        List<OrganizeMemberListOutput>? orgList = await _sysUserRep.AsSugarClient().Queryable<SysOrg>().Where(w => w.Pid == organizeId).Select(u => new OrganizeMemberListOutput
        {
            id = u.Id.ToString(),
            fullName = u.Name,
            enabledMark = u.Status == true ? 1 : 0,
            icon = "icon-ym icon-ym-tree-user2",
            headIcon = "/api/File/Image/userAvatar/", //SqlFunc.MergeString("/api/File/Image/userAvatar/", u.HeadIcon),
            isLeaf = true,
            hasChildren = false,
            type = "department",
            isAdministrator = 0
        }).ToListAsync();
        output.AddRange(orgList);
        return output;
    }

    /// <summary>
    ///     获取.
    /// </summary>
    /// <returns></returns>
    [HttpPost("/sysUser/GetUserList")]
    public async Task<dynamic> GetUserList([FromBody] UserRelationInput input)
    {
        List<SysUser>? data = await _sysUserRep.AsQueryable()
            .Where(it => input.ids.Contains(it.Id.ToString()))
            .Includes(w => w.SysOrg)
            .ToListAsync();

        var output = data.Select(it => new
        {
            id = it.Id.ToString(),
            fullName = it.Name,
            headIcon = "/api/File/Image/userAvatar/", //SqlFunc.MergeString("/api/File/Image/userAvatar/", it.HeadIcon),
            enabledMark = 1,
            orgName = (it.SysOrg.OrganizeIdTree.Replace(",","/") + "/" + it.Name).TrimStart('/'),
            isAdministrator = it.AdminType == AdminTypeEnum.SuperAdmin ? 1 : 0
        });
        return output;
    }
}