using IotPlatform.Core.Enum;

namespace Systems.Core.PrintTemplate;

/// <summary>
/// 打印模板服务
/// </summary>
[ApiDescriptionSettings("系统服务", Order = 500)]
public class PrintTemplateService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<Entity.PrintTemplate> _printTemplateRep;
    private readonly ISqlSugarRepository<PrintTemplateVariable> _printTemplateVariableRep;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="printTemplateRep">打印模板仓储</param>
    /// <param name="printTemplateVariableRep">打印模板变量仓储</param>
    public PrintTemplateService(
        ISqlSugarRepository<Entity.PrintTemplate> printTemplateRep,
        ISqlSugarRepository<PrintTemplateVariable> printTemplateVariableRep)
    {
        _printTemplateRep = printTemplateRep;
        _printTemplateVariableRep = printTemplateVariableRep;
    }

    /// <summary>
    /// 解析模板变量
    /// </summary>
    /// <param name="content">模板内容</param>
    /// <returns>变量列表</returns>
    private List<string> ParseTemplateVariables(string content)
    {
        if (string.IsNullOrEmpty(content))
            return new List<string>();

        var pattern = @"\{([^{}]+)\}";
        var matches = Regex.Matches(content, pattern);

        // 提取所有变量名
        var variables = matches
            .Cast<Match>()
            .Select(m => m.Groups[1].Value)
            .Distinct()
            .ToList();

        return variables;
    }

    /// <summary>
    /// 同步模板变量
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <param name="variables">变量列表</param>
    /// <returns></returns>
    private async Task SyncTemplateVariables(long templateId, List<string> variables)
    {
        // 获取模板当前的变量
        var existingVariables = await _printTemplateVariableRep.GetListAsync(v => v.TemplateId == templateId);
        var existingVariableNames = existingVariables.Select(v => v.VariableName).ToList();

        // 找出需要新增的变量
        var newVariables = variables.Where(v => !existingVariableNames.Contains(v)).ToList();

        // 找出需要删除的变量
        var removedVariables = existingVariables.Where(v => !variables.Contains(v.VariableName)).ToList();

        // 添加新变量
        if (newVariables.Any())
        {
            var newVariableEntities = newVariables.Select(v => new PrintTemplateVariable
            {
                TemplateId = templateId,
                VariableName = v,
                Description = string.Empty
            }).ToList();

            await _printTemplateVariableRep.InsertRangeAsync(newVariableEntities);
        }

        // 删除已移除的变量
        if (removedVariables.Any())
        {
            await _printTemplateVariableRep.DeleteAsync(removedVariables);
        }
    }

    /// <summary>
    /// 添加打印模板
    /// </summary>
    /// <param name="input">打印模板信息</param>
    /// <returns></returns>
    [HttpPost("/printTemplate/add")]
    [DisplayName("添加打印模板")]
    public async Task Add(PrintTemplateInput input)
    {
        var entity = new Entity.PrintTemplate
        {
            TemplateName = input.TemplateName,
            TemplateContent = input.TemplateContent,
            Description = input.Description
        };

        await _printTemplateRep.InsertAsync(entity);

        // 解析并保存变量
        var variables = ParseTemplateVariables(input.TemplateContent);
        await SyncTemplateVariables(entity.Id, variables);
    }

    /// <summary>
    /// 更新打印模板
    /// </summary>
    /// <param name="input">打印模板信息</param>
    /// <returns></returns>
    [HttpPost("/printTemplate/update")]
    [DisplayName("更新打印模板")]
    public async Task Update(PrintTemplateInput input)
    {
        var entity = await _printTemplateRep.GetFirstAsync(u => u.Id == input.Id);
        if (entity == null)
            throw Oops.Oh(ErrorCode.D1002);

        // 更新实体属性
        entity.TemplateName = input.TemplateName;
        entity.TemplateContent = input.TemplateContent;
        entity.Description = input.Description;

        await _printTemplateRep.UpdateAsync(entity);

        // 解析并更新变量
        var variables = ParseTemplateVariables(input.TemplateContent);
        await SyncTemplateVariables(entity.Id, variables);
    }

    /// <summary>
    /// 删除打印模板
    /// </summary>
    /// <param name="input">打印模板Id</param>
    /// <returns></returns>
    [HttpPost("/printTemplate/delete")]
    [DisplayName("删除打印模板")]
    public async Task Delete(DeleteFileInput input)
    {
        var entity = await _printTemplateRep.GetFirstAsync(u => u.Id == input.Id);
        if (entity == null)
            throw Oops.Oh(ErrorCode.D1002);

        // 删除模板变量
        await _printTemplateVariableRep.DeleteAsync(v => v.TemplateId == entity.Id);

        // 删除模板
        await _printTemplateRep.DeleteAsync(entity);
    }

    /// <summary>
    /// 获取打印模板详情
    /// </summary>
    /// <param name="input">打印模板Id</param>
    /// <returns>打印模板详情</returns>
    [HttpGet("/printTemplate/detail")]
    [DisplayName("获取打印模板详情")]
    public async Task<PrintTemplateDto> GetDetail([FromQuery] PrintTemplateDetailInput input)
    {
        var entity = await _printTemplateRep.GetFirstAsync(u => u.Id == input.Id);
        if (entity == null)
            throw Oops.Oh(ErrorCode.D1002);

        // 获取模板变量
        var variables = await _printTemplateVariableRep.GetListAsync(v => v.TemplateId == entity.Id);
        var variableDtos = variables.Select(v => new PrintTemplateVariableDto
        {
            Id = v.Id,
            TemplateId = v.TemplateId,
            VariableName = v.VariableName,
            Description = v.Description
        }).ToList();

        return new PrintTemplateDto
        {
            Id = entity.Id,
            TemplateName = entity.TemplateName,
            TemplateContent = entity.TemplateContent,
            Description = entity.Description,
            Variables = variableDtos
        };
    }

    /// <summary>
    /// 获取打印模板分页列表
    /// </summary>
    /// <returns>打印模板列表</returns>
    [HttpGet("/printTemplate/page")]
    [DisplayName("获取打印模板分页列表")]
    public async Task<List<PrintTemplateDto>> GetPage()
    {
        var result = await _printTemplateRep.AsQueryable()
            .OrderBy(u => u.CreatedTime, OrderByType.Desc)
            .Select(u => new PrintTemplateDto
            {
                Id = u.Id,
                TemplateName = u.TemplateName,
                TemplateContent = u.TemplateContent,
                Description = u.Description,
            })
            .ToListAsync();
        return result;
    }

    /// <summary>
    /// 获取模板变量列表
    /// </summary>
    /// <param name="templateId">模板ID</param>
    /// <returns>变量列表</returns>
    [HttpGet("/printTemplate/variables/{templateId}")]
    [DisplayName("获取模板变量列表")]
    public async Task<List<PrintTemplateVariableDto>> GetTemplateVariables([FromQuery] long templateId)
    {
        var variables = await _printTemplateVariableRep.GetListAsync(v => v.TemplateId == templateId);
        return variables.Select(v => new PrintTemplateVariableDto
        {
            Id = v.Id,
            TemplateId = v.TemplateId,
            VariableName = v.VariableName,
            Description = v.Description,
        }).ToList();
    }

    /// <summary>
    /// 更新模板变量描述
    /// </summary>
    /// <param name="input">变量描述</param>
    /// <returns></returns>
    [HttpPost("/printTemplate/variable/update")]
    [DisplayName("更新模板变量描述")]
    public async Task UpdateVariableDescription(PrintTemplateVariableUpdateInput input)
    {
        var variable = await _printTemplateVariableRep.GetFirstAsync(v => v.Id == input.Id);
        if (variable == null)
            throw Oops.Oh(ErrorCode.D1002);

        variable.Description = input.Description;
        await _printTemplateVariableRep.UpdateAsync(variable);
    }

    /// <summary>
    /// 批量更新模板变量
    /// </summary>
    /// <param name="input">变量列表</param>
    /// <returns></returns>
    [HttpPost("/printTemplate/variables/batchUpdate")]
    [DisplayName("批量更新模板变量")]
    public async Task BatchUpdateVariables(PrintTemplateVariableBatchUpdateInput input)
    {
        // 检查模板是否存在
        var template = await _printTemplateRep.GetFirstAsync(t => t.Id == input.TemplateId);
        if (template == null)
            throw Oops.Oh(ErrorCode.D1002);

        // 获取当前模板所有变量
        var currentVariables = await _printTemplateVariableRep.GetListAsync(v => v.TemplateId == input.TemplateId);

        // 更新变量描述
        foreach (var variableDto in input.Variables)
        {
            var variable = currentVariables.FirstOrDefault(v => v.Id == variableDto.Id);
            if (variable != null)
            {
                variable.Description = variableDto.Description;
            }
        }

        // 批量更新
        if (currentVariables.Any())
        {
            await _printTemplateVariableRep.UpdateRangeAsync(currentVariables);
        }
    }
}