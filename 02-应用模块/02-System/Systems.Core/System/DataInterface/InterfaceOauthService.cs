using System.Linq.Expressions;
using Furion.LinqBuilder;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Systems.Entity.Dto.DataInterfaceLog;
using Systems.Entity.Dto.InterfaceOauth;

namespace Systems.Core;

/// <summary>
///     接口认证
/// </summary>
[ApiDescriptionSettings("数据应用")]
public class InterfaceOauthService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基本仓储.
    /// </summary>
    private readonly ISqlSugarRepository<InterfaceOauthEntity> _repository;

    /// <summary>
    ///     用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    /// </summary>
    public InterfaceOauthService(ISqlSugarRepository<InterfaceOauthEntity> repository, IUserManager userManager)
    {
        _repository = repository;
        _userManager = userManager;
    }

    #region Get

    /// <summary>
    ///     信息.
    /// </summary>
    /// <param name="id">请求参数.</param>
    /// <returns></returns>
    [HttpGet("/interfaceOauth/{id}")]
    public async Task<dynamic> GetInfo(long id)
    {
        InterfaceOauthEntity? info = await _repository.GetFirstAsync(x => x.Id == id);
        InterfaceOauthOutput output = info.Adapt<InterfaceOauthOutput>();
        if (info.IsNotEmptyOrNull() && info.DataInterfaceIds.IsNotEmptyOrNull())
        {
            string[] ids = info.DataInterfaceIds.Split(",");
            output.list = await _repository.AsSugarClient().Queryable<DataInterfaceEntity>()
                .Where(a => ids.Contains(a.Id.ToString()))
                .Select(a => new DataInterfaceListOutput
                {
                    id = a.Id,
                    fullName = a.FullName,
                    enCode = a.EnCode,
                    type = SqlFunc.IF(a.Type == 1).Return("SQL操作").ElseIF(a.Type == 2).Return("静态数据").End("API操作"),
                    creatorTime = a.CreatedTime,
                    creatorUser = SqlFunc.Subqueryable<SysUser>().Where(u => u.Id == a.CreatedUserId)
                        .Select(u => u.Name),
                    sortCode = a.SortCode,
                    enabledMark = a.EnabledMark,
                    tenantId = _userManager.TenantId,
                    isPostPosition = a.IsPostposition,
                    parameterJson = a.ParameterJson,
                    hasPage = a.HasPage,
                    fieldJson = a.FieldJson
                }).ToListAsync();
            output.userList = await _repository.AsSugarClient().Queryable<DataInterfaceUserEntity>()
                .Where(a => a.OauthId == id)
                .Select(a => new
                {
                    id = a.Id,
                    userId = a.UserId,
                    userName = SqlFunc.Subqueryable<SysUser>().Where(u => u.Id == a.UserId)
                        .Select(u => u.Name),
                    userKey = a.UserKey
                }).ToListAsync();
        }

        var dataInterfaceUserEntityList = await _repository.AsSugarClient().Queryable<DataInterfaceUserEntity>()
            .Where(a => a.OauthId == id)
            .Includes(w => w.SysUser,w => w.SysOrg)
            .ToListAsync();
        output.userList = dataInterfaceUserEntityList
            .Select(x => new
            {
                id = x.Id,
                userId = x.UserId,
                userName = x.SysUser?.Name,
                user =  new
                {
                    x.SysUser?.Id,
                    x.SysUser?.Name,
                    x.SysUser?.Account,
                    x.SysUser?.NickName,
                    x.SysUser?.Avatar,
                    x.SysUser?.Phone,
                    x.SysUser?.Enable,
                    OrgName = x.SysUser?.SysOrg?.Name,
                },
                userKey = x.UserKey
            }).ToList();
        return output;
    }

    /// <summary>
    ///     列表.
    /// </summary>
    [HttpGet("/interfaceOauth/page")]
    public async Task<dynamic> GetList([FromQuery] InterfaceOauthListInput input)
    {
        SqlSugarPagedList<InterfaceOauthListOutput> list = await _repository.AsSugarClient()
            .Queryable<InterfaceOauthEntity, SysUser>((a, b) =>
                new JoinQueryInfos(JoinType.Left, b.Id == a.CreatedUserId))
            .WhereIF(input.enabledMark.IsNotEmptyOrNull(), a => a.EnabledMark.Equals(input.enabledMark))
            .WhereIF(!string.IsNullOrEmpty(input.keyword),
                a => a.AppId.Contains(input.keyword) || a.AppName.Contains(input.keyword))
            .OrderBy(a => a.SortCode).OrderBy(a => a.CreatedTime, OrderByType.Desc)
            .Select((a, b) => new InterfaceOauthListOutput
            {
                id = a.Id,
                lastModifyTime = a.UpdatedTime,
                enabledMark = a.EnabledMark,
                creatorUser = b.Name,
                appId = a.AppId,
                appName = a.AppName,
                usefulLife = a.UsefulLife,
                sortCode = a.SortCode,
                creatorTime = a.CreatedTime
            }).ToPagedListAsync(input.currentPage, input.pageSize);
        return list;
    }

    /// <summary>
    ///     获取秘钥.
    /// </summary>
    /// <returns></returns>
    [HttpGet("/interfaceOauth/getAppSecret")]
    public async Task<dynamic> GetAppSecret()
    {
        return Guid.NewGuid().ToString().Replace("-", string.Empty);
    }

    /// <summary>
    ///     日志.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/interfaceOauth/dataInterfaceLog/{id}")]
    public async Task<dynamic> GetList(long id, [FromQuery] DataInterfaceLogListQuery input)
    {
        InterfaceOauthEntity? entity = await _repository.GetFirstAsync(x => x.Id == id);
        Expression<Func<DataInterfaceLogListOutput, bool>>? whereLambda =
            LinqExpression.And<DataInterfaceLogListOutput>();
        if (!input.startTime.IsNullOrEmpty() && !input.endTime.IsNullOrEmpty())
        {
            whereLambda = whereLambda.And(a =>  a.invokTime >= input.startTime && a.invokTime <= input.endTime);
        }

        SqlSugarPagedList<DataInterfaceLogListOutput> list = await _repository.AsSugarClient()
            .Queryable<DataInterfaceLogEntity, SysUser, DataInterfaceEntity>((a, b, c) =>
                new JoinQueryInfos(JoinType.Left, b.Id == a.UserId, JoinType.Left, a.InvokId == c.Id))
            .Where(a => a.OauthAppId == entity.AppId)
            .WhereIF(input.keyword.IsNotEmptyOrNull(),
                (a, b, c) => c.FullName.Contains(input.keyword) || c.EnCode.Contains(input.keyword))
            .Select((a, b, c) => new DataInterfaceLogListOutput
            {
                id = a.Id,
                fullName = c.FullName,
                enCode = c.EnCode,
                invokDevice = a.InvokDevice,
                invokIp = a.InvokIp,
                userId = b.Name,
                invokTime = a.InvokTime,
                invokType = a.InvokType,
                invokWasteTime = a.InvokWasteTime
            }).MergeTable().Where(whereLambda).OrderBy(a => a.invokTime, OrderByType.Desc)
            .ToPagedListAsync(input.currentPage, input.pageSize);
        return list;
    }

    #endregion

    #region Post

    /// <summary>
    ///     新增.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpPost("/interfaceOauth/add")]
    public async Task Create_Api([FromBody] InterfaceOauthInput input)
    {
        if (await _repository.IsAnyAsync(x => x.AppId == input.appId || x.AppName == input.appName))
        {
            throw Oops.Oh(ErrorCode.D3001);
        }

        InterfaceOauthEntity entity = input.Adapt<InterfaceOauthEntity>();
        if (input.usefulLife.IsNullOrEmpty() || input.usefulLife == "0")
        {
            entity.UsefulLife = null;
        }

        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh("新增数据失败");
        }
    }

    /// <summary>
    ///     删除.
    /// </summary>
    /// <param name="id">请求参数.</param>
    /// <returns></returns>
    [HttpPost("/interfaceOauth/{id}/delete")]
    public async Task Delete(long id)
    {
        InterfaceOauthEntity? entity = await _repository.GetFirstAsync(x => x.Id == id);
        if (entity == null)
        {
            throw Oops.Oh("检测数据不存在");
        }

        bool isOk = await _repository.DeleteAsync(entity);
        if (!isOk)
        {
            throw Oops.Oh("删除数据失败");
        }
    }

    /// <summary>
    ///     修改.
    /// </summary>
    /// <param name="id">id.</param>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpPost("/interfaceOauth/{id}/update")]
    public async Task Update(long id, [FromBody] InterfaceOauthInput input)
    {
        if (await _repository.IsAnyAsync(x => x.Id != id && (x.AppId == input.appId || x.AppName == input.appName)))
        {
            throw Oops.Oh("已存在同名或同编码数据");
        }

        InterfaceOauthEntity entity = input.Adapt<InterfaceOauthEntity>();
        bool isOk = await _repository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandHasChangeAsync();
        if (input.usefulLife.IsNullOrEmpty() || input.usefulLife == "0")
        {
            await _repository.AsUpdateable().SetColumns(it => new InterfaceOauthEntity
            {
                UsefulLife = null
            }).Where(it => it.Id.Equals(id)).ExecuteCommandHasChangeAsync();
        }

        if (!isOk)
        {
            throw Oops.Oh("修改数据失败");
        }
    }

    /// <summary>
    ///     授权接口.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpPost("/interfaceOauth/saveInterfaceList")]
    public async Task SaveInterFaceList([FromBody] InterfaceOauthSaveInput input)
    {
        bool isOk = await _repository.AsSugarClient().Updateable<InterfaceOauthEntity>()
            .SetColumns(it => it.DataInterfaceIds == input.dataInterfaceIds)
            .Where(x => x.Id == input.interfaceIdentId)
            .ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("删除数据失败");
        }
    }

    /// <summary>
    ///     授权用户.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpPost("/interfaceOauth/SaveUserList")]
    public async Task SaveUserList([FromBody] InterfaceOauthSaveInput input)
    {
        await _repository.AsSugarClient().Deleteable<DataInterfaceUserEntity>()
            .Where(x => x.OauthId == input.interfaceIdentId && !input.userIds.Contains(x.UserId.Value))
            .ExecuteCommandAsync();
        List<long?>? oldUserIds = await _repository.AsSugarClient().Queryable<DataInterfaceUserEntity>()
            .Where(x => x.OauthId == input.interfaceIdentId).Select(x => x.UserId).ToListAsync();
        List<DataInterfaceUserEntity> dataInterfaceUserList = new();
        foreach (long item in input.userIds)
        {
            if (!oldUserIds.Contains(item))
            {
                dataInterfaceUserList.Add(new DataInterfaceUserEntity
                {
                    UserId = item,
                    UserKey = new Random().NextLetterAndNumberString(16).ToLower(),
                    OauthId = input.interfaceIdentId
                });
            }
        }

        await _repository.AsSugarClient().Insertable(dataInterfaceUserList).ExecuteCommandAsync();
    }
    
    // /// <summary>
    // /// 已授权用户
    // /// </summary>
    // /// <returns></returns>
    // [HttpPost("/interfaceOauth/GetUserList")]
    // public async Task<dynamic> GetUserList([FromBody] UserRelationInput input)
    // {
    //     var data = await _sysUserRep.AsQueryable().Where(it => it.Enable == true)
    //         .Where(it => input.ids.Contains(it.Id.ToString()))
    //         .Select(it => new OrganizeMemberListOutput()
    //         {
    //             id = it.Id.ToString(),
    //             fullName = it.Name,
    //             headIcon = SqlFunc.MergeString("/api/file/Image/userAvatar/", it.Phone),
    //             enabledMark = it.Enable == true ? 1 : 0,
    //             isAdministrator = it.AdminType == AdminTypeEnum.SuperAdmin ? 1 : 0,
    //         }).ToListAsync();
    //
    //     data = data.OrderBy(x => input.ids.IndexOf(x.id)).ToList();
    //     if (data.Any())
    //     {
    //         var orgList = _organizeService.GetOrgListTreeName();
    //
    //         // 获取 所属组织的所有成员
    //         List<UserRelationEntity>? userList = await _repository.AsSugarClient().Queryable<UserRelationEntity>()
    //             .Where(x => x.ObjectType == "Organize" && data.Select(x => x.id).Contains(x.UserId)).ToListAsync();
    //
    //         // 处理组织树
    //         data.ForEach(item =>
    //         {
    //             var oids = userList.Where(x => x.UserId.Equals(item.id)).Select(x => x.ObjectId).ToList();
    //             var oTree = orgList.Where(x => oids.Contains(x.Id)).Select(x => x.Description).ToList();
    //             item.organize = string.Join(",", oTree);
    //         });
    //     }
    //
    //     return new { list = data };
    // }

    #endregion
}