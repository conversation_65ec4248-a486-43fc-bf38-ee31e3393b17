using System.Diagnostics;
using System.Security.Cryptography;
using Common.Core.Manager.DataBase;
using Common.Core.Manager.Files;
using Common.Dto;
using Common.Models;
using Common.Net;
using Common.Security;
using Engine.Entity.Model;
using Extras.DatabaseAccessor.SqlSugar.Options;
using Furion.HttpRemote;
using Furion.Shapeless;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Microsoft.AspNetCore.Hosting.Server;
using Microsoft.AspNetCore.Hosting.Server.Features;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;
using Systems.Entity.Model.System.DataInterFace;
using DateTime = System.DateTime;

namespace Systems.Core;

/// <summary>
///     数据接口
/// </summary>
[ApiDescriptionSettings("数据应用")]
public class DataInterfaceService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<DataInterfaceEntity> _repository;
    private readonly IDataBaseManager _dataBaseManager;
    private readonly IUserManager _userManager;
    private readonly IFileManager _fileManager;
    private readonly SqlSugarScope _sqlSugarClient;
    private readonly SysCacheService _cacheManager;
    private readonly JsScriptEnginePool _enginePool;
    private readonly IHttpRemoteService _httpRemoteService;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private string _configId = App.GetOptions<ConnectionStringsOptions>().DefaultConnectionConfig.ConfigId.ToString();
    private string _dbName = App.GetOptions<ConnectionStringsOptions>().DefaultConnectionConfig.DBName;
    private int currentPage = 1;
    private int pageSize = 20;
    private string keyword = string.Empty;
    private string showKey = string.Empty;
    private string showValue = string.Empty;

    /// <summary>
    ///     初始化一个<see cref="DataInterfaceService" />类型的新实例.
    /// </summary>
    public DataInterfaceService(
        ISqlSugarRepository<DataInterfaceEntity> repository,
        IDataBaseManager dataBaseManager,
        IUserManager userManager,
        SysCacheService cacheManager,
        IFileManager fileManager,
        IServiceScopeFactory serviceScopeFactory,
        ISqlSugarClient context, IHttpRemoteService httpRemoteService, JsScriptEnginePool enginePool)
    {
        _repository = repository;
        _fileManager = fileManager;
        _dataBaseManager = dataBaseManager;
        _userManager = userManager;
        _cacheManager = cacheManager;
        _serviceScopeFactory = serviceScopeFactory;
        _httpRemoteService = httpRemoteService;
        _enginePool = enginePool;
        _sqlSugarClient = (SqlSugarScope)context;
    }

    #region Get

    /// <summary>
    ///     获取接口列表(分页).
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpGet("/dataInterface/page")]
    public async Task<dynamic> GetPage([FromQuery] DataInterfaceListQuery input)
    {
        SqlSugarPagedList<DataInterfaceListOutput> list = await _repository.AsSugarClient()
            .Queryable<DataInterfaceEntity>()
            .WhereIF(input.CategoryId > 0, a => a.CategoryId == input.CategoryId)
            .WhereIF(!string.IsNullOrEmpty(input.Type), a => input.Type.Contains(a.Type.ToString()))
            .WhereIF(input.EnabledMark.IsNotEmptyOrNull(), a => a.EnabledMark.Equals(input.EnabledMark))
            .WhereIF(!string.IsNullOrEmpty(input.keyword),
                a => a.FullName.Contains(input.keyword) || a.EnCode.Contains(input.keyword))
            .OrderBy(a => a.SortCode).OrderBy(a => a.CreatedTime, OrderByType.Desc)
            .Select(a => new DataInterfaceListOutput
            {
                id = a.Id,
                fullName = a.FullName,
                enCode = a.EnCode,
                type = SqlFunc.IF(a.Type == 1).Return("SQL操作").ElseIF(a.Type == 2).Return("静态数据").End("API操作"),
                creatorTime = a.CreatedTime,
                creatorUser = SqlFunc.Subqueryable<SysUser>().Where(u => u.Id == a.CreatedUserId)
                    .Select(u => u.Name),
                sortCode = a.SortCode,
                enabledMark = a.EnabledMark,
                isPostPosition = a.IsPostposition,
                hasPage = a.HasPage,
                tenantId = _userManager.TenantId
            }).ToPagedListAsync(input.currentPage, input.pageSize);
        return list;
    }

    /// <summary>
    ///     获取接口列表(分页).
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpGet("/dataInterface/getList")]
    public async Task<dynamic> GetList([FromQuery] DataInterfaceListQuery input)
    {
        SqlSugarPagedList<DataInterfaceListOutput> list = await _repository.AsSugarClient()
            .Queryable<DataInterfaceEntity>()
            .Where(a => a.EnabledMark == 1 && a.IsPostposition == 0)
            .WhereIF(input.HasPage == 0, a => a.HasPage == input.HasPage)
            .WhereIF(input.CategoryId > 0, a => a.CategoryId == input.CategoryId)
            .WhereIF(!string.IsNullOrEmpty(input.Type), a => input.Type.Contains(a.Type.ToString()))
            .WhereIF(!string.IsNullOrEmpty(input.keyword),
                a => a.FullName.Contains(input.keyword) || a.EnCode.Contains(input.keyword))
            .OrderBy(a => a.SortCode).OrderBy(a => a.CreatedTime, OrderByType.Desc)
            .Select(a => new DataInterfaceListOutput
            {
                id = a.Id,
                fullName = a.FullName,
                enCode = a.EnCode,
                type = SqlFunc.IF(a.Type == 1).Return("SQL操作").ElseIF(a.Type == 2).Return("静态数据").End("API操作"),
                creatorTime = a.CreatedTime,
                creatorUser = SqlFunc.Subqueryable<SysUser>().Where(u => u.Id == a.CreatedUserId)
                    .Select(u => u.Name),
                sortCode = a.SortCode,
                enabledMark = a.EnabledMark,
                tenantId = _userManager.TenantId,
                isPostPosition = a.IsPostposition,
                parameterJson = a.ParameterJson,
                hasPage = a.HasPage,
                fieldJson = a.FieldJson
            }).ToPagedListAsync(input.currentPage, input.pageSize);
        return list;
    }

    /// <summary>
    ///     获取接口列表下拉框.
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataInterface/selector")]
    public async Task<dynamic> GetSelector()
    {
        List<DataInterfaceSelectorOutput> tree = new();
        List<DataInterfaceSelectorOutput>? entityList = await _repository.AsQueryable().Where(x => x.EnabledMark == 1 && x.IsPostposition == 0)
            .OrderBy(x => x.SortCode)
            .Select(a => new DataInterfaceSelectorOutput
            {
                id = a.Id.ToString(),
                categoryId = "1",
                fullName = a.FullName,
                parentId = a.CategoryId.ToString()
            }).ToListAsync();
        List<string> pidList = entityList.Select(it => it.parentId).ToList();
        tree = await _repository.AsSugarClient().Queryable<DataInterfaceCategory>().Where(d => pidList.Contains(d.Id.ToString()))
            .Select(a => new DataInterfaceSelectorOutput
            {
                id = a.Id.ToString(),
                categoryId = "0",
                fullName = a.Name,
                parentId = "0"
            }).ToListAsync();

        return tree.Union(entityList).ToList().ToTree();
    }

    /// <summary>
    ///     获取接口数据.
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataInterface/{id}")]
    public async Task<dynamic> GetInfoApi(long id)
    {
        return (await GetInfo(id)).Adapt<DataInterfaceInput>();
    }

    /// <summary>
    ///     获取预览参数.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("/dataInterface/GetParam/{id}")]
    [UnitOfWork]
    public async Task<dynamic> GetParam(long id)
    {
        DataInterfaceEntity info = await GetInfo(id);
        if (info.IsNotEmptyOrNull() && info.ParameterJson.IsNotEmptyOrNull())
        {
            return info.ParameterJson.ToObjectOld<List<DataInterfaceReqParameter>>();
        }

        return new List<DataInterfaceReqParameter>();
    }

    /// <summary>
    ///     导出.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("/dataInterface/{id}/Actions/Export")]
    public async Task<dynamic> ActionsExport(long id)
    {
        DataInterfaceEntity data = await GetInfo(id);
        if (data == null)
        {
            throw Oops.Oh("数据接口已经被删除！");
        }

        return data;
        // string? jsonStr = data.ToJson();
        // return await _fileManager.Export(jsonStr, data.FullName, ExportFileType.bd);
    }

    #endregion

    #region Post

    /// <summary>
    ///     调试接口
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataInterface/{id}/Actions/Debug")]
    [UnitOfWork]
    [UnifySerializerSetting("datainterfaceSpecial")]
    public async Task<dynamic> Debug(long id, [FromBody] DataInterfaceDebugInput input)
    {
        _configId = _userManager.TenantId.ToString();
        _dbName = _userManager.TenantDbName;
        GetDataInterfaceParameter(input);
        return await GetDataInterfaceData(id, input, 3, script: input.script);
    }

    /// <summary>
    ///     数据连接-动态执行SQL
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dataInterface/Actions/DynamicQuery")]
    [UnitOfWork]
    [UnifySerializerSetting("datainterfaceSpecial")]
    public async Task<dynamic> DataInterfaceActionsDynamicQuery([FromBody] DataInterfaceActionsDynamicQueryInput input)
    {
        DbLink? entity = await _repository.AsSugarClient().CopyNew().Queryable<DbLink>().FirstAsync(x => x.Id == input.DbLinkId);
        if (entity == null)
        {
            throw Oops.Oh("数据源不存在！");
        }

        List<SugarParameter> parameter = input.ParamList.ToObjectOld<List<DataInterfaceReqParameter>>()
            .Select(x => new SugarParameter("@" + x.field, GetSugarParameterList(x))).ToList();
        string sql = GetSqlParameter(input.Sql, parameter);
        // 拼接传递参数  todo
        sql = await GetDataTableWithCondition(sql, parameter);
        return _dataBaseManager.GetSqlData(entity, sql, true, parameter.ToArray());
    }


    /// <summary>
    ///     预览接口.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataInterface/{id}/Actions/Preview")]
    [UnitOfWork]
    [UnifySerializerSetting("datainterfaceSpecial")]
    public async Task<dynamic> Preview(long id, [FromBody] DataInterfacePreviewInput input)
    {
        _configId = _userManager.TenantId.ToString();
        _dbName = _userManager.TenantDbName;
        GetDataInterfaceParameter(input);
        return await GetDataInterfaceData(id, input, 3);
    }

    /// <summary>
    ///     访问接口 选中 回写.
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dataInterface/{id}/Actions/InfoByIds")]
    [UnifySerializerSetting("datainterfaceSpecial")]
    [AllowAnonymous]
    public async Task<dynamic> ActionsResponseInfo(long id, [FromBody] DataInterfacePreviewInput input)
    {
        _configId = _userManager.TenantId.ToString();
        _dbName = _userManager.TenantDbName;
        bool isEcho = await _repository.IsAnyAsync(x => x.Id == id && x.HasPage == 1);
        GetDataInterfaceParameter(input);
        List<object> output = new();
        keyword = input.keyword;
        showKey = input.propsValue;
        input.dicParameters.Add("@showKey", input.propsValue);
        if (isEcho)
        {
            foreach (string item in input.ids)
            {
                showValue = item;
                input.dicParameters["@showValue"] = item;
                object data = await GetDataInterfaceData(id, input, 1, isEcho);
                if (data.IsNotEmptyOrNull())
                {
                    output.Add(data);
                }
            }
        }
        else
        {
            return await GetDataInterfaceData(id, input, 1, isEcho);
        }

        return output;
    }

    /// <summary>
    ///     访问接口 分页.
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dataInterface/{id}/Actions/List")]
    [UnifySerializerSetting("datainterfaceSpecial")]
    [AllowAnonymous]
    public async Task<dynamic> ActionsResponseList(long id, [FromBody] DataInterfacePreviewInput input)
    {
        _configId = _userManager.TenantId.ToString();
        _dbName = _userManager.TenantDbName;
        currentPage = input.currentPage;
        pageSize = input.pageSize;
        keyword = input.keyword;
        GetDataInterfaceParameter(input);
        return await GetDataInterfaceData(id, input, 0);
    }

    /// <summary>
    ///     外部访问接口.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="tenantId">有值则为地址请求，没有则是内部请求.</param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/dataInterface/{id}/Actions/Response")]
    [UnitOfWork]
    [UnifySerializerSetting("datainterfaceSpecial")]
    public async Task<dynamic> ActionsResponse(long id, [FromQuery] string tenantId, [FromBody] Dictionary<string, string> dic)
    {
        return await InterfaceVerify(id, tenantId, dic);
    }

    /// <summary>
    ///     添加接口.
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/dataInterface/add")]
    public async Task Create([FromBody] DataInterfaceInput input)
    {
        if (await _repository.IsAnyAsync(x =>
                x.EnCode == input.enCode || x.FullName == input.fullName))
        {
            throw Oops.Oh("已存在同名或同编码数据");
        }

        DataInterfaceEntity entity = input.Adapt<DataInterfaceEntity>();
        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh("新增数据失败");
        }
    }

    /// <summary>
    ///     修改接口.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/dataInterface/{id}/update")]
    public async Task Update(long id, [FromBody] DataInterfaceInput input)
    {
        if (await _repository.IsAnyAsync(x =>
                x.Id != id && (x.EnCode == input.enCode || x.FullName == input.fullName)))
        {
            throw Oops.Oh("已存在同名或同编码数据");
        }

        DataInterfaceEntity entity = input.Adapt<DataInterfaceEntity>();
        bool isOk = await _repository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("修改数据失败");
        }
    }

    /// <summary>
    ///     删除接口.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpPost("/dataInterface/{id}/delete")]
    public async Task Delete(long id)
    {
        bool isOk = await _repository.AsDeleteable().Where(it => it.Id == id).ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("删除数据失败");
        }
    }

    /// <summary>
    ///     更新接口状态.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpPost("/dataInterface/{id}/Actions/State")]
    public async Task UpdateState(long id)
    {
        bool isOk = await _repository.AsUpdateable().SetColumns(it => new DataInterfaceEntity
        {
            EnabledMark = SqlFunc.IIF(it.EnabledMark == 1, 0, 1),
            UpdatedTime = DateTime.Now,
            UpdatedUserId = _userManager.UserId
        }).Where(it => it.Id == id).ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("修改状态失败");
        }
    }

    /// <summary>
    ///     导入.
    /// </summary>
    /// <param name="file"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    [HttpPost("/dataInterface/Actions/Import")]
    public async Task ActionsImport(IFormFile file, int type)
    {
        string fileType = Path.GetExtension(file.FileName).Replace(".", string.Empty);
        if (!fileType.ToLower().Equals(ExportFileType.json.ToString()))
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        string josn = _fileManager.Import(file);
        DataInterfaceEntity? data;
        try
        {
            data = josn.ToObjectOld<DataInterfaceEntity>();
        }
        catch
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        if (data == null)
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        List<string> errorMsgList = new();
        List<string> errorList = new();
        if (await _repository.AsQueryable().AnyAsync(it => it.Id.Equals(data.Id)))
        {
            errorList.Add("ID");
        }

        if (await _repository.AsQueryable().AnyAsync(it => it.EnCode.Equals(data.EnCode)))
        {
            errorList.Add("编码");
        }

        if (await _repository.AsQueryable().AnyAsync(it => it.FullName.Equals(data.FullName)))
        {
            errorList.Add("名称");
        }

        if (errorList.Any())
        {
            if (type.Equals(0))
            {
                string error = string.Join("、", errorList);
                errorMsgList.Add(string.Format("{0}重复", error));
            }
            else
            {
                string? random = new Random().NextLetterAndNumberString(5);
                data.Id = YitIdHelper.NextId();
                data.FullName = string.Format("{0}.副本{1}", data.FullName, random);
                data.EnCode += random;
            }
        }

        if (errorMsgList.Any() && type.Equals(0))
        {
            throw Oops.Oh(ErrorCode.COM1018, string.Join(";", errorMsgList));
        }

        data.EnabledMark = 0;
        data.CreatedUserId = _userManager.UserId;

        try
        {
            StorageableResult<DataInterfaceEntity>? storModuleModel =
                await _repository.AsSugarClient().Storageable(data).Saveable().ToStorageAsync(); // 存在更新不存在插入 根据主键
            await storModuleModel.AsInsertable.ExecuteCommandAsync(); // 执行插入
            await storModuleModel.AsUpdateable.ExecuteCommandAsync(); // 执行更新
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ErrorCode.COM1020, ex.Message);
        }
    }

    /// <summary>
    ///     外部接口授权码.
    /// </summary>
    /// <param name="appId"></param>
    /// <param name="intefaceId"></param>
    /// <param name="dic"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("/dataInterface/Actions/GetAuth")]
    public async Task<dynamic> GetAuthorization([FromQuery] string appId, [FromQuery] string tenantId,
        [FromQuery] long intefaceId, [FromBody] Dictionary<string, string> dic)
    {
        InterfaceOauthEntity? interfaceOauthEntity = await _sqlSugarClient.Queryable<InterfaceOauthEntity>()
            .FirstAsync(x => x.AppId == appId && x.EnabledMark == 1);
        if (interfaceOauthEntity == null)
        {
            return null;
        }

        string ymDate = DateTime.Now.ParseToUnixTime().ToString();
        string authorization = GetVerifySignature(interfaceOauthEntity, intefaceId, ymDate);
        return new
        {
            YmDate = ymDate,
            Authorization = authorization
        };
    }

    /// <summary>
    ///     复制.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpPost("/dataInterface/{id}/Actions/Copy")]
    public async Task ActionsCopy(long id)
    {
        DataInterfaceEntity? entity = await _repository.GetFirstAsync(x => x.Id == id);
        if (entity == null)
        {
            throw Oops.Oh("检测数据不存在");
        }

        string random = new Random().NextLetterAndNumberString(5).ToLower();
        entity.FullName = $"{entity.FullName}.副本{random}";
        entity.EnCode = $"{entity.EnCode}{random}";
        if (entity.FullName.Length >= 50 || entity.EnCode.Length >= 50)
        {
            throw Oops.Oh("已到达该模板复制上限，请复制源模板");
        }

        entity.EnabledMark = 0;
        entity.Id = YitIdHelper.NextId();
        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh("新增数据失败");
        }
    }

    #endregion

    #region Public

    /// <summary>
    ///     远端数据.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="type"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<object> GetResponseByType(long id, int type, DataInterfacePreviewInput input)
    {
        bool isEcho = await _repository.IsAnyAsync(x => x.Id == id && x.HasPage == 1) && type == 1;
        if (input.IsNotEmptyOrNull())
        {
            currentPage = input.currentPage;
            pageSize = input.pageSize;
            keyword = input.keyword.IsNotEmptyOrNull() ? input.keyword : string.Empty;
        }

        GetDataInterfaceParameter(input);
        return await GetDataInterfaceData(id, input, type, isEcho);
    }

    /// <summary>
    ///     信息.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [NonAction]
    public async Task<DataInterfaceEntity> GetInfo(long id)
    {
        return await _repository.GetFirstAsync(x => x.Id == id);
    }

    /// <summary>
    ///     处理远端数据.
    /// </summary>
    /// <param name="cacheKey"></param>
    /// <param name="propsUrl">远端数据ID.</param>
    /// <param name="value">指定选项标签为选项对象的某个属性值.</param>
    /// <param name="label">指定选项的值为选项对象的某个属性值.</param>
    /// <param name="children">指定选项的子选项为选项对象的某个属性值.</param>
    /// <param name="linkageParameters">联动参数.</param>
    /// <returns></returns>
    [NonAction]
    public async Task<List<StaticDataModel>> GetDynamicList(string cacheKey, long propsUrl, string value, string label, string children, List<DataInterfaceParameter> linkageParameters = null)
    {
        List<StaticDataModel> list = new();

        // 获取远端数据
        DataInterfaceEntity? dynamic = await _repository.AsQueryable().Where(x => x.Id == propsUrl).FirstAsync();
        if (dynamic == null)
        {
            return list;
        }

        // 控件联动 不能缓存
        if (linkageParameters == null)
        {
            list = await GetDynamicDataCache(cacheKey, dynamic.Id);
        }

        if (list == null || list.Count == 0)
        {
            list = new List<StaticDataModel>();

            _configId = _userManager.TenantId.ToString();
            _dbName = _userManager.TenantDbName;
            DataInterfacePreviewInput input = new() { paramList = linkageParameters };
            if (linkageParameters == null)
            {
                input.paramList = new List<DataInterfaceParameter>();
            }

            GetDataInterfaceParameter(input);
            //var dicParameters = linkageParameters != null && linkageParameters.Any() ? linkageParameters.ToDictionary(x => x.ParameterName, y => y.FormFieldValues) : new Dictionary<string, string>();
            object interfaceData = await GetDataInterfaceData(propsUrl, input, 3);

            // 数据处理结果
            string? dataProcessingResults = interfaceData.IsNullOrEmpty() ? string.Empty : interfaceData.ToJson();

            if (!dataProcessingResults.IsNullOrEmpty())
            {
                if (dynamic.HasPage.Equals(1) && dataProcessingResults.IsNotEmptyOrNull() && !dataProcessingResults.FirstOrDefault().Equals('['))
                {
                    SqlSugarPagedList<Dictionary<string, object>>? realList = dataProcessingResults.ToObjectOld<SqlSugarPagedList<Dictionary<string, object>>>();
                    dataProcessingResults = realList.Rows.ToJson();
                }

                foreach (JToken? item in JToken.Parse(dataProcessingResults))
                {
                    StaticDataModel dynamicDic = new()
                    {
                        id = item.Value<string>(value),
                        fullName = item.Value<string>(label)
                    };
                    list.Add(dynamicDic);

                    // 为避免子级有数据.
                    if (item.Value<object>(children) != null && item.Value<object>(children).ToString().IsNotEmptyOrNull())
                    {
                        list.AddRange(GetDynamicInfiniteData(item.Value<object>(children).ToString(), value, label, children));
                    }
                }

                await SetDynamicDataCache(cacheKey, dynamic.Id, list);
            }
        }

        return list;
    }

    #endregion

    #region Private

    /// <summary>
    ///     处理数据接口参数.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    private Dictionary<string, string> GetDataInterfaceParameter(DataInterfacePreviewInput input)
    {
        dynamic? clay = input.sourceData.IsNotEmptyOrNull() ? Clay.Parse(input.sourceData.ToJson()) : null;
        Dictionary<string, string> pairs = new();
        if (input.paramList == null)
        {
            return pairs;
        }

        foreach (DataInterfaceParameter item in input.paramList)
        {
            switch (item.sourceType)
            {
                case 1:
                    if (item.relationField.IsNotEmptyOrNull() && clay != null)
                    {
                        if (item.isSubTable || item.relationField.ToString().IsMatch("^tableField\\d{3}"))
                        {
                            List<string> fields = item.relationField.ToString().Split("-").ToList();
                            // 子表键值
                            string tableField = fields[0];
                            // 子表字段键值
                            string keyField = fields[1];
                            if (clay.IsDefined(tableField))
                            {
                                // 获取子表全部数据
                                dynamic? subTabelData = clay[tableField];

                                // 子表数据转粘土对象
                                dynamic? subTableClay = Clay.Parse(subTabelData.ToString());
                                List<Dictionary<string, object>> dataCount = subTableClay.Deserialize<List<Dictionary<string, object>>>();
                                if (dataCount.Count > 0)
                                {
                                    // 粘土对象转数组/集合
                                    IEnumerable<dynamic> subTableList = subTableClay.AsEnumerator<dynamic>();

                                    // 取子表第一条数据
                                    dynamic? subTable = subTableList.FirstOrDefault();

                                    // 子表行转换成 dictionary
                                    Dictionary<string, object> subTableDic = subTable.ToDictionary();

                                    item.defaultValue = subTableDic[keyField];
                                }
                            }
                        }
                        else
                        {
                            if (clay.IsDefined(item.relationField.ToString()))
                            {
                                item.defaultValue = clay[item.relationField.ToString()];
                            }
                        }
                    }

                    break;
                case 2:
                    if (item.relationField.IsNotEmptyOrNull())
                    {
                        item.defaultValue = item.relationField;
                    }

                    break;
                case 3:
                    item.defaultValue = string.Empty;
                    break;
                case 4:
                    if (input.systemParamter.Keys.Contains(item.relationField.ToString()))
                    {
                        item.defaultValue = input.systemParamter[item.relationField.ToString()];
                    }
                    else
                    {
                        switch (item.relationField.ToString())
                        {
                            case "@userId":
                            case "@flowOperatorUserId":
                                item.defaultValue = _userManager.UserId;
                                break;
                            case "@flowOperatorUserName":
                                item.defaultValue = _userManager.User.Name;
                                break;
                            // case "@userAndSubordinates":
                            //     item.defaultValue = _userManager.CurrentUserAndSubordinates;
                            //     break;
                            case "@organizeId":
                                item.defaultValue = _userManager?.User?.OrgId;
                                break;
                                // case "@organizationAndSuborganization":
                                //     item.defaultValue = _userManager.CurrentOrganizationAndSubOrganizations;
                                //     break;
                                // case "@branchManageOrganize":
                                //     var chargeorganization = _userManager.DataScope.Select(x => x.organizeId).ToList();
                                //     if (_userManager.IsAdministrator)
                                //     {
                                //         chargeorganization = _repository.AsSugarClient().Queryable<OrganizeEntity>().Where(x => x.DeleteMark == null && x.EnabledMark == 1).Select(x => x.Id).ToList();
                                //     }
                                //
                                //     item.defaultValue = chargeorganization;
                                //     break;
                        }
                    }

                    break;
                default:
                    if (item.relationField.IsNotEmptyOrNull())
                    {
                        item.defaultValue = item.relationField;
                    }

                    break;
            }

            if (item.defaultValue.ToJson().FirstOrDefault().Equals('['))
            {
                pairs[item.field] = item.defaultValue.ToJson().Trim('"');
            }
            else
            {
                pairs[item.field] = item.defaultValue?.ToString();
            }
        }

        if (pairs.Any())
        {
            input.dicParameters = pairs;
        }

        return pairs;
    }

    /// <summary>
    ///     获取数据接口数据.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <param name="type"></param>
    /// <param name="isEcho"></param>
    /// <param name="script"></param>
    /// <returns></returns>
    private async Task<object> GetDataInterfaceData(long id, DataInterfacePreviewInput input, int type, bool isEcho = false, string? script = null)
    {
        object output = null;
        DataInterfaceEntity info = await GetInfo(id);
        ReplaceSqlParameter(info, input.dicParameters);
        VerifyRequired(info, input.dicParameters);
        if (info.Type == 1)
        {
            output = await GetSqlData(info, isEcho);
            // 预览更新变量值.
            if (info.IsPostposition == 1)
            {
                List<DataInterfaceVariateEntity>? variateList = await _repository.AsSugarClient().Queryable<DataInterfaceVariateEntity>().Where(x => x.InterfaceId == info.Id).ToListAsync();
                foreach (DataInterfaceVariateEntity? item in variateList)
                {
                    object value = await _enginePool.ExecuteScriptAsync("interface", item.Expression, output);
                    item.Value = value.ToJson().Trim('"');
                }

                await _repository.AsSugarClient().Updateable(variateList).ExecuteCommandAsync();
            }
        }
        else if (info.Type == 2)
        {
            output = info.DataConfigJson.ToObjectOld<DataInterfaceProperJson>().staticData.ToObjectOld<object>();
            // 预览更新变量值.
            if (info.IsPostposition == 1)
            {
                List<DataInterfaceVariateEntity>? variateList = await _repository.AsSugarClient().Queryable<DataInterfaceVariateEntity>().Where(x => x.InterfaceId == info.Id).ToListAsync();
                foreach (DataInterfaceVariateEntity? item in variateList)
                {
                    object value = await _enginePool.ExecuteScriptAsync("interface", item.Expression, output);
                    item.Value = value.ToJson().Trim('"');
                }

                await _repository.AsSugarClient().Updateable(variateList).ExecuteCommandAsync();
            }
        }
        else
        {
            output = await GetApiData(info, input.dicParameters, type, isEcho);
        }

        if (info.DataJsJson.IsNotEmptyOrNull() && output.IsNotEmptyOrNull())
        {
            // 1. 单次执行
            Dictionary<string, object> dictionary = new();
            foreach (KeyValuePair<string, string> parameter in input.dicParameters)
            {
                dictionary.Add(parameter.Key, parameter.Value);
            }

            output = await _enginePool.ExecuteScriptAsync(
                "interface", // 脚本标识，用于日志
                script != null ? script : info.DataJsJson, // 脚本内容
                output, // 主数据
                dictionary
            );
        }

        // 自定义数据过滤
        if (input.ruleList.IsNotEmptyOrNull() && input.ruleList.conditionList.Any())
        {
            output = AssembleItemRule(output, input.ruleList.conditionList.Copy());
        }

        if (info.HasPage == 0)
        {
            // 假分页数据处理.
            output = InterfaceDataManage(output, type, input);
        }

        return output;
    }

    /// <summary>
    ///     获取数据过滤条件.
    /// </summary>
    /// <param name="interfaceData"></param>
    /// <param name="model"></param>
    /// <returns></returns>
    private object GetItemRule(object interfaceData, DataFilteringConditionGroupsListModel model)
    {
        List<ConditionalModel> conModels = new();
        List<string>? between = new();
        List<Dictionary<string, object>>? result = new();
        if (interfaceData.ToJson().FirstOrDefault().Equals('['))
        {
            result = interfaceData.ToObjectOld<List<Dictionary<string, object>>>();
        }
        else
        {
            Dictionary<string, object>? dic = interfaceData.ToObjectOld<Dictionary<string, object>>();
            result = dic.IsNotEmptyOrNull() && dic.ContainsKey("list") ? dic["list"].ToObjectOld<List<Dictionary<string, object>>>() : new List<Dictionary<string, object>>();
        }

        if (model.fieldValue.IsNotEmptyOrNull())
        {
            if (model.symbol.Equals("between"))
            {
                between = model.fieldValue.ToObjectOld<List<string>>();
            }

            switch (model.jnpfKey)
            {
                case JnpfKeyConst.COMSELECT:
                case JnpfKeyConst.CURRORGANIZE:
                    if ((model.fieldValue.IsNotEmptyOrNull() && model.symbol.Equals("==")) || model.symbol.Equals("<>"))
                    {
                        model.fieldValue = model.fieldValue.ToString().Replace("\r\n", "").Replace(" ", "");
                    }
                    else
                    {
                        if (model.fieldValue.ToString().Replace("\r\n", "").Replace(" ", "").Contains("[["))
                        {
                            model.fieldValue = model.fieldValue.ToObjectOld<List<List<string>>>().Select(x => x.Last() + "\"]").ToList();
                        }
                        else if (model.fieldValue.ToString().Replace("\r\n", "").Replace(" ", "").Contains("["))
                        {
                            model.fieldValue = model.fieldValue.ToObjectOld<List<string>>().Select(x => x + "\"]").ToList();
                        }
                    }

                    break;
                case JnpfKeyConst.CREATETIME:
                case JnpfKeyConst.MODIFYTIME:
                case JnpfKeyConst.DATE:
                    {
                        if (model.symbol.Equals("between"))
                        {
                            DateTime startTime = between.First().ParseToDateTime();
                            DateTime endTime = between.Last().ParseToDateTime();
                            between[0] = startTime.ToString();
                            between[1] = endTime.ToString();
                        }
                        else
                        {
                            model.fieldValue = model.fieldValue.ToString().ParseToDateTime();
                        }
                    }
                    break;
                case JnpfKeyConst.TIME:
                    {
                        if (!model.symbol.Equals("between"))
                        {
                            model.fieldValue = string.Format("{0:" + model.format + "}", Convert.ToDateTime(model.fieldValue));
                        }
                    }
                    break;
                case JnpfKeyConst.CASCADER:
                case JnpfKeyConst.ADDRESS:
                    if (model.fieldValue.IsNotEmptyOrNull() && (model.symbol.Equals("==") || model.symbol.Equals("<>")))
                    {
                        model.fieldValue = model.fieldValue.ToString().Replace("\r\n", "").Replace(" ", "");
                    }

                    break;
            }
        }

        switch (model.symbol)
        {
            case ">=":
                result = result.Where(x => Convert.ToDecimal(x[model.field]) >= Convert.ToDecimal(model.fieldValue)).ToList();
                break;
            case ">":
                result = result.Where(x => Convert.ToDecimal(x[model.field]) > Convert.ToDecimal(model.fieldValue)).ToList();
                break;
            case "==":
                result = result.Where(x => x[model.field] != null && x[model.field].Equals(model.fieldValue)).ToList();
                break;
            case "<=":
                result = result.Where(x => Convert.ToDecimal(x[model.field]) <= Convert.ToDecimal(model.fieldValue)).ToList();
                break;
            case "<":
                result = result.Where(x => Convert.ToDecimal(x[model.field]) < Convert.ToDecimal(model.fieldValue)).ToList();
                break;
            case "<>":
                result = result.Where(x => x[model.field] == null || !x[model.field].Equals(model.fieldValue)).ToList();
                break;
            case "like":
                if (model.fieldValue != null && model.fieldValue.ToString().Contains("["))
                {
                    model.fieldValue = model.fieldValue.ToString().Replace("[", string.Empty).Replace("]", string.Empty);
                }

                result = result.Where(x => x[model.field].ToString()!.Contains(model.fieldValue?.ToString())).ToList();
                break;
            case "notLike":
                if (model.fieldValue != null && model.fieldValue.ToString().Contains("["))
                {
                    model.fieldValue = model.fieldValue.ToString().Replace("[", string.Empty).Replace("]", string.Empty);
                }

                result = result.Where(x => !x[model.field].ToString()!.Contains(model.fieldValue?.ToString())).ToList();
                break;
            case "in":
            case "notIn":
                if (model.fieldValue != null && model.fieldValue.ToString().Contains("["))
                {
                    List<string>? ids = new();
                    if (model.fieldValue.ToString().Replace("\r\n", "").Replace(" ", "").Contains("[["))
                    {
                        ids = model.fieldValue.ToObjectOld<List<List<string>>>().Select(x => x.Last()).ToList();
                    }
                    else
                    {
                        ids = model.fieldValue.ToObjectOld<List<string>>();
                    }

                    if (model.symbol.Equals("notIn"))
                    {
                        model.field = string.Format("{0}--And", model.field);
                        result = result.Where(x => !x[model.field].ToString()!.Contains(model.fieldValue?.ToString())).ToList();
                        conModels.Add(new ConditionalModel
                        {
                            FieldName = model.field,
                            ConditionalType = ConditionalType.IsNot,
                            FieldValue = null
                        });
                        conModels.Add(new ConditionalModel
                        {
                            FieldName = model.field,
                            ConditionalType = ConditionalType.IsNot,
                            FieldValue = string.Empty
                        });
                    }

                    for (int i = 0; i < ids.Count; i++)
                    {
                        string it = ids[i];
                        conModels.Add(new ConditionalModel
                        {
                            FieldName = model.field,
                            ConditionalType = model.symbol.Equals("in") ? model.jnpfKey.Equals(JnpfKeyConst.TREESELECT) ? ConditionalType.Equal : ConditionalType.Like
                                : model.jnpfKey.Equals(JnpfKeyConst.TREESELECT) ? ConditionalType.NoEqual : ConditionalType.NoLike,
                            FieldValue = it
                        });
                    }
                }

                return conModels;
            case "null":
                if (model.jnpfKey != null && (model.jnpfKey.Equals(JnpfKeyConst.CALCULATE) || model.jnpfKey.Equals(JnpfKeyConst.NUMINPUT) || model.jnpfKey.Equals(JnpfKeyConst.RATE) ||
                                              model.jnpfKey.Equals(JnpfKeyConst.SLIDER)))
                {
                    result = result.Where(x => x[model.field].Equals(null)).ToList();
                }
                else
                {
                    result = result.Where(x => x[model.field].Equals(null) || x[model.field].Equals(string.Empty)).ToList();
                }

                break;
            case "notNull":
                result = result.Where(x => !x[model.field].Equals(null)).ToList();
                if (model.fieldValue.IsNullOrEmpty())
                {
                    model.fieldValue = null;
                }

                break;
            case "between":
                result = result.Where(x => Convert.ToDateTime(x[model.field]) >= Convert.ToDateTime(between.First())
                                           && Convert.ToDateTime(x[model.field]) <= Convert.ToDateTime(between.Last())).ToList();
                break;
        }

        return result;
    }

    /// <summary>
    ///     组装数据过滤条件.
    /// </summary>
    /// <param name="interfaceData"></param>
    /// <param name="conditionList"></param>
    /// <returns></returns>
    private object AssembleItemRule(object interfaceData, List<DataFilteringConditionListModel> conditionList)
    {
        conditionList.ForEach(item => { item.groups.ForEach(con => { interfaceData = GetItemRule(interfaceData, con); }); });
        return interfaceData;
    }

    /// <summary>
    ///     未开启分页数据处理.
    /// </summary>
    /// <param name="interfaceData"></param>
    /// <param name="type"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    private object InterfaceDataManage(object interfaceData, int type, DataInterfacePreviewInput input)
    {
        object? output = new();
        List<Dictionary<string, object>>? result = new();
        if (type != 3)
        {
            if (interfaceData.ToJson().FirstOrDefault().Equals('['))
            {
                result = interfaceData.ToObjectOld<List<Dictionary<string, object>>>();
            }
            else
            {
                Dictionary<string, object>? dic = interfaceData.ToObjectOld<Dictionary<string, object>>();
                result = dic.IsNotEmptyOrNull() && dic.ContainsKey("list") ? dic["list"].ToObjectOld<List<Dictionary<string, object>>>() : new List<Dictionary<string, object>>();
            }
        }

        switch (type)
        {
            case 0:
                if (input.IsNotEmptyOrNull())
                {
                    // 模糊搜索.
                    if (input.keyword.IsNotEmptyOrNull())
                    {
                        result = result.FindAll(r =>
                            r.WhereIF(input.columnOptions.IsNotEmptyOrNull(), x => input.columnOptions.Contains(x.Key)).Where(x => x.Value != null && x.Value.ToString().Contains(input.keyword))
                                .Any());
                    }

                    // 精准搜索.
                    if (input.queryJson.IsNotEmptyOrNull() || input.sidx.IsNotEmptyOrNull())
                    {
                        if (input.queryJson.IsNotEmptyOrNull())
                        {
                            foreach (KeyValuePair<string, string> item in input.queryJson.ToObjectOld<Dictionary<string, string>>())
                            {
                                if (item.Key.Contains("jnpf_searchType_equals_"))
                                {
                                    result = result.Where(x => x[item.Key.Replace("jnpf_searchType_equals_", "")].Equals(item.Value)).ToList();
                                }
                                else
                                {
                                    result = result.Where(x => x[item.Key].ToJson().Contains(item.Value)).ToList();
                                }
                            }
                        }

                        if (input.sidx.IsNotEmptyOrNull())
                        {
                            if (input.sort.Equals("desc"))
                            {
                                result = result.OrderBy(x => x[input.sidx]).ToList();
                            }
                            else
                            {
                                result = result.OrderByDescending(x => x[input.sidx]).ToList();
                            }
                        }
                    }
                }

                output = new
                {
                    pagination = new
                    {
                        input.currentPage,
                        input.pageSize,
                        total = result.Count
                    },
                    list = result.Skip((input.currentPage - 1) * input.pageSize).Take(input.pageSize).ToList()
                };
                break;
            case 1:
                if (input.id != null)
                {
                    output = result.FirstOrDefault(x => x[input.propsValue].ToString().Equals(input.id));
                }
                else if (input.id == null && input.ids.Count > 0)
                {
                    List<Dictionary<string, object>> res = new();
                    foreach (string item in input.ids)
                    {
                        Dictionary<string, object>? firstData = result.FirstOrDefault(x => x[input.propsValue].ToString().Equals(item));
                        if (firstData != null)
                        {
                            res.Add(firstData);
                        }
                    }

                    output = res;
                }

                break;
            default:
                output = interfaceData;
                break;
        }

        return output;
    }

    /// <summary>
    ///     验证必填参数.
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="dicParams"></param>
    private void VerifyRequired(DataInterfaceEntity entity, Dictionary<string, string> dicParams)
    {
        try
        {
            if (entity.IsNotEmptyOrNull() && entity.ParameterJson.IsNotEmptyOrNull() && !entity.ParameterJson.Equals("[]"))
            {
                List<DataInterfaceReqParameter> reqParams = entity.ParameterJson.ToObjectOld<List<DataInterfaceReqParameter>>();
                if (reqParams.Count > 0)
                {
                    // 必填参数
                    List<DataInterfaceReqParameter> requiredParams = reqParams.Where(x => x.required == "1").ToList();
                    if (requiredParams.Any() && (dicParams.IsNullOrEmpty() || dicParams.Keys.Count == 0))
                    {
                        throw Oops.Oh("必要参数不能为空");
                    }

                    foreach (DataInterfaceReqParameter item in requiredParams)
                    {
                        if (dicParams.ContainsKey(item.field))
                        {
                            switch (item.dataType)
                            {
                                case "varchar":
                                    if (dicParams[item.field].IsNullOrEmpty())
                                    {
                                        throw Oops.Oh(item.field + "不能为空");
                                    }

                                    break;
                                case "int":
                                    long.Parse(dicParams[item.field]);
                                    break;
                                case "datetime":
                                    if (long.TryParse(dicParams[item.field], out long result))
                                    {
                                        dicParams[item.field].TimeStampToDateTime();
                                    }
                                    else
                                    {
                                        DateTime.Parse(dicParams[item.field]);
                                    }

                                    break;
                                case "decimal":
                                    decimal.Parse(dicParams[item.field]);
                                    break;
                            }
                        }
                        else
                        {
                            throw Oops.Oh(item.field + "不能为空");
                        }
                    }
                }
            }
        }
        catch (AppFriendlyException ex)
        {
            throw Oops.Oh("必要参数不能为空");
        }
    }

    /// <summary>
    ///     远端数据前段参数处理.
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    private Dictionary<string, string> GetFarEndParams(List<DataInterfaceReqParameterInfo> paramList)
    {
        Dictionary<string, string> ret = new();
        if (paramList != null && paramList.Any())
        {
            foreach (DataInterfaceReqParameterInfo item in paramList)
            {
                if (item.defaultValue is JArray || item.defaultValue is Array)
                {
                    ret.Add(item.field, item.defaultValue.ToJson().Trim('"'));
                }
                else
                {
                    ret.Add(item.field, item.defaultValue?.ToString());
                }
            }
        }

        return ret;
    }

    #region Sql

    /// <summary>
    ///     执行sql.
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="isEcho"></param>
    /// <returns></returns>
    private async Task<object> GetSqlData(DataInterfaceEntity entity, bool isEcho = false)
    {
        DataInterfaceProperJson? propJson = isEcho
            ? entity.DataEchoJson.ToObjectOld<DataInterfaceProperJson>()
            : entity.DataConfigJson.ToObjectOld<DataInterfaceProperJson>();
        List<SugarParameter> parameter = entity.ParameterJson.ToObjectOld<List<DataInterfaceReqParameter>>()
            .Select(x => new SugarParameter("@" + x.field, GetSugarParameterList(x))).ToList();
        object sqlData = await ExcuteSql(propJson, parameter, entity.Action.ParseToInt());
        // // 打印sqldata返回数据条数,datatable类型
        // if (sqlData is DataTable dt) 
        // {
        //     Console.WriteLine($"sqldata返回数据条数: {dt.Rows.Count}");
        // }
        if (isEcho)
        {
            return sqlData.ToObjectOld<List<Dictionary<string, object>>>().FirstOrDefault();
        }

        if (entity.HasPage == 1)
        {
            propJson = entity.DataCountJson.ToObjectOld<DataInterfaceProperJson>();
            object count = await ExcuteSql(propJson, parameter, -1);
            return new
            {
                list = sqlData,
                pagination = new { currentPage, pageSize, total = count.ParseToInt() }
            };
        }

        return sqlData;
    }

    /// <summary>
    ///     替换sql参数默认值.
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="dic"></param>
    [NonAction]
    public void ReplaceSqlParameter(DataInterfaceEntity entity, Dictionary<string, string> dic)
    {
        if (dic.IsNotEmptyOrNull() && entity.IsNotEmptyOrNull() && entity.ParameterJson.IsNotEmptyOrNull())
        {
            List<DataInterfaceReqParameter> parameterList = entity.ParameterJson.ToObjectOld<List<DataInterfaceReqParameter>>();
            foreach (DataInterfaceReqParameter item in parameterList)
            {
                if (dic.Keys.Contains(item.field))
                {
                    if (dic[item.field].IsNullOrEmpty())
                    {
                        dic[item.field] = item.defaultValue;
                    }

                    item.defaultValue = HttpUtility.UrlDecode(dic[item.field], Encoding.UTF8); // 对参数解码
                }

                if (entity.Type == 1)
                {
                    // 将sql语句参数替换成@field
                    entity.DataConfigJson = entity.DataConfigJson?.Replace("{" + item.field + "}", "@" + item.field);
                    entity.DataCountJson = entity.DataCountJson?.Replace("{" + item.field + "}", "@" + item.field);
                    entity.DataEchoJson = entity.DataEchoJson?.Replace("{" + item.field + "}", "@" + item.field);
                }
            }

            entity.ParameterJson = parameterList.ToJson();
        }
    }

    /// <summary>
    ///     执行sql.
    /// </summary>
    /// <param name="properJson"></param>
    /// <param name="parameter"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    private async Task<object> ExcuteSql(DataInterfaceProperJson properJson, List<SugarParameter> parameter, int action)
    {
        DataTable dt = new();
        DbLink? link = new();
        if (!_sqlSugarClient.AsTenant().IsAnyConnection(_configId))
        {
            link = await _sqlSugarClient.CopyNew().Queryable<DbLink>()
                .FirstAsync(x => x.Id == properJson.sqlData.dbLinkId);
        }
        else
        {
            link = await _repository.AsSugarClient().CopyNew().Queryable<DbLink>()
                .FirstAsync(x => x.Id == properJson.sqlData.dbLinkId);
        }

        DbLink? tenantLink = link ?? _dataBaseManager.GetTenantDbLink(Convert.ToInt64(_configId), _dbName);
        string sql = GetSqlParameter(properJson.sqlData.sql, parameter);
        // 拼接传递参数
        sql = await GetDataTableWithCondition(sql, parameter);
        if (action == 3)
        {
            dt = _dataBaseManager.GetSqlData(tenantLink, sql, true, parameter.ToArray());
        }
        else if (action == -1)
        {
            return _dataBaseManager.GetCount(tenantLink, properJson.sqlData.sql, true, parameter.ToArray());
        }
        else
        {
            return await _dataBaseManager.ExecuteSql(tenantLink, sql, true, parameter.ToArray());
        }

        return dt;
    }

    /// <summary>
    ///     替换sql变量参数.拼接条件
    /// </summary>
    /// <param name="sql"></param>
    /// <param name="sugarParameters"></param>
    private async Task<string> GetDataTableWithCondition(string sql, List<SugarParameter> sugarParameters)
    {
        List<string> conditions = new();
        Dictionary<string, object> newParameters = new();

        foreach (SugarParameter param in sugarParameters)
        {
            object? value = param.Value;
            sql = sql.Replace("{" + param.ParameterName.Trim('@') + "}", param.ParameterName);
            if (!sql.Contains(param.ParameterName.Trim('@')) && value != null && !string.IsNullOrWhiteSpace(value.ToString())) // 检查值是否有效  
            {
                // 根据参数名称生成条件  
                string condition = $"{param.ParameterName.Trim('@')} = {param.ParameterName}";
                conditions.Add(condition);
                newParameters[param.ParameterName] = value;
            }
        }

        // 构造最终的 SQL 语句  
        if (conditions.Count > 0)
        {
            int whereIndex = sql.IndexOf("WHERE", StringComparison.OrdinalIgnoreCase);
            int groupByIndex = sql.IndexOf("GROUP BY", StringComparison.OrdinalIgnoreCase);
            int orderByIndex = sql.IndexOf("ORDER BY", StringComparison.OrdinalIgnoreCase);

            // 插入条件逻辑  
            if (whereIndex >= 0) // 存在 WHERE  
            {
                if (groupByIndex > whereIndex) // GROUP BY 在 WHERE 之后  
                {
                    sql = sql.Insert(groupByIndex, " AND " + string.Join(" AND ", conditions));
                }
                else // GROUP BY 在 WHERE 之前或不存在  
                {
                    sql += " AND " + string.Join(" AND ", conditions);
                }
            }
            else // 不存在 WHERE  
            {
                if (groupByIndex >= 0) // 存在 GROUP BY  
                {
                    sql = sql.Insert(groupByIndex, " WHERE " + string.Join(" AND ", conditions));
                }
                else if (orderByIndex >= 0) // 存在 ORDER BY  
                {
                    sql = sql.Insert(orderByIndex, " WHERE " + string.Join(" AND ", conditions));
                }
                else // 既没有 WHERE 也没有 GROUP BY 或 ORDER BY  
                {
                    sql += " WHERE " + string.Join(" AND ", conditions);
                }
            }
        }

        // 确保 SQL 语句末尾是合理的  
        sql = sql.Trim();
        if (sql.EndsWith(";", StringComparison.OrdinalIgnoreCase))
        {
            sql = sql.TrimEnd(';'); // 移除末尾的分号  
        }

        return sql;
    }

    /// <summary>
    ///     获取sql系统变量参数.
    /// </summary>
    /// <param name="sql"></param>
    /// <param name="sugarParameters"></param>
    [NonAction]
    public string GetSqlParameter(string sql, List<SugarParameter> sugarParameters)
    {
        if (_userManager.ToKen != null)
        {
            if (sql.Contains("@currentUser") && !sugarParameters.Any(x => x.ParameterName == "@currentUser"))
            {
                sugarParameters.Add(new SugarParameter("@currentUser", _userManager.UserId));
            }

            if (sql.Contains("@snowFlakeID") && !sugarParameters.Any(x => x.ParameterName == "@snowFlakeID"))
            {
                sugarParameters.Add(new SugarParameter("@snowFlakeID", YitIdHelper.NextId()));
            }

            if (sql.Contains("@lotSnowID") && !sugarParameters.Any(x => x.ParameterName == "@lotSnowID"))
            {
                int splitCount = sql.Split("@lotSnowID").Count();
                Regex r = new("@lotSnowID");
                for (int i = 0; i < splitCount - 1; i++)
                {
                    string newId = string.Format("@snowID{0}", i);
                    sql = r.Replace(sql, newId, 1);
                    sugarParameters.Add(new SugarParameter(string.Format("@snowID{0}", i), YitIdHelper.NextId()));
                }
            }

            // if (sql.Contains("@currentUsersAndSubordinates") &&
            //     !sugarParameters.Any(x => x.ParameterName == "@currentUsersAndSubordinates"))
            // {
            //     var subordinates = _userManager.CurrentUserAndSubordinates;
            //     sugarParameters.Add(new SugarParameter("@currentUsersAndSubordinates", subordinates));
            // }

            if (sql.Contains("@organization") && !sugarParameters.Any(x => x.ParameterName == "@organization"))
            {
                sugarParameters.Add(new SugarParameter("@organization", _userManager?.User?.OrgId));
            }

            // if (sql.Contains("@currentOrganizationAndSuborganization") &&
            //     !sugarParameters.Any(x => x.ParameterName == "@currentOrganizationAndSuborganization"))
            // {
            //     var subsidiary = _userManager.CurrentOrganizationAndSubOrganizations;
            //     sugarParameters.Add(new SugarParameter("@currentOrganizationAndSuborganization", subsidiary));
            // }

            // if (sql.Contains("@chargeorganization") &&
            //     !sugarParameters.Any(x => x.ParameterName == "@chargeorganization"))
            // {
            //     var chargeorganization = _userManager.DataScope.Select(x => x.organizeId).ToList();
            //     if (_userManager.IsAdministrator)
            //     {
            //         chargeorganization = _repository.AsSugarClient().Queryable<OrganizeEntity>()
            //             .Where(x => x.DeleteMark == null && x.EnabledMark == 1).Select(x => x.Id).ToList();
            //     }
            //
            //     sugarParameters.Add(new SugarParameter("@chargeorganization", chargeorganization));
            // }

            // if (sql.Contains("@currentChargeorganizationAndSuborganization") &&
            //     !sugarParameters.Any(x => x.ParameterName == "@currentChargeorganizationAndSuborganization"))
            // {
            //     var subsidiary = _userManager.DataScope.Select(x => x.organizeId).ToList();
            //     if (_userManager.IsAdministrator)
            //     {
            //         subsidiary = _repository.AsSugarClient().Queryable<OrganizeEntity>()
            //             .Where(x => x.DeleteMark == null && x.EnabledMark == 1).Select(x => x.Id).ToList();
            //     }
            //
            //     sugarParameters.Add(new SugarParameter("@currentChargeorganizationAndSuborganization", subsidiary));
            // }

            if (sql.Contains("@offsetSize") && !sugarParameters.Any(x => x.ParameterName == "@offsetSize"))
            {
                sugarParameters.Add(new SugarParameter("@offsetSize", (currentPage - 1) * pageSize));
            }

            if (sql.Contains("@pageSize") && !sugarParameters.Any(x => x.ParameterName == "@pageSize"))
            {
                sugarParameters.Add(new SugarParameter("@pageSize", pageSize));
            }

            if (sql.Contains("@showValue") && !sugarParameters.Any(x => x.ParameterName == "@showValue"))
            {
                sugarParameters.Add(new SugarParameter("@showValue", showValue));
            }

            if (sql.Contains("@keyword") && !sugarParameters.Any(x => x.ParameterName == "@keyword"))
            {
                sugarParameters.Add(new SugarParameter("@keyword", keyword));
            }

            if (sql.Contains("@showKey"))
            {
                sql = sql.Replace("@showKey", showKey);
            }
        }

        return sql;
    }

    /// <summary>
    ///     转换接口参数类型.
    /// </summary>
    /// <param name="dataInterfaceReqParameter"></param>
    /// <returns></returns>
    [NonAction]
    public object GetSugarParameterList(DataInterfaceReqParameter dataInterfaceReqParameter)
    {
        try
        {
            object? value = new();
            if (dataInterfaceReqParameter.defaultValue.IsNotEmptyOrNull())
            {
                switch (dataInterfaceReqParameter.dataType)
                {
                    case "int":
                        value = long.Parse(dataInterfaceReqParameter.defaultValue);
                        break;
                    case "datetime":
                        if (long.TryParse(dataInterfaceReqParameter.defaultValue, out long result))
                        {
                            value =
                                $"{DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(dataInterfaceReqParameter.defaultValue)).ToLocalTime().DateTime:yyyy-MM-dd HH:mm:ss}";
                        }
                        else
                        {
                            value = DateTime.Parse(dataInterfaceReqParameter.defaultValue);
                        }

                        break;
                    case "decimal":
                        value = decimal.Parse(dataInterfaceReqParameter.defaultValue);
                        break;
                    default:
                        value = dataInterfaceReqParameter.defaultValue;
                        break;
                }
            }
            else
            {
                value = string.Empty;
            }

            return value;
        }
        catch (Exception)
        {
            throw Oops.Oh("参数类型错误");
        }
    }

    #endregion

    #region Api

    /// <summary>
    ///     根据不同规则请求接口(预览).
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    private async Task<JObject> GetApiData(DataInterfaceEntity entity, Dictionary<string, string> dicParameters, int type, bool isEcho = false)
    {
        try
        {
            DataInterfaceProperJson? propJson = isEcho && entity.HasPage == 1
                ? entity.DataEchoJson.ToObjectOld<DataInterfaceProperJson>()
                : entity.DataConfigJson.ToObjectOld<DataInterfaceProperJson>();
            GetApiParameter(propJson, entity.ParameterJson, dicParameters);
            JObject? result = await RequestApi(entity, propJson, isEcho);
            // 异常验证
            if (entity.DataExceptionJson.IsNotEmptyOrNull())
            {
                object flag = await _enginePool.ExecuteScriptAsync("interface", entity.DataExceptionJson, result);
                if (flag.ToJson().Equals("false"))
                {
                    if (entity.IsPostposition == 0)
                    {
                        if (propJson.variateIds.Any())
                        {
                            foreach (long item in propJson.variateIds)
                            {
                                await Preview(item, new DataInterfacePreviewInput());
                            }

                            GetApiParameter(propJson, entity.ParameterJson, dicParameters);
                            result = await RequestApi(entity, propJson, isEcho);
                        }
                        else
                        {
                            throw Oops.Oh("接口请求失败或接口数据异常");
                        }
                    }
                    else
                    {
                        throw Oops.Oh("鉴权接口请求失败或接口数据异常");
                    }
                }
            }

            // 预览更新变量值.
            if (entity.IsPostposition == 1)
            {
                List<DataInterfaceVariateEntity>? variateList = await _sqlSugarClient.Queryable<DataInterfaceVariateEntity>()
                    .Where(x => x.InterfaceId == entity.Id).ToListAsync();
                foreach (DataInterfaceVariateEntity? item in variateList)
                {
                    object value = await _enginePool.ExecuteScriptAsync("interface", item.Expression, result); //此处时间非时间戳
                    item.Value = value.ToJson().Trim('"');
                }

                await _sqlSugarClient.Updateable(variateList).ExecuteCommandAsync();
            }

            if (result.ContainsKey("code") && result.ContainsKey("data") && result.ContainsKey("msg"))
            {
                result = type == 4 ? result : result["data"].ToObjectOld<JObject>();
            }

            if (isEcho && result is JArray)
            {
                return JArray.Parse(result.ToJson()).FirstOrDefault().ToObjectOld<JObject>();
            }

            return result;
        }
        catch (Exception ex)
        {
            throw Oops.Oh("接口请求失败或接口数据异常");
        }
    }

    /// <summary>
    ///     获取api参数.
    /// </summary>
    /// <returns></returns>
    private string GetApiParameter(DataInterfaceProperJson propJson, string parameterJson,
        Dictionary<string, string> dicParameter)
    {
        List<DataInterfaceReqParameter> parameterList = parameterJson.ToObjectOld<List<DataInterfaceReqParameter>>();
        foreach (DataInterfaceReqParameter item in parameterList)
        {
            if (dicParameter.Keys.Contains(item.field))
            {
                item.defaultValue = HttpUtility.UrlDecode(dicParameter[item.field], Encoding.UTF8);
            }
        }

        parameterList.Add(
            new DataInterfaceReqParameter { field = "currentPage", defaultValue = currentPage.ToString() });
        parameterList.Add(new DataInterfaceReqParameter { field = "pageSize", defaultValue = pageSize.ToString() });
        parameterList.Add(new DataInterfaceReqParameter { field = "keyword", defaultValue = keyword });
        parameterList.Add(new DataInterfaceReqParameter { field = "showKey", defaultValue = showKey });
        parameterList.Add(new DataInterfaceReqParameter { field = "showValue", defaultValue = showValue });
        if (propJson.apiData.header.Any())
        {
            foreach (DataInterfaceReqParameter item in propJson.apiData.header)
            {
                if (item.source == "1")
                {
                    if (dicParameter.ContainsKey(item.defaultValue))
                    {
                        item.defaultValue = dicParameter[item.defaultValue];
                    }
                    else
                    {
                        DataInterfaceReqParameter? parameter = parameterList.Find(x => x.field == item.defaultValue);
                        item.defaultValue = parameter.IsNullOrEmpty() ? item.defaultValue : parameter.defaultValue;
                    }
                }

                if (item.source == "2")
                {
                    DataInterfaceVariateEntity? variateEntity = _sqlSugarClient.Queryable<DataInterfaceVariateEntity>()
                        .First(x => x.FullName == item.field);
                    item.defaultValue = variateEntity.Value.Trim('"');
                    propJson.variateIds.Add(variateEntity.InterfaceId);
                }

                if (item.source == "4" && parameterList.Any(it => it.field.Equals(item.field)))
                {
                    item.defaultValue = parameterList.Find(it => it.field.Equals(item.field)).defaultValue;
                }
            }
        }

        if (propJson.apiData.query.Any())
        {
            foreach (DataInterfaceReqParameter item in propJson.apiData.query)
            {
                if (item.source == "1")
                {
                    if (dicParameter.ContainsKey(item.defaultValue))
                    {
                        item.defaultValue = dicParameter[item.defaultValue];
                    }
                    else
                    {
                        DataInterfaceReqParameter? parameter = parameterList.Find(x => x.field == item.defaultValue);
                        item.defaultValue = parameter.IsNullOrEmpty() ? item.defaultValue : parameter.defaultValue;
                    }
                }

                if (item.source == "2")
                {
                    DataInterfaceVariateEntity? variateEntity = _sqlSugarClient.Queryable<DataInterfaceVariateEntity>()
                        .First(x => x.Id.ToString() == item.defaultValue);
                    item.defaultValue = variateEntity.Value;
                    propJson.variateIds.Add(variateEntity.InterfaceId);
                }

                if (item.source == "4" && parameterList.Any(it => it.field.Equals(item.field)))
                {
                    item.defaultValue = parameterList.Find(it => it.field.Equals(item.field)).defaultValue;
                }

                item.defaultValue = HttpUtility.UrlDecode(item.defaultValue, Encoding.UTF8);
            }
        }

        if (propJson.apiData.body.IsNotEmptyOrNull())
        {
            if (propJson.apiData.bodyType == "1" || propJson.apiData.bodyType == "2")
            {
                List<DataInterfaceReqParameter>? bodyList =
                    propJson.apiData.body.ToObjectOld<List<DataInterfaceReqParameter>>();
                foreach (DataInterfaceReqParameter item in bodyList)
                {
                    if (item.source == "1")
                    {
                        if (dicParameter.ContainsKey(item.defaultValue))
                        {
                            item.defaultValue = dicParameter[item.defaultValue];
                        }
                        else
                        {
                            DataInterfaceReqParameter?
                                parameter = parameterList.Find(x => x.field == item.defaultValue);
                            item.defaultValue = parameter.IsNullOrEmpty() ? item.defaultValue : parameter.defaultValue;
                        }
                    }

                    if (item.source == "2")
                    {
                        DataInterfaceVariateEntity? variateEntity = _sqlSugarClient
                            .Queryable<DataInterfaceVariateEntity>().First(x => x.FullName == item.field);
                        item.defaultValue = variateEntity.Value.Trim('"');
                        propJson.variateIds.Add(variateEntity.InterfaceId);
                    }

                    if (item.source == "4" && parameterList.Any(it => it.field.Equals(item.field)))
                    {
                        item.defaultValue = parameterList.Find(it => it.field.Equals(item.field)).defaultValue;
                    }
                }
            }
            else
            {
                foreach (DataInterfaceReqParameter item in parameterList)
                {
                    propJson.apiData.body = propJson.apiData.body.Replace("{" + item.field + "}", item.defaultValue);
                }

                List<DataInterfaceVariateEntity>? variateList = _sqlSugarClient.Queryable<DataInterfaceVariateEntity>()
                    .ToList();
                foreach (DataInterfaceVariateEntity? item in variateList)
                {
                    if (propJson.apiData.body.Contains("{@" + item.FullName + "}"))
                    {
                        propJson.apiData.body = propJson.apiData.body.Replace("{@" + item.FullName + "}", item.Value);
                        propJson.variateIds.Add(item.InterfaceId);
                    }
                }
            }
        }

        propJson.apiData.url = propJson.apiData.url.Replace("{showKey}", showKey).Replace("{showValue}", showValue).TrimStart().TrimEnd();
        if (!propJson.apiData.url.StartsWith("http"))
        {
            propJson.apiData.url = GetLocalAddress() + propJson.apiData.url;
        }

        return propJson.ToJson();
    }

    private async Task<JObject> RequestApi(DataInterfaceEntity entity, DataInterfaceProperJson propJson, bool isEcho)
    {
        JObject? result = new();
        Dictionary<string, object> heraderParameters = new(); // 头部参数.
        Dictionary<string, object> queryParameters = new(); // 请求参数.
        Dictionary<string, object>? bodyParameters = new(); // 请求参数.
        object? body = new();
        //heraderParameters.Add("jnpf_api", true); // 接口返回结果是{"code":200,"msg":"操作成功","data":null}结构则直接取data数据
        if (_userManager.ToKen != null && !_userManager.ToKen.Contains("::"))
        {
            heraderParameters.Add("Authorization", _userManager.ToKen);
            heraderParameters.Add("Jnpf-Origin", _userManager.UserOrigin);
        }

        string path = propJson.apiData.url;
        string requestMethod = propJson.apiData.method;
        // contentType设置
        string contentType = null;
        if (propJson.apiData.bodyType == "2")
        {
            contentType = "application/x-www-form-urlencoded";
        }

        if (propJson.apiData.bodyType == "4")
        {
            contentType = "application/xml";
        }

        foreach (DataInterfaceReqParameter key in propJson.apiData.query)
        {
            queryParameters.Add(key.field, key.defaultValue);
        }

        foreach (DataInterfaceReqParameter key in propJson.apiData.header)
        {
            heraderParameters[key.field] = key.defaultValue;
        }

        if (propJson.apiData.body.IsNotEmptyOrNull())
        {
            if (propJson.apiData.bodyType == "1" || propJson.apiData.bodyType == "2")
            {
                foreach (DataInterfaceReqParameter key in propJson.apiData.body
                             .ToObjectOld<List<DataInterfaceReqParameter>>())
                {
                    bodyParameters.Add(key.field, GetSugarParameterList(key));
                }

                body = bodyParameters;
            }
            else
            {
                if ('['.Equals(propJson.apiData.body.FirstOrDefault()))
                {
                    body = propJson.apiData.body.ToObjectOld<List<Dictionary<string, object>>>();
                }
                else
                {
                    bodyParameters = propJson.apiData.body.ToObjectOld<Dictionary<string, object>>();
                    body = bodyParameters;
                }
            }
        }

        switch (requestMethod)
        {
            case "1":
                result = (await _httpRemoteService.GetAsStringAsync(path, builder => builder.WithHeaders(heraderParameters)
                        .SetContent(queryParameters)))
                    .ToObjectOld<JObject>();
                break;
            case "2":
                result = (await _httpRemoteService.PostAsStringAsync(path, builder => builder.WithHeaders(heraderParameters)
                    .SetContent(body, contentType)
                )).ToObjectOld<JObject>();
                break;
        }

        return result;
    }

    #endregion

    #region 接口验证

    /// <summary>
    ///     外部接口验证并请求.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="tenantId"></param>
    /// <param name="dic"></param>
    /// <returns></returns>
    private async Task<dynamic> InterfaceVerify(long id, string tenantId, Dictionary<string, string> dic)
    {
        object output = null;
        DataInterfacePreviewInput input = new() { dicParameters = dic };
        UserAgent userAgent = new(App.HttpContext);
        long userId = 0;
        string appId = VerifyInterfaceOauth(id, ref userId);
        Stopwatch sw = new();
        sw.Start();
        if (userId.IsNullOrEmpty())
        {
            output = await GetDataInterfaceData(id, input, 3);
            if (tenantId.IsNotEmptyOrNull() && _sqlSugarClient.IsAnyConnection(tenantId))
            {
                _sqlSugarClient.ChangeDatabase(tenantId);
            }
        }
        else
        {
            SysUser? userEntity = _sqlSugarClient.Queryable<SysUser>().First(x => x.Id == userId && x.Enable == true);
            string token = NetHelper.GetToken(userEntity.Id, userEntity.Account, userEntity.Name, userEntity.AdminType, tenantId);
            Dictionary<string, object> heraderDic = new();
            heraderDic.Add("Authorization", token);
            heraderDic.Add("JNPF_API", true);
            ScheduleTaskModel scheduleTaskModel = new();
            scheduleTaskModel.taskParams.Add("id", id);
            scheduleTaskModel.taskParams.Add("input", input.ToJson());
            string path = string.Format("{0}/ScheduleTask/datainterface", GetLocalAddress());
            string? result = await _httpRemoteService.PostAsStringAsync(path, builder => builder.WithHeaders(heraderDic)
                .SetContent(scheduleTaskModel));
            output = result.FirstOrDefault().Equals('[') ? result.ToObjectOld<JArray>() : result.ToObjectOld<JObject>();
        }

        sw.Stop();

        #region 插入日志

        if (App.HttpContext.IsNotEmptyOrNull())
        {
            HttpContext? httpContext = App.HttpContext;
            IHeaderDictionary headers = httpContext.Request.Headers;
            DataInterfaceLogEntity log = new()
            {
                Id = YitIdHelper.NextId(),
                OauthAppId = appId,
                InvokId = id,
                InvokTime = DateTime.Now,
                InvokIp = httpContext.GetLocalIpAddressToIPv4(),
                InvokDevice = string.Format("{0}-{1}", userAgent.OS, userAgent.RawValue),
                InvokWasteTime = (int)sw.ElapsedMilliseconds,
                InvokType = httpContext.Request.Method
            };
            await _sqlSugarClient.Insertable(log).ExecuteCommandAsync();
        }

        #endregion

        return output;
    }

    /// <summary>
    ///     HMACSHA256加密.
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="interfaceId"></param>
    /// <param name="ymDate"></param>
    /// <returns></returns>
    private string GetVerifySignature(InterfaceOauthEntity entity, long interfaceId, string ymDate)
    {
        string secret = entity.AppSecret;
        string method = "POST";
        string urlPath = string.Format("/DataInterface/{0}/Actions/Response", interfaceId);
        string YmDate = ymDate;
        string host = App.HttpContext.Request.Host.ToString();
        string source = new StringBuilder().Append(method).Append('\n').Append(urlPath).Append('\n')
            .Append(YmDate).Append('\n').Append(host).ToString();
        using (HMACSHA256 hmac = new(secret.ToBase64String().ToBytes()))
        {
            byte[] hashmessage = hmac.ComputeHash(source.ToBytes(Encoding.UTF8));
            string? signature = hashmessage.ToHexString();
            return entity.AppId + "::" + signature;
        }
    }

    /// <summary>
    ///     外部接口验证.
    /// </summary>
    /// <param name="interfaceId"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    private string VerifyInterfaceOauth(long interfaceId, ref long userId)
    {
        string authorization = App.HttpContext.Request.Headers["Authorization"].ToString();
        if (authorization.IsNullOrEmpty())
        {
            throw Oops.Oh("请求头部参数Authorization不能为空");
        }

        string ymDate = App.HttpContext.Request.Headers["YmDate"].ToString();
        if (ymDate.IsNullOrEmpty())
        {
            throw Oops.Oh("请求头部参数YmDate不能为空");
        }

        string appId = authorization.Split("::")[0];
        string appSecret = authorization.Split("::")[1];
        bool isUserKey = App.HttpContext.Request.Headers.ContainsKey("UserKey");
        string userKey = isUserKey ? App.HttpContext.Request.Headers["UserKey"].ToString() : string.Empty;
        InterfaceOauthEntity? interfaceEntity = _sqlSugarClient.Queryable<InterfaceOauthEntity>()
            .First(x => x.AppId == appId && x.EnabledMark == 1);
        if (interfaceEntity.IsNullOrEmpty() || string.IsNullOrEmpty(interfaceEntity.DataInterfaceIds) ||
            !interfaceEntity.DataInterfaceIds.Contains(interfaceId.ToString()))
        {
            throw Oops.Oh("接口未授权");
        }

        List<DataInterfaceUserEntity>? dataInterfaceUserList = _sqlSugarClient.Queryable<DataInterfaceUserEntity>()
            .Where(x => x.OauthId == interfaceEntity.Id).ToList();
        if (dataInterfaceUserList.Any())
        {
            if (isUserKey)
            {
                if (dataInterfaceUserList.Any(x => x.UserKey == userKey))
                {
                    userId = (long)dataInterfaceUserList.Find(x => x.UserKey == userKey).UserId;
                }
                else
                {
                    throw Oops.Oh("userKey不匹配,请填写正确的userKey");
                }
            }
            else
            {
                throw Oops.Oh("未填写userKey,请确认");
            }
        }

        if (interfaceEntity.WhiteList.IsNotEmptyOrNull())
        {
            List<string> ipList = interfaceEntity.WhiteList.Split(",").ToList();
            if (!ipList.Contains(App.HttpContext.GetLocalIpAddressToIPv4()))
            {
                throw Oops.Oh("此IP未在白名单中");
            }
        }

        if (interfaceEntity.UsefulLife.IsNotEmptyOrNull() && interfaceEntity.UsefulLife < DateTime.Now)
        {
            throw Oops.Oh("接口授权已过期");
        }

        if (interfaceEntity.VerifySignature == 1)
        {
            if (DateTime.Now > DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(ymDate)).ToLocalTime().DateTime
                    .AddMinutes(1))
            {
                throw Oops.Oh("接口授权已过期");
            }

            string signature = GetVerifySignature(interfaceEntity, interfaceId, ymDate);
            if (authorization != signature)
            {
                throw Oops.Oh("接口未授权");
            }
        }
        else
        {
            if (interfaceEntity.AppSecret != appSecret)
            {
                throw Oops.Oh("接口未授权");
            }
        }

        return appId;
    }

    /// <summary>
    ///     本地地址.
    /// </summary>
    /// <returns></returns>
    private string GetLocalAddress()
    {
        using IServiceScope scope = _serviceScopeFactory.CreateScope();
        IServer server = scope.ServiceProvider.GetRequiredService<IServer>();
        IServerAddressesFeature? addressesFeature = server.Features.Get<IServerAddressesFeature>();
        ICollection<string>? addresses = addressesFeature?.Addresses;
        return addresses.FirstOrDefault().Replace("[::]", "localhost");
    }

    #endregion

    #region 代码生成

    /// <summary>
    ///     获取代码生成远端数据缓存.
    /// </summary>
    /// <param name="key">缓存标识.</param>
    /// <param name="dynamicId">远端数据ID.</param>
    /// <returns></returns>
    private async Task<List<StaticDataModel>> GetDynamicDataCache(string key, long dynamicId)
    {
        string cacheKey = string.Format("{0}{1}_{2}_{3}", CommonConst.CodeGenDynamic, _userManager.TenantId, key, dynamicId);
        return _cacheManager.Get<List<StaticDataModel>>(cacheKey);
    }

    /// <summary>
    ///     保存代码生成远端数据缓存.
    /// </summary>
    /// <param name="dynamicId">远端数据ID.</param>
    /// <param name="list">在线用户列表.</param>
    /// <param name="key">缓存标识.</param>
    /// <returns></returns>
    private async Task<bool> SetDynamicDataCache(string key, long dynamicId, List<StaticDataModel> list)
    {
        string cacheKey = string.Format("{0}{1}_{2}", CommonConst.CodeGenDynamic, _userManager.TenantId, key, dynamicId);
        return _cacheManager.Set(cacheKey, list, TimeSpan.FromMinutes(3));
    }

    /// <summary>
    ///     获取动态无限级数据.
    /// </summary>
    /// <param name="data"></param>
    /// <param name="value">指定选项标签为选项对象的某个属性值.</param>
    /// <param name="label">指定选项的值为选项对象的某个属性值.</param>
    /// <param name="children">指定选项的子选项为选项对象的某个属性值.</param>
    /// <returns></returns>
    private List<StaticDataModel> GetDynamicInfiniteData(string data, string value, string label, string children)
    {
        List<StaticDataModel> list = new();
        foreach (JToken? info in JToken.Parse(data))
        {
            StaticDataModel dic = new()
            {
                id = info.Value<string>(value),
                fullName = info.Value<string>(label)
            };
            list.Add(dic);
            if (info.Value<object>(children) != null && info.Value<object>(children).ToString() != string.Empty)
            {
                list.AddRange(GetDynamicInfiniteData(info.Value<object>(children).ToString(), value, label, children));
            }
        }

        return list;
    }

    #endregion

    #endregion
}