namespace Systems.Core.Message.Dto;

public class MessageInput
{
    /// <summary>
    ///     用户ID
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    ///     用户ID列表
    /// </summary>
    public List<long> UserIds { get; set; }

    /// <summary>
    ///     消息标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    ///     消息类型
    /// </summary>
    public MessageTypeEnum MessageType { get; set; }

    /// <summary>
    ///     消息内容
    /// </summary>
    public string Message { get; set; }
}

/// <summary>
///     消息类型枚举
/// </summary>
[Description("消息类型枚举")]
public enum MessageTypeEnum
{
    /// <summary>
    ///     普通信息
    /// </summary>
    [Description("消息")] Info = 0,

    /// <summary>
    ///     成功提示
    /// </summary>
    [Description("成功")] Success = 1,

    /// <summary>
    ///     警告提示
    /// </summary>
    [Description("警告")] Warning = 2,

    /// <summary>
    ///     错误提示
    /// </summary>
    [Description("错误")] Error = 3
}