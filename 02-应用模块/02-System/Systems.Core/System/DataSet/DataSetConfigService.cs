using Common.Core.Manager.DataBase;
using IotPlatform.Core.Enum;
using Common.Security;
using DateTime = System.DateTime;
using Common.Models;
using Jint;
using NewLife.Serialization;
using IotPlatform.Core.Extension;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using System.Text;
using System.Web;
using System.IO;

namespace Systems.Core.DataSet;

/// <summary>
///     数据集配置
/// </summary>
[ApiDescriptionSettings("系统配置", Order = 450)]
public class DataSetConfigService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     数据集配置仓储
    /// </summary>
    private readonly ISqlSugarRepository<ChartsDataConfigEntity> _chartsConfigRepository;
    /// <summary>
    ///     数据集分组仓储
    /// </summary>
    private readonly ISqlSugarRepository<ChartsDataGroupEntity> _chartsGroupRepository;
    /// <summary>
    ///     用户管理
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    ///     
    /// </summary>
    private readonly IDataBaseManager _changeDataBase;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="chartsConfigRepository">数据集配置仓储</param>
    /// <param name="chartsGroupRepository">数据集分组仓储</param>
    /// <param name="userManager">用户管理</param>
    public DataSetConfigService(
        ISqlSugarRepository<ChartsDataConfigEntity> chartsConfigRepository,
        ISqlSugarRepository<ChartsDataGroupEntity> chartsGroupRepository,
        IUserManager userManager, IDataBaseManager changeDataBase)
    {
        _chartsConfigRepository = chartsConfigRepository;
        _chartsGroupRepository = chartsGroupRepository;
        _userManager = userManager;
        _changeDataBase = changeDataBase;
    }

    #region ChartsDataConfig功能

    /// <summary>
    ///     获取数据集树形结构
    /// </summary>
    /// <returns>数据集树形结构</returns>
    [HttpGet("/ChartsDataConfig/GetTreeJson")]
    [DisplayName("获取数据集树形结构")]
    public async Task<List<ChartsDataTreeOutput>> GetTreeJson()
    {
        List<ChartsDataTreeOutput> result = new();

        // 获取所有分组
        List<ChartsDataGroupEntity> groups = await _chartsGroupRepository.GetListAsync();

        // 添加分组节点
        foreach (ChartsDataGroupEntity group in groups)
        {
            // 特殊处理根节点
            if (group.ParentId == "ROOT")
            {
                result.Add(new ChartsDataTreeOutput
                {
                    ParentId = "#",  // 根节点的父级在树形控件中通常为"#"
                    Id = group.Id.ToString(),
                    Text = group.Name,
                    Icon = "fa fa-folder-open",
                    Type = "Root",  // 标记为根节点类型
                    State = new ChartsDataTreeState
                    {
                        Opened = true,  // 根节点默认展开
                        Disabled = false,
                        Selected = false
                    },
                    SimpleName = null,
                    IsGroup = group.IsGroup
                });
            }
            else
            {
                // 普通分组节点
                result.Add(new ChartsDataTreeOutput
                {
                    ParentId = group.ParentId,
                    Id = group.Id.ToString(),
                    Text = group.Name,
                    Icon = "fa fa-folder-open",
                    Type = "Classify",
                    State = new ChartsDataTreeState
                    {
                        Opened = false,
                        Disabled = false,
                        Selected = false
                    },
                    SimpleName = null,
                    IsGroup = group.IsGroup
                });
            }
        }

        // 获取所有数据集配置
        List<ChartsDataConfigEntity> configs = await _chartsConfigRepository.GetListAsync();

        // 添加数据集节点
        foreach (ChartsDataConfigEntity config in configs)
        {
            result.Add(new ChartsDataTreeOutput
            {
                ParentId = config.GroupId,
                Id = config.Id.ToString(),
                Text = config.Name,
                Type = "Data",
                DataType = config.Type
            });
        }

        return result;
    }

    /// <summary>
    ///     获取数据集详情
    /// </summary>
    /// <param name="input">数据集Id</param>
    /// <returns>数据集详情</returns>
    [HttpGet("/ChartsDataConfig/GetForm")]
    [DisplayName("获取数据集详情")]
    public async Task<ChartsDataFormOutput> GetForm([FromQuery] BaseId<string> input)
    {
        ChartsDataConfigEntity entity = await _chartsConfigRepository.GetFirstAsync(e => e.Id.ToString() == input.Id);
        if (entity == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        ChartsDataFormOutput output = new()
        {
            Id = entity.Id.ToString(),
            Name = entity.Name,
            GroupId = entity.GroupId,
            Type = entity.Type,
            Content = entity.Content,
            Fields = entity.Fields,
            Params = entity.Params,
            SortCode = entity.SortCode,
            CreateTime = string.Format("{0:yyyy-MM-dd HH:mm:ss}", entity.CreatedTime)
        };

        return output;
    }

    /// <summary>
    ///     创建或编辑数据集（基本信息）
    /// </summary>
    /// <param name="input">数据集基本信息</param>
    /// <returns>创建或编辑的数据集ID</returns>
    [HttpPost("/ChartsDataConfig/SaveForm")]
    [DisplayName("创建或编辑数据集基本信息")]
    public async Task<string> CreateDataSet([FromBody] ChartsDataCreateInput input)
    {
        // 检查分组是否存在
        ChartsDataGroupEntity group = await _chartsGroupRepository.GetFirstAsync(g => g.Id.ToString() == input.GroupId);
        if (group == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        ChartsDataConfigEntity entity;

        // 如果提供了ID，则尝试获取现有数据集进行编辑
        if (!string.IsNullOrEmpty(input.Id))
        {
            entity = await _chartsConfigRepository.GetFirstAsync(e => e.Id.ToString() == input.Id);
            if (entity == null)
            {
                throw Oops.Oh(ErrorCode.D1002); // 数据不存在
            }

            // 更新基本信息
            entity.Name = input.Name;
            entity.GroupId = input.GroupId;
            entity.Type = input.Type;
            entity.SortCode = input.SortCode;
            entity.UpdatedTime = DateTime.Now;
            entity.UpdatedUserId = _userManager.UserId;

            await _chartsConfigRepository.UpdateAsync(entity);
        }
        else
        {
            // 创建新的数据集配置（仅基本信息）
            entity = new()
            {
                Name = input.Name,
                GroupId = input.GroupId,
                Type = input.Type,
                SortCode = input.SortCode,
                CreatedTime = DateTime.Now,
                CreatedUserId = _userManager.UserId,
                TenantId = _userManager.TenantId
            };

            await _chartsConfigRepository.InsertAsync(entity);
        }

        // 返回数据集ID
        return entity.Id.ToString();
    }

    /// <summary>
    ///     保存数据集配置
    /// </summary>
    /// <param name="input">数据集配置</param>
    /// <returns></returns>
    [HttpPost("/ChartsDataConfig/SaveConfig")]
    [DisplayName("保存数据集配置")]
    public async Task SaveConfig([FromBody] ChartsDataConfigInput input)
    {
        // 获取数据集
        ChartsDataConfigEntity entity = await _chartsConfigRepository.GetFirstAsync(e => e.Id.ToString() == input.Id);
        if (entity == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 更新配置信息
        entity.Content = input.Content.ToJson();
        entity.Fields = input.Fields.ToJson();
        entity.Params = input.Params.ToJson();
        entity.UpdatedTime = DateTime.Now;
        entity.UpdatedUserId = _userManager.UserId;

        await _chartsConfigRepository.UpdateAsync(entity);
    }

    /// <summary>
    ///     删除数据集配置
    /// </summary>
    /// <param name="input">数据集Id</param>
    /// <returns></returns>
    [HttpPost("/ChartsDataConfig/DeleteForm")]
    [DisplayName("删除数据集配置")]
    public async Task DeleteForm(BaseId<string> input)
    {
        ChartsDataConfigEntity entity = await _chartsConfigRepository.GetFirstAsync(e => e.Id.ToString() == input.Id);
        if (entity == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        await _chartsConfigRepository.DeleteAsync(entity);
    }

    /// <summary>
    ///     保存数据集分组
    /// </summary>
    /// <param name="input">分组信息</param>
    /// <returns></returns>
    [HttpPost("/ChartsDataConfig/SaveGroup")]
    [DisplayName("保存数据集分组")]
    public async Task SaveGroup([FromBody] ChartsDataGroupSaveInput input)
    {
        ChartsDataGroupEntity entity = null;
        if (!string.IsNullOrEmpty(input.Id))
        {
            entity = await _chartsGroupRepository.GetFirstAsync(g => g.Id.ToString() == input.Id);
        }

        if (entity == null)
        {
            // 创建新的分组
            entity = new()
            {
                Name = input.Name,
                ParentId = input.ParentId,
                IsGroup = "Y",
                SortCode = input.SortCode,
                CreatedTime = DateTime.Now,
                CreatedUserId = _userManager.UserId,
                TenantId = _userManager.TenantId
            };

            await _chartsGroupRepository.InsertAsync(entity);
        }
        else
        {
            entity.Name = input.Name;
            entity.ParentId = input.ParentId;
            entity.SortCode = input.SortCode;
            entity.UpdatedTime = DateTime.Now;
            entity.UpdatedUserId = _userManager.UserId;

            await _chartsGroupRepository.UpdateAsync(entity);
        }
    }

    /// <summary>
    ///     删除数据集分组
    /// </summary>
    /// <param name="input">分组Id</param>
    /// <returns></returns>
    [HttpPost("/ChartsDataConfig/DeleteGroup")]
    [DisplayName("删除数据集分组")]
    public async Task DeleteGroup(BaseId<string> input)
    {
        ChartsDataGroupEntity entity = await _chartsGroupRepository.GetFirstAsync(g => g.Id.ToString() == input.Id);
        if (entity == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 检查该分组下是否有子分组
        bool hasChildGroups = await _chartsGroupRepository.IsAnyAsync(g => g.ParentId == input.Id);
        if (hasChildGroups)
        {
            throw Oops.Oh(ErrorCode.D1006);
        }

        // 检查该分组下是否有数据集
        bool hasConfigs = await _chartsConfigRepository.IsAnyAsync(c => c.GroupId == input.Id);
        if (hasConfigs)
        {
            throw Oops.Oh(ErrorCode.D1006);
        }

        await _chartsGroupRepository.DeleteAsync(entity);
    }

    #endregion


    /// <summary>
    /// 图表数据配置预览
    /// </summary>
    /// <param name="input">请求参数</param>
    /// <returns>键值对数据</returns>
    [HttpPost("/ChartsDataConfig/PreviewData")]
    public async Task<List<Dictionary<string, object>>> PreviewData([FromBody] ChartPreviewDataInput input)
    {
        if (string.IsNullOrEmpty(input.Type))
        {
            throw Oops.Oh("数据源类型不能为空！");
        }

        switch (input.Type?.ToLower())
        {
            case "table":
                // 解析参数
                TableContentNew? tableContent = input.Content == null ? null : JsonConvert.DeserializeObject<TableContentNew>(input.Content.ToJson());
                List<FieldItem>? fields = input.Fields;
                // 调用新的辅助方法处理表格数据
                return await ProcessTableDataPreviewAsync(tableContent, fields);
            case "sql":
                SqlQueryContent? sqlContent = input.Content == null ? null : JsonConvert.DeserializeObject<SqlQueryContent>(input.Content.ToJson());
                List<FieldItem>? sqlFields = input.Fields;
                Dictionary<string, object>? sqlParams = input.Params;
                return await ProcessSqlDataPreviewAsync(sqlContent, sqlFields, sqlParams);
            case "http":
                HttpRequestConfig? httpContent = input.Content == null ? null : JsonConvert.DeserializeObject<HttpRequestConfig>(input.Content.ToJson());
                List<FieldItem>? httpFields = input.Fields;
                Dictionary<string, object>? httpParams = input.Params;
                return await ProcessHttpDataPreviewAsync(httpContent, httpFields, httpParams);
            case "static":
                StaticDataContent? staticContent = input.Content == null ? null : JsonConvert.DeserializeObject<StaticDataContent>(input.Content.ToJson());
                List<FieldItem>? staticFields = input.Fields;
                Dictionary<string, object>? staticParams = input.Params;
                return await ProcessStaticDataPreviewAsync(staticContent, staticFields, staticParams);
            default:
                throw Oops.Oh($"Unsupported data source type: {input.Type}");
        }

    }

    /// <summary>
    /// 执行SQL查询
    /// </summary>
    /// <param name="input">SQL查询输入参数</param>
    /// <returns>查询结果</returns>
    [HttpPost("/ChartsDataConfig/ExecuteSQLQuery")]
    [DisplayName("执行SQL查询")]
    public async Task<SQLQueryOutput> ExecuteSQLQuery([FromBody] SQLQueryInput input)
    {
        // 检查参数
        if (string.IsNullOrEmpty(input.Id))
        {
            throw Oops.Oh("数据库连接ID不能为空");
        }

        // 直接获取数据库连接
        if (!long.TryParse(input.Id, out long dbLinkId))
        {
            throw Oops.Oh("无效的数据库ID格式！");
        }

        DbLink? dbLink = await _chartsGroupRepository.AsSugarClient().Queryable<DbLink>().FirstAsync(m => m.Id == dbLinkId);
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 处理SQL参数
        string executableSql = input.Sql;
        Dictionary<string, object>? queryParams = input.ParamsConfig;

        if (queryParams != null)
        {
            foreach (var param in queryParams)
            {
                // 替换 {key} 为 @key
                executableSql = executableSql.Replace("{" + param.Key + "}", "@" + param.Key);
            }
        }

        // 执行SQL查询
        var parameters = ConvertDictToParameters(queryParams);
        SqlSugarPagedList<dynamic> pagedData = await _changeDataBase.GetDataTablePage(dbLink, executableSql, 1, 1000, parameters);
        List<dynamic> rows = pagedData.Rows.ToList();

        // 准备结果
        SQLQueryOutput result = new SQLQueryOutput();

        // 如果没有数据，返回空结果
        if (rows.Count == 0)
        {
            return result;
        }

        // 获取列名（从第一行数据获取）
        IDictionary<string, object> firstRow = (IDictionary<string, object>)rows[0];
        result.Columns = firstRow.Keys.ToList();

        // 转换数据行
        foreach (var row in rows)
        {
            Dictionary<string, object> rowData = new Dictionary<string, object>();
            IDictionary<string, object> rowDict = (IDictionary<string, object>)row;

            foreach (var column in result.Columns)
            {
                rowData[column] = rowDict.ContainsKey(column) ? rowDict[column] : null;
            }

            result.Data.Add(rowData);
        }

        return result;
    }

    /// <summary>
    /// HTTP请求预览
    /// </summary>
    /// <param name="input">HTTP请求预览输入参数</param>
    /// <returns>HTTP请求预览结果</returns>
    [HttpPost("/ChartsDataConfig/HttpRequestPreview")]
    [DisplayName("HTTP请求预览")]
    public async Task<HttpRequestPreviewOutput> HttpRequestPreview([FromBody] HttpRequestPreviewInput input)
    {
        if (input.Config == null || string.IsNullOrEmpty(input.Config.Url))
        {
            throw Oops.Oh("请求URL不能为空");
        }

        // 构建HTTP客户端
        using HttpClient client = new HttpClient();

        // 设置超时
        client.Timeout = TimeSpan.FromSeconds(input.Config.Timeout > 0 ? input.Config.Timeout : 30);

        // 处理URL中的参数
        string url = input.Config.Url.Trim();

        // 替换URL中的参数
        if (input.ParamsJson != null && input.ParamsJson.Count > 0)
        {
            foreach (var param in input.ParamsJson)
            {
                url = url.Replace("{" + param.Key + "}", param.Value?.ToString() ?? string.Empty);
            }
        }

        // 添加查询参数
        if (input.Config.Query != null && input.Config.Query.Count > 0)
        {
            UriBuilder uriBuilder = new UriBuilder(url);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);

            foreach (var item in input.Config.Query)
            {
                // 替换参数
                string value = item.Value ?? string.Empty;
                if (input.ParamsJson != null && input.ParamsJson.Count > 0)
                {
                    foreach (var param in input.ParamsJson)
                    {
                        value = value.Replace("{" + param.Key + "}", param.Value?.ToString() ?? string.Empty);
                    }
                }

                query[item.Key] = value;
            }

            uriBuilder.Query = query.ToString();
            url = uriBuilder.Uri.ToString();
        }

        // 创建请求消息
        HttpRequestMessage request = new HttpRequestMessage(new HttpMethod(input.Config.Method), url);

        // 添加请求头
        if (input.Config.Header != null && input.Config.Header.Count > 0)
        {
            foreach (var header in input.Config.Header)
            {
                // 替换参数
                string value = header.Value ?? string.Empty;
                if (input.ParamsJson != null && input.ParamsJson.Count > 0)
                {
                    foreach (var param in input.ParamsJson)
                    {
                        value = value.Replace("{" + param.Key + "}", param.Value?.ToString() ?? string.Empty);
                    }
                }

                request.Headers.TryAddWithoutValidation(header.Key, value);
            }
        }

        // 处理认证
        if (input.Config.Auth != null && !string.IsNullOrEmpty(input.Config.AuthMode))
        {
            switch (input.Config.AuthMode.ToLower())
            {
                case "basic":
                    if (!string.IsNullOrEmpty(input.Config.Auth.User) && !string.IsNullOrEmpty(input.Config.Auth.Pass))
                    {
                        string credentials = Convert.ToBase64String(
                            Encoding.ASCII.GetBytes($"{input.Config.Auth.User}:{input.Config.Auth.Pass}"));
                        request.Headers.TryAddWithoutValidation("Authorization", $"Basic {credentials}");
                    }
                    break;
                case "bearer":
                    if (!string.IsNullOrEmpty(input.Config.Auth.Token))
                    {
                        request.Headers.TryAddWithoutValidation("Authorization", $"Bearer {input.Config.Auth.Token}");
                    }
                    break;
            }
        }

        // 添加请求体
        if (input.Config.Method.ToUpper() != "GET" && input.Config.Method.ToUpper() != "HEAD")
        {
            string bodyType = input.Config.BodyType?.ToLower() ?? "json";

            // 根据不同的Body类型和BodyType采取不同的处理方式
            if (input.Config.Body != null)
            {
                switch (bodyType)
                {
                    case "json":
                        string jsonBody;
                        if (input.Config.Body is string bodyStr)
                        {
                            jsonBody = bodyStr;
                        }
                        else
                        {
                            // 对象或数组序列化为JSON
                            jsonBody = JsonConvert.SerializeObject(input.Config.Body);
                        }

                        // 替换参数
                        jsonBody = ReplaceParams(jsonBody, input.ParamsJson);
                        request.Content = new StringContent(jsonBody, Encoding.UTF8, "application/json");
                        break;

                    case "urlencoded":
                        if (input.Config.Body is string urlencodedStr)
                        {
                            // 如果是字符串，直接使用
                            string formBody = urlencodedStr;

                            // 替换参数
                            formBody = ReplaceParams(formBody, input.ParamsJson);
                            request.Content = new StringContent(formBody, Encoding.UTF8, "application/x-www-form-urlencoded");
                        }
                        else if (input.Config.Body is IDictionary<string, object> formDict)
                        {
                            // 如果是字典对象，转换为表单数据
                            var formContent = new FormUrlEncodedContent(
                                formDict.Select(kv => new KeyValuePair<string, string>(
                                    kv.Key,
                                    ReplaceParams(kv.Value?.ToString() ?? string.Empty, input.ParamsJson)
                                ))
                            );
                            request.Content = formContent;
                        }
                        else if (input.Config.Body is IEnumerable<object> formList)
                        {
                            // 尝试转换为表单数据
                            var formData = new List<KeyValuePair<string, string>>();
                            try
                            {
                                // 尝试处理键值对列表
                                foreach (var item in formList)
                                {
                                    if (item is IDictionary<string, object> dictItem)
                                    {
                                        foreach (var kv in dictItem)
                                        {
                                            formData.Add(new KeyValuePair<string, string>(
                                                kv.Key,
                                                ReplaceParams(kv.Value?.ToString() ?? string.Empty, input.ParamsJson)
                                            ));
                                        }
                                    }
                                    else if (item is HttpKeyValuePair kvp)
                                    {
                                        // 处理我们自己定义的HttpKeyValuePair类型
                                        formData.Add(new KeyValuePair<string, string>(
                                            kvp.Key,
                                            ReplaceParams(kvp.Value ?? string.Empty, input.ParamsJson)
                                        ));
                                    }
                                }

                                if (formData.Count > 0)
                                {
                                    request.Content = new FormUrlEncodedContent(formData);
                                }
                                else
                                {
                                    // 无法直接转换，序列化为JSON
                                    string formBody = JsonConvert.SerializeObject(input.Config.Body);
                                    formBody = ReplaceParams(formBody, input.ParamsJson);
                                    request.Content = new StringContent(formBody, Encoding.UTF8, "application/x-www-form-urlencoded");
                                }
                            }
                            catch
                            {
                                // 转换失败，序列化为JSON
                                string formBody = JsonConvert.SerializeObject(input.Config.Body);
                                formBody = ReplaceParams(formBody, input.ParamsJson);
                                request.Content = new StringContent(formBody, Encoding.UTF8, "application/x-www-form-urlencoded");
                            }
                        }
                        else
                        {
                            // 其他情况，序列化为JSON
                            string formBody = JsonConvert.SerializeObject(input.Config.Body);
                            formBody = ReplaceParams(formBody, input.ParamsJson);
                            request.Content = new StringContent(formBody, Encoding.UTF8, "application/x-www-form-urlencoded");
                        }
                        break;

                    case "xml":
                        string xmlBody;
                        if (input.Config.Body is string xmlStr)
                        {
                            xmlBody = xmlStr;
                        }
                        else
                        {
                            // 对象序列化为字符串
                            xmlBody = JsonConvert.SerializeObject(input.Config.Body);
                        }

                        // 替换参数
                        xmlBody = ReplaceParams(xmlBody, input.ParamsJson);
                        request.Content = new StringContent(xmlBody, Encoding.UTF8, "application/xml");
                        break;

                    case "text":
                    default:
                        string textBody;
                        if (input.Config.Body is string textStr)
                        {
                            textBody = textStr;
                        }
                        else
                        {
                            // 对象转换为字符串
                            textBody = input.Config.Body.ToString();
                        }

                        // 替换参数
                        textBody = ReplaceParams(textBody, input.ParamsJson);
                        request.Content = new StringContent(textBody, Encoding.UTF8, "text/plain");
                        break;
                }
            }
        }

        try
        {
            // 发送请求
            HttpResponseMessage response = await client.SendAsync(request);

            // 获取响应内容
            string responseBody = await response.Content.ReadAsStringAsync();

            // 构建返回结果
            HttpRequestPreviewOutput result = new HttpRequestPreviewOutput
            {
                Body = responseBody
            };

            // 解析JSON数据
            if (!string.IsNullOrEmpty(responseBody))
            {
                try
                {
                    // 解析JSON
                    JToken json = JToken.Parse(responseBody);

                    // 判断是否指定了JsonPath
                    if (!string.IsNullOrEmpty(input.Config.JsonPath))
                    {
                        // 提取指定路径的数据
                        if (input.Config.JsonPath.StartsWith("$"))
                        {
                            // 使用JsonPath提取
                            IEnumerable<JToken> tokens = json.SelectTokens(input.Config.JsonPath);
                            if (tokens.Any())
                            {
                                result.Data = tokens.First().ToObject<object>();
                            }
                        }
                        else
                        {
                            // 简单属性提取
                            if (json is JObject jObject && jObject.ContainsKey(input.Config.JsonPath))
                            {
                                result.Data = jObject[input.Config.JsonPath].ToObject<object>();
                            }
                        }
                    }
                    else
                    {
                        // JsonPath为空，使用整个JSON对象
                        result.Data = json.ToObject<object>();
                    }
                }
                catch (Exception ex)
                {
                    // 解析JSON异常，记录日志但不影响返回
                    Console.WriteLine($"解析JSON失败: {ex.Message}");
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"HTTP请求失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 解析文件为Luckysheet格式
    /// </summary>
    /// <param name="input">包含文件和类型的输入参数</param>
    /// <returns>Luckysheet可用的数据格式</returns>
    [HttpPost("/ChartsDataConfig/ParseFileToLuckysheet")]
    [DisplayName("解析文件为Luckysheet格式")]
    public async Task<List<LuckysheetModel>> ParseFileToLuckysheet([FromForm] ParseFileToLuckysheetInput input)
    {
        if (input.File == null || input.File.Length == 0)
        {
            throw Oops.Oh("请上传有效的文件");
        }

        // 检查文件类型
        string fileType = input.Type?.ToLower() ?? Path.GetExtension(input.File.FileName).ToLowerInvariant();
        if (string.IsNullOrEmpty(fileType))
        {
            fileType = ".csv"; // 默认CSV类型
        }
        else if (!fileType.StartsWith("."))
        {
            fileType = "." + fileType;
        }

        // 创建结果模型
        var result = new List<LuckysheetModel>();
        var sheet = new LuckysheetModel
        {
            Name = "Sheet1",
            Status = 1,
            Order = 0,
            Index = 0,
            Data = new List<List<object>>()
        };

        // 读取文件内容
        using (var streamReader = new StreamReader(input.File.OpenReadStream()))
        {
            string content = await streamReader.ReadToEndAsync();

            // 解析CSV内容
            var rows = ParseCsvContent(content);

            // 为每行创建数据
            foreach (var row in rows)
            {
                var rowData = new List<object>();
                foreach (var cell in row)
                {
                    rowData.Add(string.IsNullOrEmpty(cell) ? null : cell);
                }

                // 确保每行都有足够的列（至少18列，根据示例）
                while (rowData.Count < 18)
                {
                    rowData.Add(null);
                }

                sheet.Data.Add(rowData);
            }

            // 确保有足够的空行（根据示例至少有100行）
            while (sheet.Data.Count < 100)
            {
                sheet.Data.Add(new List<object>());
            }
        }

        result.Add(sheet);
        return result;
    }

    /// <summary>
    /// 解析CSV内容为字符串二维数组
    /// </summary>
    /// <param name="csvContent">CSV文件内容</param>
    /// <returns>解析后的数据</returns>
    private List<List<string>> ParseCsvContent(string csvContent)
    {
        var result = new List<List<string>>();

        // 按行分割内容
        string[] lines = csvContent.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None);

        foreach (var line in lines)
        {
            if (string.IsNullOrEmpty(line))
            {
                continue;
            }

            // 简单的CSV解析，不处理引号等特殊情况
            string[] cells = line.Split(',');
            result.Add(new List<string>(cells));
        }

        return result;
    }

    #region 辅助方法

    /// <summary>
    /// 静态数据内容类
    /// </summary>
    public class StaticDataContent
    {
        /// <summary>
        /// JSON数据
        /// </summary>
        public string? Json { get; set; }
    }

    /// <summary>
    /// 处理静态数据源的预览逻辑
    /// </summary>
    private async Task<List<Dictionary<string, object>>> ProcessStaticDataPreviewAsync(StaticDataContent? staticContent, List<FieldItem>? fields, Dictionary<string, object>? queryParams)
    {
        // 基本参数检查，这依然会抛出异常
        if (staticContent == null || string.IsNullOrEmpty(staticContent.Json))
        {
            throw Oops.Oh("静态数据源配置不正确！");
        }

        // 解析JSON数据阶段 - 不抛异常，失败时返回空列表
        List<dynamic> rows = new List<dynamic>();
        bool parseSuccess = false;

        // 尝试解析为数组
        try
        {
            // 尝试解析为对象数组
            var dataArray = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(staticContent.Json);
            if (dataArray != null && dataArray.Count > 0)
            {
                foreach (var item in dataArray)
                {
                    rows.Add(item);
                }
                parseSuccess = true;
            }
        }
        catch
        {
            // 解析失败，尝试下一种方式
        }

        if (!parseSuccess)
        {
            try
            {
                // 尝试解析为JArray
                JArray jArray = JArray.Parse(staticContent.Json);
                foreach (var item in jArray)
                {
                    if (item is JObject jObj)
                    {
                        rows.Add(jObj.ToObject<Dictionary<string, object>>());
                    }
                    else
                    {
                        // 非对象元素，包装为值对象
                        Dictionary<string, object> dict = new Dictionary<string, object>
                        {
                            { "value", item.ToObject<object>() }
                        };
                        rows.Add(dict);
                    }
                }
                parseSuccess = true;
            }
            catch
            {
                // 解析失败，尝试下一种方式
            }
        }

        if (!parseSuccess)
        {
            try
            {
                // 尝试解析为单个对象
                var dataObject = JsonConvert.DeserializeObject<Dictionary<string, object>>(staticContent.Json);
                if (dataObject != null)
                {
                    rows.Add(dataObject);
                    parseSuccess = true;
                }
            }
            catch
            {
                // 解析失败，尝试下一种方式
            }
        }

        if (!parseSuccess)
        {
            try
            {
                // 作为最后尝试，直接存储原始文本
                Dictionary<string, object> dict = new Dictionary<string, object>
                {
                    { "text", staticContent.Json }
                };
                rows.Add(dict);
                parseSuccess = true;
            }
            catch
            {
                // 所有解析尝试都失败
                Console.WriteLine("静态数据解析失败，返回空列表");
                return new List<Dictionary<string, object>>();
            }
        }

        // 如果解析失败返回空列表
        if (!parseSuccess || rows.Count == 0)
        {
            return new List<Dictionary<string, object>>();
        }

        // 处理阶段 - 处理阶段的错误会抛出异常

        // 应用参数替换（如果需要）
        if (queryParams != null && queryParams.Count > 0)
        {
            // 这里可以添加参数处理逻辑，如果静态数据需要处理参数
        }

        try
        {
            // 判断是否需要使用Jint处理字段
            if (fields == null || fields.Count == 0)
            {
                // 如果没有字段配置，直接返回原始数据
                var resultList = new List<Dictionary<string, object>>();
                foreach (var row in rows)
                {
                    if (row is IDictionary<string, object> dictRow)
                    {
                        // 清理字典再添加
                        resultList.Add(CleanDictionaryForSerialization(dictRow));
                    }
                    else
                    {
                        try
                        {
                            // 尝试将动态对象转换为字典
                            var convertedDict = JObject.FromObject(row).ToObject<Dictionary<string, object>>();
                            // 清理字典再添加
                            resultList.Add(CleanDictionaryForSerialization(convertedDict));
                        }
                        catch (Exception ex)
                        {
                            // 单行转换失败，记录日志但不影响整体处理
                            Console.WriteLine($"行数据转换失败: {ex.Message}");
                            resultList.Add(new Dictionary<string, object> { { "value", row.ToString() } });
                        }
                    }
                }
                return resultList;
            }

            // 有字段配置，使用Jint处理
            return ProcessDataRowsWithJint(rows, fields);
        }
        catch (Exception ex)
        {
            // 处理阶段的错误会抛出异常
            throw Oops.Oh($"静态数据处理失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理表格类型数据源的预览逻辑
    /// </summary>
    private async Task<List<Dictionary<string, object>>> ProcessTableDataPreviewAsync(TableContentNew? tableContent, List<FieldItem>? fields)
    {
        if (tableContent == null || tableContent.DatabaseId.HasValue == false || string.IsNullOrEmpty(tableContent.TableName))
        {
            throw Oops.Oh("数据源配置不正确！");
        }

        // 获取数据库连接
        long dbLinkId = tableContent.DatabaseId ?? 0;
        DbLink? dbLink = await _chartsGroupRepository.AsSugarClient().Queryable<DbLink>().FirstAsync(m => m.Id == dbLinkId);
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 构建SQL查询
        StringBuilder dbSql = new();
        dbSql.AppendFormat("SELECT * FROM {0} WHERE 1=1", tableContent.TableName);

        // 获取数据
        SqlSugarPagedList<dynamic> pagedData = await _changeDataBase.GetDataTablePage(dbLink, dbSql.ToString(), 1, 200);
        List<dynamic> rows = pagedData.Rows.ToList();

        // 调用通用 Jint 处理方法
        return ProcessDataRowsWithJint(rows, fields);
    }

    /// <summary>
    /// 处理SQL类型数据源的预览逻辑
    /// </summary>
    private async Task<List<Dictionary<string, object>>> ProcessSqlDataPreviewAsync(SqlQueryContent? sqlContent, List<FieldItem>? fields, Dictionary<string, object>? queryParams)
    {
        if (sqlContent == null || string.IsNullOrEmpty(sqlContent.Sql) || string.IsNullOrEmpty(sqlContent.DatabaseId))
        {
            throw Oops.Oh("SQL数据源配置不正确！");
        }

        // 获取数据库连接
        if (!long.TryParse(sqlContent.DatabaseId, out long dbLinkId))
        {
            throw Oops.Oh("无效的数据库ID格式！");
        }
        DbLink? dbLink = await _chartsGroupRepository.AsSugarClient().Queryable<DbLink>().FirstAsync(m => m.Id == dbLinkId);
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 转换SQL占位符并准备参数
        string executableSql = sqlContent.Sql;
        if (queryParams != null)
        {
            foreach (var param in queryParams)
            {
                // 替换 {key} 为 @key
                executableSql = executableSql.Replace("{" + param.Key + "}", "@" + param.Key);
            }
        }

        // 获取数据
        // 注意：SqlSugar 可能需要显式传递参数对象，而不是依赖字符串替换后的参数名匹配
        // 确保 _changeDataBase.GetDataTablePage 支持 parameters 参数
        var parameters = ConvertDictToParameters(queryParams);
        SqlSugarPagedList<dynamic> pagedData = await _changeDataBase.GetDataTablePage(dbLink, executableSql, 1, 200, parameters);
        List<dynamic> dataRows = pagedData.Rows.ToList();

        // 调用通用 Jint 处理方法
        return ProcessDataRowsWithJint(dataRows, fields);
    }

    /// <summary>
    /// 处理HTTP类型数据源的预览逻辑
    /// </summary>
    private async Task<List<Dictionary<string, object>>> ProcessHttpDataPreviewAsync(HttpRequestConfig? httpConfig, List<FieldItem>? fields, Dictionary<string, object>? queryParams)
    {
        if (httpConfig == null || string.IsNullOrEmpty(httpConfig.Url))
        {
            throw Oops.Oh("HTTP数据源配置不正确！");
        }

        // 构建HTTP请求输入对象
        HttpRequestPreviewInput requestInput = new HttpRequestPreviewInput
        {
            Config = httpConfig,
            ParamsJson = queryParams ?? new Dictionary<string, object>()
        };

        try
        {
            // 利用现有方法发送HTTP请求
            HttpRequestPreviewOutput response = await HttpRequestPreview(requestInput);

            // 如果没有响应数据
            if (response.Data == null && string.IsNullOrEmpty(response.Body))
            {
                return new List<Dictionary<string, object>>();
            }

            List<dynamic> rows = new List<dynamic>();

            // 优先使用解析后的数据
            if (response.Data != null)
            {
                // 如果解析结果是数组
                if (response.Data is IEnumerable<object> dataArray && !(response.Data is string))
                {
                    foreach (var item in dataArray)
                    {
                        if (item is IDictionary<string, object> dictItem)
                        {
                            rows.Add(dictItem);
                        }
                        else if (item is JObject jobj)
                        {
                            // 处理JObject，确保所有属性都被转换
                            rows.Add(jobj.ToObject<Dictionary<string, object>>());
                        }
                        else
                        {
                            // 不是键值对的项，尝试转换
                            try
                            {
                                // 尝试将对象转换为字典
                                var objDict = JObject.FromObject(item).ToObject<Dictionary<string, object>>();
                                if (objDict != null && objDict.Count > 0)
                                {
                                    rows.Add(objDict);
                                }
                                else
                                {
                                    Dictionary<string, object> dict = new Dictionary<string, object>
                                    {
                                        { "value", item }
                                    };
                                    rows.Add(dict);
                                }
                            }
                            catch
                            {
                                // 转换失败，使用简单包装
                                Dictionary<string, object> dict = new Dictionary<string, object>
                                {
                                    { "value", item }
                                };
                                rows.Add(dict);
                            }
                        }
                    }
                }
                // 如果解析结果是单个对象
                else if (response.Data is IDictionary<string, object> dataObject)
                {
                    rows.Add(dataObject);
                }
                // 如果是JObject
                else if (response.Data is JObject jObject)
                {
                    // 转换为Dictionary确保所有属性被包含
                    rows.Add(jObject.ToObject<Dictionary<string, object>>());
                }
                // 其他类型，作为单值包装
                else
                {
                    Dictionary<string, object> dict = new Dictionary<string, object>
                    {
                        { "value", response.Data }
                    };
                    rows.Add(dict);
                }
            }
            // 尝试解析响应体为JSON
            else if (!string.IsNullOrEmpty(response.Body))
            {
                try
                {
                    JToken json = JToken.Parse(response.Body);

                    // 如果是数组
                    if (json is JArray array)
                    {
                        foreach (var item in array)
                        {
                            if (item is JObject obj)
                            {
                                rows.Add(obj.ToObject<Dictionary<string, object>>());
                            }
                            else
                            {
                                Dictionary<string, object> dict = new Dictionary<string, object>
                                {
                                    { "value", item.ToObject<object>() }
                                };
                                rows.Add(dict);
                            }
                        }
                    }
                    // 如果是对象
                    else if (json is JObject obj)
                    {
                        rows.Add(obj.ToObject<Dictionary<string, object>>());
                    }
                    // 其他类型，包装为值
                    else
                    {
                        Dictionary<string, object> dict = new Dictionary<string, object>
                        {
                            { "value", json.ToObject<object>() }
                        };
                        rows.Add(dict);
                    }
                }
                catch
                {
                    // 无法解析为JSON，返回原始文本
                    Dictionary<string, object> dict = new Dictionary<string, object>
                    {
                        { "text", response.Body }
                    };
                    rows.Add(dict);
                }
            }

            // 判断是否需要使用Jint处理字段
            if (fields == null || fields.Count == 0)
            {
                // 如果没有字段配置，直接返回原始数据
                var resultList = new List<Dictionary<string, object>>();
                foreach (var row in rows)
                {
                    if (row is IDictionary<string, object> dictRow)
                    {
                        // 清理字典再添加
                        resultList.Add(CleanDictionaryForSerialization(dictRow));
                    }
                    else
                    {
                        try
                        {
                            // 尝试将动态对象转换为字典
                            var convertedDict = JObject.FromObject(row).ToObject<Dictionary<string, object>>();
                            // 清理字典再添加
                            resultList.Add(CleanDictionaryForSerialization(convertedDict));
                        }
                        catch (Exception ex)
                        {
                            // 单行转换失败，记录日志但不影响整体处理
                            Console.WriteLine($"行数据转换失败: {ex.Message}");
                            resultList.Add(new Dictionary<string, object> { { "value", row.ToString() } });
                        }
                    }
                }
                return resultList;
            }

            // 有字段配置，使用Jint处理
            return ProcessDataRowsWithJint(rows, fields);
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"HTTP数据获取失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 使用 Jint 处理数据行（计算字段等）
    /// </summary>
    private List<Dictionary<string, object>> ProcessDataRowsWithJint(List<dynamic> rows, List<FieldItem>? fields)
    {
        List<Dictionary<string, object>> result = new();

        // 创建Jint引擎
        Jint.Engine jintEngine = new Jint.Engine(options =>
        {
            options.TimeoutInterval(TimeSpan.FromSeconds(5));
        });

        foreach (var row in rows)
        {
            Dictionary<string, object> item = new();

            // 转换row为扩展字典，方便Jint访问
            IDictionary<string, object> rowDict = ((IDictionary<string, object>)row);

            if (fields == null || fields.Count == 0)
            {
                // 如果未配置字段，则返回所有列数据
                foreach (var prop in rowDict)
                {
                    item[prop.Key] = prop.Value;
                }
            }
            else
            {
                // 处理所有字段
                foreach (var field in fields)
                {
                    if (field.IsCalc && !string.IsNullOrEmpty(field.Express))
                    {
                        try
                        {
                            // 添加所有数据行属性到Jint引擎
                            foreach (var prop in rowDict)
                            {
                                jintEngine.SetValue(prop.Key, prop.Value);
                            }

                            // 执行表达式
                            var calcResult = jintEngine.Evaluate(field.Express);
                            item[field.Name ?? "undefined"] = calcResult.ToObject();
                        }
                        catch (Exception ex)
                        {
                            item[field.Name ?? "undefined"] = $"计算错误: {ex.Message}";
                        }
                    }
                    else if (!string.IsNullOrEmpty(field.Column) && rowDict.ContainsKey(field.Column))
                    {
                        // 直接使用列值
                        item[field.Name ?? field.Column] = rowDict[field.Column];
                    }
                    else if (!string.IsNullOrEmpty(field.Name))
                    {
                        // 字段配置有误，返回null
                        item[field.Name] = null;
                    }
                }
            }

            // 在添加到结果前清理字典
            result.Add(CleanDictionaryForSerialization(item));
        }

        return result;
    }

    /// <summary>
    ///     构建树形结构
    /// </summary>
    /// <param name="allNodes">所有节点</param>
    /// <param name="parentId">父节点Id</param>
    /// <returns>树形结构</returns>
    private static List<ChartsDataGroupOutput> BuildTree(List<ChartsDataGroupOutput> allNodes, string parentId)
    {
        // 找出根节点
        List<ChartsDataGroupOutput> rootNodes = allNodes
            .Where(x => x.ParentId == parentId || string.IsNullOrEmpty(x.ParentId))
            .ToList();

        // 从所有节点中移除根节点
        allNodes = allNodes
            .Where(x => x.ParentId != parentId && !string.IsNullOrEmpty(x.ParentId))
            .ToList();

        // 递归处理每个根节点
        foreach (ChartsDataGroupOutput rootNode in rootNodes)
        {
            rootNode.HasChildren = HasChildren(allNodes, rootNode.Id);
            if (rootNode.HasChildren)
            {
                rootNode.Children = GetChildren(allNodes, rootNode.Id);
                rootNode.Num = rootNode.Children.Count;
            }
            else
            {
                rootNode.IsLeaf = true;
                rootNode.Children = new List<ChartsDataGroupOutput>();
            }
        }

        return rootNodes;
    }

    /// <summary>
    ///     获取子节点
    /// </summary>
    /// <param name="nodes">所有节点</param>
    /// <param name="parentId">父节点Id</param>
    /// <returns>子节点列表</returns>
    private static List<ChartsDataGroupOutput> GetChildren(List<ChartsDataGroupOutput> nodes, string parentId)
    {
        // 找出当前节点的子节点
        List<ChartsDataGroupOutput> children = nodes
            .Where(x => x.ParentId == parentId)
            .ToList();

        // 从所有节点中移除这些子节点
        List<ChartsDataGroupOutput> remainingNodes = nodes
            .Where(x => x.ParentId != parentId)
            .ToList();

        // 递归处理每个子节点
        foreach (ChartsDataGroupOutput child in children)
        {
            child.HasChildren = HasChildren(remainingNodes, child.Id);
            if (child.HasChildren)
            {
                child.Children = GetChildren(remainingNodes, child.Id);
                child.Num = child.Children.Count;
            }
            else
            {
                child.IsLeaf = true;
                child.Children = new List<ChartsDataGroupOutput>();
            }
        }

        return children;
    }

    /// <summary>
    ///     判断是否有子节点
    /// </summary>
    /// <param name="nodes">所有节点</param>
    /// <param name="nodeId">节点Id</param>
    /// <returns>是否有子节点</returns>
    private static bool HasChildren(List<ChartsDataGroupOutput> nodes, string nodeId)
    {
        return nodes.Any(x => x.ParentId == nodeId);
    }

    /// <summary>
    /// 将字典转换为SugarParameter数组
    /// </summary>
    /// <param name="dict">字典</param>
    /// <returns>SugarParameter数组</returns>
    private SugarParameter[] ConvertDictToParameters(Dictionary<string, object>? dict)
    {
        if (dict == null || dict.Count == 0)
        {
            return Array.Empty<SugarParameter>();
        }

        return dict.Select(kvp => new SugarParameter(kvp.Key, kvp.Value)).ToArray();
    }

    /// <summary>
    /// 替换参数
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="paramsJson">参数JSON</param>
    /// <returns>替换后的字符串</returns>
    private string ReplaceParams(string input, Dictionary<string, object>? paramsJson)
    {
        if (paramsJson == null || paramsJson.Count == 0)
        {
            return input;
        }

        foreach (var param in paramsJson)
        {
            input = input.Replace("{" + param.Key + "}", param.Value?.ToString() ?? string.Empty);
        }

        return input;
    }

    /// <summary>
    /// 清理字典以确保安全序列化为JSON
    /// </summary>
    /// <param name="dict">需要清理的字典</param>
    /// <returns>清理后的字典</returns>
    private Dictionary<string, object> CleanDictionaryForSerialization(IDictionary<string, object> dict)
    {
        Dictionary<string, object> cleanDict = new Dictionary<string, object>();

        if (dict == null) return cleanDict;

        foreach (var pair in dict)
        {
            // 跳过null键
            if (pair.Key == null)
                continue;

            string key = pair.Key.ToString();

            // 处理值
            object value = pair.Value;

            // 处理值为字典的情况，递归清理
            if (value is IDictionary<string, object> nestedDict)
            {
                value = CleanDictionaryForSerialization(nestedDict);
            }
            // 处理值为字典列表的情况
            else if (value is IEnumerable<object> collection && !(value is string))
            {
                List<object> cleanList = new List<object>();
                foreach (var item in collection)
                {
                    if (item is IDictionary<string, object> itemDict)
                    {
                        cleanList.Add(CleanDictionaryForSerialization(itemDict));
                    }
                    else
                    {
                        cleanList.Add(item);
                    }
                }
                value = cleanList;
            }

            // 处理key重复的情况
            if (cleanDict.ContainsKey(key))
            {
                int counter = 1;
                string newKey = $"{key}_{counter}";
                while (cleanDict.ContainsKey(newKey))
                {
                    counter++;
                    newKey = $"{key}_{counter}";
                }
                key = newKey;
            }

            cleanDict[key] = value;
        }

        return cleanDict;
    }
    #endregion
}