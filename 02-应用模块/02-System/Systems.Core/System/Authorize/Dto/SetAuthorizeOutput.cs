namespace Systems.Core;

/// <summary>
///     授权输出
/// </summary>
public class SetAuthorizeOutput
{
  /// <summary>
  ///     Sn
  /// </summary>
  public string? Sn { get; set; }

  /// <summary>
  ///     设备数量
  /// </summary>
  public short DeviceNumber { get; set; }

  /// <summary>
  ///     属性数量
  /// </summary>
  public short VariableNumber { get; set; }

  /// <summary>
  ///     状态
  /// </summary>
  public string Status => string.IsNullOrEmpty(EndTime) ? "未授权" : System.DateTime.Now >= Convert.ToDateTime(EndTime) ? "过期" : "正常";

  /// <summary>
  ///     到期时间
  /// </summary>
  public string EndTime { get; set; }

  /// <summary>
  ///     后端版本号
  /// </summary>
  public string Version { get; set; }
}

/// <summary>
///     授权输入
/// </summary>
public class SetAuthorizeInput
{
  /// <summary>
  ///     授权
  /// </summary>
  public string EncryptedText { get; set; }
}
