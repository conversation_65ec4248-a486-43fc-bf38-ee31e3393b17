namespace Systems.Core;

/// <summary>
///     系统访问日志服务
/// </summary>
[ApiDescriptionSettings("系统服务",Order = 340)]
public class SysLogVisService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SysLogVis> _sysLogVisRep;

    public SysLogVisService(ISqlSugarRepository<SysLogVis> sysLogVisRep)
    {
        _sysLogVisRep = sysLogVisRep;
    }

    /// <summary>
    ///     获取访问日志分页列表
    /// </summary>
    /// <returns></returns>
    [SuppressMonitor]
    [HttpGet("/sysVislog/page")]
    [DisplayName("获取访问日志分页列表")]
    public async Task<SqlSugarPagedList<SysLogVis>> Page([FromQuery] PageLogInput input)
    {
        return await _sysLogVisRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchBeginTime), u => u.CreatedTime >= Convert.ToDateTime(input.SearchBeginTime))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchEndTime), u => u.CreatedTime <= Convert.ToDateTime(input.SearchEndTime))
            .OrderBy(u => u.CreatedTime, OrderByType.Desc)
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     清空访问日志
    /// </summary>
    /// <returns></returns>
    [HttpPost("/sysVislog/delete")]
    [DisplayName("清空访问日志")]
    public async Task<bool> Clear()
    {
        return await _sysLogVisRep.DeleteAsync(u => u.Id > 0);
    }
}