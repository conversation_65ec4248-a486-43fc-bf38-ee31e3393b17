namespace Systems.Core;

/// <summary>
///     系统差异日志服务
/// </summary>
[ApiDescriptionSettings("系统服务",Order = 330)]
public class SysLogDiffService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SysLogDiff> _sysLogDiffRep;

    public SysLogDiffService(ISqlSugarRepository<SysLogDiff> sysLogDiffRep)
    {
        _sysLogDiffRep = sysLogDiffRep;
    }

    /// <summary>
    ///     获取差异日志分页列表
    /// </summary>
    /// <returns></returns>
    [SuppressMonitor]
    [HttpGet("/sysDiffLog/page")]
    [DisplayName("获取差异日志分页列表")]
    public async Task<SqlSugarPagedList<SysLogDiff>> Page([FromQuery] PageLogInput input)
    {
        return await _sysLogDiffRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchBeginTime), u => u.CreatedTime >= Convert.ToDateTime(input.SearchBeginTime))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchEndTime), u => u.CreatedTime <= Convert.ToDateTime(input.SearchEndTime))
            .OrderBy(u => u.CreatedTime, OrderByType.Desc)
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     清空差异日志
    /// </summary>
    /// <returns></returns>
    [HttpPost("/sysDiffLog/delete")]
    [DisplayName("清空差异日志")]
    public async Task<bool> Clear()
    {
        return await _sysLogDiffRep.DeleteAsync(u => u.Id > 0);
    }
}