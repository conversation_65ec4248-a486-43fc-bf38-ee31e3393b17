namespace Systems.Entity;

/// <summary>
///     单据规则
/// </summary>
[SugarTable("BASE_BILL_RULE")]
public class BillRuleEntity : EntityTenant
{
    /// <summary>
    ///     单据名称.
    /// </summary>
    [SugarColumn(ColumnName = "F_FULL_NAME")]
    public string FullName { get; set; }

    /// <summary>
    ///     单据编码.
    /// </summary>
    [SugarColumn(ColumnName = "F_EN_CODE")]
    public string EnCode { get; set; }

    /// <summary>
    ///     单据前缀.
    /// </summary>
    [SugarColumn(ColumnName = "F_PREFIX")]
    public string Prefix { get; set; }

    /// <summary>
    ///     日期格式.
    /// </summary>
    [SugarColumn(ColumnName = "F_DATE_FORMAT")]
    public string DateFormat { get; set; }

    /// <summary>
    ///     流水位数.
    /// </summary>
    [SugarColumn(ColumnName = "F_DIGIT")]
    public int? Digit { get; set; }

    /// <summary>
    ///     流水起始.
    /// </summary>
    [SugarColumn(ColumnName = "F_START_NUMBER")]
    public string StartNumber { get; set; }

    /// <summary>
    ///     流水范例.
    /// </summary>
    [SugarColumn(ColumnName = "F_EXAMPLE")]
    public string Example { get; set; }

    /// <summary>
    ///     当前流水号.
    /// </summary>
    [SugarColumn(ColumnName = "F_THIS_NUMBER")]
    public int? ThisNumber { get; set; } = 0;

    /// <summary>
    ///     输出流水号.
    /// </summary>
    [SugarColumn(ColumnName = "F_OUTPUT_NUMBER")]
    public string? OutputNumber { get; set; }

    /// <summary>
    ///     描述.
    /// </summary>
    [SugarColumn(ColumnName = "F_DESCRIPTION")]
    public string? Description { get; set; }

    /// <summary>
    ///     分类id.
    /// </summary>
    [SugarColumn(ColumnName = "F_CATEGORY")]
    public long Category { get; set; }
    
    /// <summary>
    /// 获取或设置 启用标识
    /// 0-禁用,1-启用.
    /// </summary>
    [SugarColumn(ColumnName = "F_ENABLED_MARK", ColumnDescription = "启用标识")]
    public int? EnabledMark { get; set; }
    
}