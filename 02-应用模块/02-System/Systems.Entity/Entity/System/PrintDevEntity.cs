namespace Systems.Entity;

/// <summary>
///     打印模板配置
/// </summary>
[SugarTable("BASE_PRINT_TEMPLATE")]
public class PrintDevEntity : EntityTenant
{
    /// <summary>
    ///     名称.
    /// </summary>
    [SugarColumn(ColumnName = "F_FULL_NAME")]
    public string FullName { get; set; }

    /// <summary>
    ///     编码.
    /// </summary>
    [SugarColumn(ColumnName = "F_EN_CODE")]
    public string EnCode { get; set; }

    /// <summary>
    ///     分类.
    /// </summary>
    [SugarColumn(ColumnName = "F_CATEGORY")]
    public long Category { get; set; }

    /// <summary>
    ///     状态：0-未发布，1-已发布，2-已修改.
    /// </summary>
    [SugarColumn(ColumnName = "F_STATE")]
    public int? State { get; set; }

    /// <summary>
    ///     描述.
    /// </summary>
    [SugarColumn(ColumnName = "F_DESCRIPTION")]
    public string? Description { get; set; }
    
    /// <summary>
    /// 排序码.
    /// </summary>
    [SugarColumn(ColumnName = "F_SORT_CODE", ColumnDescription = "排序码")]
    public virtual long? SortCode { get; set; }
    
    /// <summary>
    /// 获取或设置 删除标志.
    /// </summary>
    [SugarColumn(ColumnName = "F_DELETE_MARK", ColumnDescription = "删除标志")]
    public virtual int? DeleteMark { get; set; }
}