namespace Systems.Entity;

/// <summary>
///     数据集.
/// </summary>
[SugarTable("BASE_DATA_SET")]
public class DataSetEntity : EntityTenant
{
    /// <summary>
    ///     关联数据类型.
    /// </summary>
    [SugarColumn(ColumnName = "F_OBJECT_TYPE")]
    public string ObjectType { get; set; }

    /// <summary>
    ///     关联的数据id.
    /// </summary>
    [SugarColumn(ColumnName = "F_OBJECT_ID")]
    public long ObjectId { get; set; }

    /// <summary>
    ///     数据集名称.
    /// </summary>
    [SugarColumn(ColumnName = "F_FULL_NAME")]
    public string FullName { get; set; }

    /// <summary>
    ///     连接id.
    /// </summary>
    [SugarColumn(ColumnName = "F_DB_LINK_ID")]
    public long DbLinkId { get; set; }

    /// <summary>
    ///     数据sql语句.
    /// </summary>
    [SugarColumn(ColumnName = "F_DATA_CONFIG_JSON")]
    public string DataConfigJson { get; set; }

    /// <summary>
    ///     输入参数.
    /// </summary>
    [SugarColumn(ColumnName = "F_PARAMETER_JSON")]
    public string ParameterJson { get; set; }

    /// <summary>
    ///     显示字段.
    /// </summary>
    [SugarColumn(ColumnName = "F_FIELD_JSON")]
    public string FieldJson { get; set; }

    /// <summary>
    ///     描述.
    /// </summary>
    [SugarColumn(ColumnName = "F_DESCRIPTION")]
    public string? Description { get; set; }
    
    /// <summary>
    /// 排序码.
    /// </summary>
    [SugarColumn(ColumnName = "F_SORT_CODE", ColumnDescription = "排序码")]
    public virtual long? SortCode { get; set; }
}