using Systems.Entity;

namespace IotPlatform.Core.Entity;

/// <summary>
///     用户快捷菜单表
/// </summary>
[SugarTable("system_shortcutMenu", "用户快捷菜单表")]
public class SysShortcutMenu : Systems.Entity.EntityBaseId
{
    /// <summary>
    ///     用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = " 用户Id")]
    public long SysUserId { get; set; }

    /// <summary>
    ///     一对一引用（系统用户）
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(SysUserId))]
    public Systems.Entity.SysUser SysUser { get; set; }

    /// <summary>
    ///     菜单Id
    /// </summary>
    [SugarColumn(ColumnDescription = " 菜单Id")]
    public long SysMenuId { get; set; }

    /// <summary>
    ///     一对一引用（系统菜单）
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(SysMenuId))]
    public SysMenu SysMenu { get; set; }
}