using Systems.Entity.Model.System.DataSet;

namespace Systems.Entity.Model.System;

/// <summary>
///     打印模板数据集模型.
/// </summary>
[SuppressSniffer]
public class PrintDevDataSetModel
{
    /// <summary>
    ///     id.
    /// </summary>
    public long id { get; set; }

    /// <summary>
    ///     名称.
    /// </summary>
    public string fullName { get; set; }

    /// <summary>
    ///     数据连接id.
    /// </summary>
    public long dbLinkId { get; set; }

    /// <summary>
    ///     数据sql语句.
    /// </summary>
    public string dataConfigJson { get; set; }

    /// <summary>
    ///     输入参数.
    /// </summary>
    public string parameterJson { get; set; }

    /// <summary>
    ///     显示字段.
    /// </summary>
    public string fieldJson { get; set; }

    /// <summary>
    ///     字段.
    /// </summary>
    public List<DataSetFieldModel> children { get; set; }
}