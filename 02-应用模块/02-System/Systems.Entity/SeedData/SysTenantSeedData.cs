using Extras.DatabaseAccessor.SqlSugar.Options;
using Furion;
using IotPlatform.Core;
using IotPlatform.Core.Const;
using IotPlatform.Core.Enum;

namespace Systems.Entity.SeedData;

/// <summary>
///     系统租户表种子数据
/// </summary>
public class SysTenantSeedData : ISqlSugarEntitySeedData<SysTenant>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysTenant> HasData()
    {
        DbConnectionConfig defaultDbConfig = App.GetOptions<ConnectionStringsOptions>().DefaultConnectionConfig;
        return new[]
        {
            new SysTenant
            {
                Id = 1300000000001, OrgId = 31519576587015, UserId = 1300000000111, Host = "***********", TenantType = TenantTypeEnum.Id, DbType = defaultDbConfig.DbType,
                Connection = defaultDbConfig.ConnectionString, ConfigId = SqlSugarConst.MainConfigId, Remark = "系统默认", CreatedTime = DateTime.Parse("2022-02-10 00:00:00")
            }
        };
    }
}