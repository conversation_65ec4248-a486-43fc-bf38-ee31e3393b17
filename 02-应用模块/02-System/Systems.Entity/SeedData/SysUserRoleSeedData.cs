namespace Systems.Entity.SeedData;

/// <summary>
///     系统用户角色表种子数据
/// </summary>
public class SysUserRoleSeedData : ISqlSugarEntitySeedData<SysUserRole>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysUserRole> HasData()
    {
        return new[]
        {
            new SysUserRole { Id = 1300000000101, SysUserId = 1300000000111, SysRoleId = 1300000000101 }
        };
    }
}