namespace Systems.Entity.Dto;

/// <summary>
///     打印模板保存输入.
/// </summary>
[SuppressSniffer]
public class PrintDevSaveInput
{
    /// <summary>
    ///     打印模板id.
    /// </summary>
    public long id { get; set; }

    /// <summary>
    ///     版本id.
    /// </summary>
    public long versionId { get; set; }

    /// <summary>
    ///     名称.
    /// </summary>
    public string fullName { get; set; }

    /// <summary>
    ///     打印模板json.
    /// </summary>
    public string printTemplate { get; set; }

    /// <summary>
    ///     版本.
    /// </summary>
    public int? version { get; set; }

    /// <summary>
    ///     0 保存，1 发布.
    /// </summary>
    public int? type { get; set; }

    /// <summary>
    ///     转换配置.
    /// </summary>
    public string convertConfig { get; set; }

    /// <summary>
    ///     数据集.
    /// </summary>
    public List<PrintDevDataSetModel> dataSetList { get; set; }
}