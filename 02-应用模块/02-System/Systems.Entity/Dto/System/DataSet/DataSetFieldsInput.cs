namespace Systems.Entity.Dto;

/// <summary>
/// 数据集字段输入.
/// </summary>
[SuppressSniffer]
public class DataSetFieldsInput
{
    /// <summary>
    /// sql语句.
    /// </summary>
    public string dataConfigJson { get; set; }

    /// <summary>
    /// 连接id.
    /// </summary>
    public long dbLinkId { get; set; }

    /// <summary>
    /// 显示字段.
    /// </summary>
    public string fieldJson { get; set; }

    /// <summary>
    /// 名称.
    /// </summary>
    public string fullName { get; set; }

    /// <summary>
    /// 输入参数.
    /// </summary>
    public string parameterJson { get; set; }

    /// <summary>
    /// 版本id.
    /// </summary>
    public string jnpfId { get; set; }
}