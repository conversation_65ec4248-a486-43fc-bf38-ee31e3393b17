namespace Systems.Entity.Dto;

/// <summary>
/// 数据集数据输入.
/// </summary>
[SuppressSniffer]
public class DataSetDataInput
{
    /// <summary>
    /// 打印模板id.
    /// </summary>
    public long id { get; set; }

    /// <summary>
    /// 表单id.
    /// </summary>
    public string formId { get; set; }

    /// <summary>
    /// 数据来源.
    /// </summary>
    public string type { get; set; }

    /// <summary>
    /// 其他参数.
    /// </summary>
    public Dictionary<string, object> map { get; set; } = new Dictionary<string, object>();
}