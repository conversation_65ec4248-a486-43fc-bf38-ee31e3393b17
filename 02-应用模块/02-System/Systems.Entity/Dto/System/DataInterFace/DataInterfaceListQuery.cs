using Common.Models;

namespace Systems.Entity.Dto;

/// <summary>
///     数据接口列表查询输入.
/// </summary>
[SuppressSniffer]
public class DataInterfaceListQuery : PageInputBase
{
    /// <summary>
    ///     分类id.
    /// </summary>
    public long CategoryId { get; set; }

    /// <summary>
    ///     数据类型.
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    ///     是否分页.
    /// </summary>
    public int HasPage { get; set; }

    /// <summary>
    ///     启用标识.
    /// </summary>
    public int? EnabledMark { get; set; }
}