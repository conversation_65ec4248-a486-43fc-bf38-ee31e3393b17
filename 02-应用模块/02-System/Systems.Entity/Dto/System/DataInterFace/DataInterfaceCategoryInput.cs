namespace Systems.Entity.Dto;

/// <summary>
/// 新增接口分类
/// </summary>
public class DataInterfaceCategoryAddInput
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    /// 上级节点
    /// </summary>
    public long Pid { get; set; }
}

/// <summary>
/// 修改接口分类
/// </summary>
public class DataInterfaceCategoryUpdateInput : DataInterfaceCategoryAddInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}