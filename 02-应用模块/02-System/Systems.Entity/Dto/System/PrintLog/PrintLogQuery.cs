namespace Systems.Entity.Dto;

/// <summary>
///     打印模板日志列表输入.
/// </summary>
[SuppressSniffer]
public class PrintLogQuery : PageInputBase
{
    /// <summary>
    ///     开始时间.
    /// </summary>
    [ModelBinder(BinderType = typeof(TimestampToDateTimeModelBinder))]
    public DateTime? startTime { get; set; }

    /// <summary>
    ///     结束时间.
    /// </summary>
    [ModelBinder(BinderType = typeof(TimestampToDateTimeModelBinder))]
    public DateTime? endTime { get; set; }
}