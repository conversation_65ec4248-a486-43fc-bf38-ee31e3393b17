using Common.Security;

namespace Systems.Entity.Dto;

/// <summary>
///     数据集树形输出
/// </summary>
[SuppressSniffer]
public class ChartsDataTreeOutput
{
    /// <summary>
    ///     父节点Id
    /// </summary>
    public string ParentId { get; set; }

    /// <summary>
    ///     节点Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     节点文本
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    ///     节点图标
    /// </summary>
    public string Icon { get; set; }

    /// <summary>
    ///     节点类型
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public string DataType { get; set; }

    /// <summary>
    ///     节点状态
    /// </summary>
    public ChartsDataTreeState State { get; set; }

    /// <summary>
    ///     简称
    /// </summary>
    public string SimpleName { get; set; }

    /// <summary>
    ///     是否分组
    /// </summary>
    public string IsGroup { get; set; }
}

/// <summary>
///     数据集树节点状态
/// </summary>
[SuppressSniffer]
public class ChartsDataTreeState
{
    /// <summary>
    ///     是否展开
    /// </summary>
    public bool Opened { get; set; }

    /// <summary>
    ///     是否禁用
    /// </summary>
    public bool Disabled { get; set; }

    /// <summary>
    ///     是否选中
    /// </summary>
    public bool Selected { get; set; }
}

/// <summary>
///     数据集详情输出
/// </summary>
[SuppressSniffer]
public class ChartsDataFormOutput
{
    /// <summary>
    ///     Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     分组Id
    /// </summary>
    public string GroupId { get; set; }

    /// <summary>
    ///     类型
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    ///     配置内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    ///     字段配置
    /// </summary>
    public string? Fields { get; set; }

    /// <summary>
    ///     参数配置
    /// </summary>
    public string? Params { get; set; }

    /// <summary>
    ///     排序码
    /// </summary>
    public int SortCode { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public string CreateTime { get; set; }
}

/// <summary>
///     分组树形输出
/// </summary>
[SuppressSniffer]
public class ChartsDataGroupOutput
{
    /// <summary>
    ///     Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     父级Id
    /// </summary>
    public string ParentId { get; set; }

    /// <summary>
    ///     分组名称
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    ///     是否分组
    /// </summary>
    public string IsGroup { get; set; }

    /// <summary>
    ///     是否有子级
    /// </summary>
    public bool HasChildren { get; set; }

    /// <summary>
    ///     子节点
    /// </summary>
    public List<ChartsDataGroupOutput> Children { get; set; } = new();

    /// <summary>
    ///     子节点数量
    /// </summary>
    public int Num { get; set; }

    /// <summary>
    ///     是否为子节点
    /// </summary>
    public bool IsLeaf { get; set; }
}