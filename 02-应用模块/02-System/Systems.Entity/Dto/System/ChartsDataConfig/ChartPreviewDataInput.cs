using Common.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Systems.Entity.Dto;

/// <summary>
/// 图表数据预览输入
/// </summary>
public class ChartPreviewDataInput
{
    /// <summary>
    /// 数据源类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public Dictionary<string, object>? Content { get; set; }

    /// <summary>
    /// 字段设置
    /// </summary>
    public List<FieldItem>? Fields { get; set; }

    /// <summary>
    /// 参数JSON
    /// </summary>
    public Dictionary<string, object>? Params { get; set; }
}

/// <summary>
/// 表内容
/// </summary>
public class TableContentNew
{
    /// <summary>
    /// 数据库ID
    /// </summary>
    public long? DatabaseId { get; set; }

    /// <summary>
    /// 表名
    /// </summary>
    public string? TableName { get; set; }
}

/// <summary>
/// 字段项
/// </summary>
public class FieldItem
{
    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 列名
    /// </summary>
    public string? Column { get; set; }

    /// <summary>
    /// 字段类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 表达式
    /// </summary>
    public string? Express { get; set; }

    /// <summary>
    /// 是否计算字段
    /// </summary>
    public bool IsCalc { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// SQL查询内容配置
/// </summary>
public class SqlQueryContent
{
    /// <summary>
    /// SQL语句
    /// </summary>
    public string? Sql { get; set; }

    /// <summary>
    /// 数据库ID
    /// </summary>
    public string? DatabaseId { get; set; }
}