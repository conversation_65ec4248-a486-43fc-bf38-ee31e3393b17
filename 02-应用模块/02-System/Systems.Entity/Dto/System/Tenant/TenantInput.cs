using Common.Models;

namespace Systems.Entity.Dto;

public class TenantInput : BaseId
{
    /// <summary>
    ///     状态
    /// </summary>
    public bool Status { get; set; }
}

public class PageTenantInput : BasePageInput
{
    /// <summary>
    ///     名称
    /// </summary>
    public virtual string Name { get; set; }

    /// <summary>
    ///     电话
    /// </summary>
    public virtual string Phone { get; set; }
}

public class AddTenantInput
{
    /// <summary>
    ///     租户名称
    /// </summary>
    [Required(ErrorMessage = "租户名称不能为空")]
    [MinLength(2, ErrorMessage = "租户名称不能少于2个字符")]
    public string Name { get; set; }

    /// <summary>
    ///     租管账号
    /// </summary>
    [Required(ErrorMessage = "租管账号不能为空")]
    [MinLength(3, ErrorMessage = "租管账号不能少于3个字符")]
    public string AdminAccount { get; set; }

    /// <summary>
    ///     租户名称
    /// </summary>
    public string AdminName { get; set; }
}

public class UpdateTenantInput : TenantOutput
{
    /// <summary>
    ///     租户名称
    /// </summary>
    [Required(ErrorMessage = "租户名称不能为空")]
    [MinLength(2, ErrorMessage = "租户名称不能少于2个字符")]
    public string Name { get; set; }

    /// <summary>
    ///     租管账号
    /// </summary>
    [Required(ErrorMessage = "租管账号不能为空")]
    [MinLength(3, ErrorMessage = "租管账号不能少于3个字符")]
    public string AdminAccount { get; set; }
}

public class TenantUserInput
{
    /// <summary>
    ///     用户Id
    /// </summary>
    public long UserId { get; set; }
}

public class TenantIdInput
{
    /// <summary>
    ///     租户Id
    /// </summary>
    public long TenantId { get; set; }
}