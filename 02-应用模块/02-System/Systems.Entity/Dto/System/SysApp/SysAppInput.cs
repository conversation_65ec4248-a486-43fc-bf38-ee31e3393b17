namespace Systems.Entity.Dto.SysApp.Dto;

/// <summary>
///     应用开发-新增-请求参数
/// </summary>
public class SysAppAddInput
{
    /// <summary>
    ///     中英文名称
    /// </summary>
    [Required]
    public MultiName MultiName { get; set; }

    /// <summary>
    ///     版本号
    /// </summary>
    [Required]
    public string Version { get; set; }

    /// <summary>
    ///     开放能力权限 1：全部权限；2：部分权限
    /// </summary>
    [Required]
    public string ApiAuthority { get; set; }

    /// <summary>
    ///     关闭平台消息通知
    /// </summary>
    public bool ClosePlatformMessage { get; set; }

    /// <summary>
    ///     客户端配置
    /// </summary>
    [SugarColumn(ColumnDescription = "客户端配置", IsJson = true)]
    public List<AppClientAdd> Clients { get; set; }

    /// <summary>
    ///     登出通知地址
    /// </summary>
    public string NotifyUrl { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; } 
}

public class AppClientAdd
{
    /// <summary>
    ///     应用类型：1：web；2：移动端；3：小程序
    /// </summary>
    public string AppType { get; set; }

    /// <summary>
    ///     集成方式，默认：INTEGRATION 外部集成
    /// </summary>
    public string DevType { get; set; } = "INTEGRATION";

    /// <summary>
    ///     客户端-中英文名称
    /// </summary>
    public MultiName MultiName { get; set; }

    /// <summary>
    ///     图标地址
    /// </summary>
    public string IconUrl { get; set; }

    /// <summary>
    ///     登录方式:默认:refresh_token
    /// </summary>
    public List<string> Grants { get; set; }

    /// <summary>
    ///     访问地址(访问首页地址)
    /// </summary>
    public string VisitUrl { get; set; }

    /// <summary>
    ///     重定向地址
    /// </summary>
    public List<string> RedirectUris { get; set; }

    /// <summary>
    ///     State
    /// </summary>
    public string State { get; set; }

    /// <summary>
    ///     打开方式：1：在新标签中打开；2：在门户内打开
    /// </summary>
    public string OpenType { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 服务Id
    /// </summary>
    public long? ServiceId { get; set; }
}

/// <summary>
///     应用排序-请求参数
/// </summary>
public class SysAppSortInput
{
    /// <summary>
    ///     appIds
    /// </summary>
    public List<string> appIds { get; set; }
}

/// <summary>
///     应用开发-修改基本信息-请求参数
/// </summary>
public class SysAppUpdateInput
{
    /// <summary>
    ///     ID
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     中英文名称
    /// </summary>
    [Required]
    public MultiName MultiName { get; set; }

    /// <summary>
    ///     版本号
    /// </summary>
    [Required]
    public string Version { get; set; }

    /// <summary>
    ///     开放能力权限 1：全部权限；2：部分权限
    /// </summary>
    [SugarColumn(ColumnDescription = "开放能力权限 1：全部权限；2：部分权限")]
    public string ApiAuthority { get; set; }

    /// <summary>
    ///     关闭平台消息通知
    /// </summary>
    [SugarColumn(ColumnDescription = "关闭平台消息通知")]
    public bool ClosePlatformMessage { get; set; }

    /// <summary>
    ///     登出通知地址
    /// </summary>
    [SugarColumn(ColumnDescription = "登出通知地址", Length = 512)]
    public string NotifyUrl { get; set; }
}

/// <summary>
///     应用开发-修改客户端-请求参数
/// </summary>
public class SysAppUpdateClientInput
{
    /// <summary>
    ///     ID
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     应用类型：1：web；2：移动端；3：小程序
    /// </summary>
    [Required]
    public string AppType { get; set; }

    /// <summary>
    ///     集成方式，默认：INTEGRATION 外部集成
    /// </summary>
    [Required]
    public string DevType { get; set; } = "INTEGRATION";

    /// <summary>
    ///     客户端-中英文名称
    /// </summary>
    [Required]
    public MultiName MultiName { get; set; }

    /// <summary>
    ///     图标地址
    /// </summary>
    public string IconUrl { get; set; }

    /// <summary>
    ///     登录方式:默认:refresh_token
    /// </summary>
    [Required]
    public List<string> Grants { get; set; }

    /// <summary>
    ///     访问地址(访问首页地址)
    /// </summary>
    public string VisitUrl { get; set; }

    /// <summary>
    ///     重定向地址
    /// </summary>
    public List<string> RedirectUris { get; set; }

    /// <summary>
    ///     ClientSecret
    /// </summary>
    [Required]
    public string ClientSecret { get; set; }

    /// <summary>
    ///     ClientId
    /// </summary>
    [Required]
    public string ClientId { get; set; }

    /// <summary>
    ///     State
    /// </summary>
    public string State { get; set; }

    /// <summary>
    ///     打开方式：1：在新标签中打开；2：在门户内打开
    /// </summary>
    [Required]
    public string OpenType { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Remark { get; set; }
    
    /// <summary>
    /// 服务Id
    /// </summary>
    public long? ServiceId { get; set; }
}