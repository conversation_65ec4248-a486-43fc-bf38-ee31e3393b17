using Common.Models;

namespace Systems.Entity.Dto;

public class MenuInput
{
    /// <summary>
    ///     标题
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     菜单类型（1目录 2菜单 3按钮）
    /// </summary>
    public MenuEnum? Type { get; set; }
}

public class AddMenuInput
{
    /// <summary>
    ///     菜单类型（字典 0目录 1菜单 2按钮）
    /// </summary>
    [Required(ErrorMessage = "菜单类型不能为空")]
    public MenuEnum Type { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    public long Pid { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     图标
    /// </summary>
    public string Icon { get; set; }

    /// <summary>
    ///     路由地址
    /// </summary>
    public string Router { get; set; }

    /// <summary>
    ///     组件地址
    /// </summary>
    public string Component { get; set; }

    /// <summary>
    ///     权限标识
    /// </summary>
    public string Permission { get; set; }

    /// <summary>
    ///     应用分类（应用编码）
    /// </summary>
    public string Application { get; set; }

    /// <summary>
    ///     打开方式（字典 0无 1组件 2内链 3外链）
    /// </summary>
    public MenuOpenEnum OpenType { get; set; }

    /// <summary>
    ///     是否可见（Y-是，N-否）
    /// </summary>
    public bool Visible { get; set; }

    /// <summary>
    ///     内链地址
    /// </summary>
    public string Link { get; set; }

    /// <summary>
    ///     重定向地址
    /// </summary>
    public string Redirect { get; set; }

    /// <summary>
    ///     权重（字典 1系统权重 2业务权重）
    /// </summary>
    public MenuWeight Weight { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Remark { get; set; }
}

public class UpdateMenuInput
{
    /// <summary>
    ///     菜单Id
    /// </summary>
    [Required(ErrorMessage = "菜单Id不能为空")]
    public long Id { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    /// DeleteMenuInput
    [Required(ErrorMessage = "父级菜单Id不能为空")]
    public long Pid { get; set; }

    /// <summary>
    ///     菜单类型（字典 0目录 1菜单 2按钮）
    /// </summary>
    [Required(ErrorMessage = "菜单类型不能为空")]
    public MenuEnum Type { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     图标
    /// </summary>
    public string Icon { get; set; }

    /// <summary>
    ///     路由地址
    /// </summary>
    public string Router { get; set; }

    /// <summary>
    ///     组件地址
    /// </summary>
    public string Component { get; set; }

    /// <summary>
    ///     权限标识
    /// </summary>
    public string Permission { get; set; }

    /// <summary>
    ///     应用分类（应用编码）
    /// </summary>
    public string Application { get; set; }

    /// <summary>
    ///     打开方式（字典 0无 1组件 2内链 3外链）
    /// </summary>
    public MenuOpenEnum OpenType { get; set; }

    /// <summary>
    ///     是否可见（Y-是，N-否）
    /// </summary>
    public bool Visible { get; set; }

    /// <summary>
    ///     内链地址
    /// </summary>
    public string Link { get; set; }

    /// <summary>
    ///     重定向地址
    /// </summary>
    public string Redirect { get; set; }

    /// <summary>
    ///     权重（字典 1系统权重 2业务权重）
    /// </summary>
    public MenuWeight Weight { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Remark { get; set; }
    
    /// <summary>
    /// 功能主键.
    /// </summary>
    public long? ModuleId { get; set; }
}

public class GetMenuListInput
{
    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     应用分类（应用编码）
    /// </summary>
    public string Application { get; set; }
}

public class GetMenuTreeInput
{
    /// <summary>
    ///     应用分类（应用编码）
    /// </summary>
    public string? Application { get; set; }
}

public class TreeForGrantInput
{
    /// <summary>
    ///     应用分类（应用编码）
    /// </summary>
    public string Application { get; set; }
}

public class ChangeAppMenuInput
{
    /// <summary>
    ///     应用编码
    /// </summary>
    /// DeleteMenuInput
    [Required(ErrorMessage = "应用编码不能为空")]
    public string Application { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    public long Pid { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     菜单类型（字典 0目录 1菜单 2按钮）
    /// </summary>
    public MenuEnum Type { get; set; }

    /// <summary>
    ///     图标
    /// </summary>
    public string Icon { get; set; }

    /// <summary>
    ///     路由地址
    /// </summary>
    public string Router { get; set; }

    /// <summary>
    ///     组件地址
    /// </summary>
    public string Component { get; set; }

    /// <summary>
    ///     权限标识
    /// </summary>
    public string Permission { get; set; }

    /// <summary>
    ///     打开方式（字典 0无 1组件 2内链 3外链）
    /// </summary>
    public MenuOpenEnum OpenType { get; set; }

    /// <summary>
    ///     是否可见（Y-是，N-否）
    /// </summary>
    public bool Visible { get; set; }

    /// <summary>
    ///     内链地址
    /// </summary>
    public string Link { get; set; }

    /// <summary>
    ///     重定向地址
    /// </summary>
    public string Redirect { get; set; }

    /// <summary>
    ///     权重（字典 1系统权重 2业务权重）
    /// </summary>
    public MenuWeight Weight { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// </summary>
public class UpdateMenuStatusInput : BaseId
{
    /// <summary>
    ///     启用状态
    /// </summary>
    public bool Status { get; set; }
}

/// <summary>
///     服务菜单树
/// </summary>
public class TreeForGrantMenuOutput
{
    /// <summary>
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     菜单
    /// </summary>
    public List<SysMenu> Menus { get; set; }
}