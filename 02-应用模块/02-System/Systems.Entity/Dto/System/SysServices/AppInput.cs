namespace Systems.Entity.Dto;

/// <summary>
/// </summary>
public class AddServiceInput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required(ErrorMessage = "应用名称不能为空")]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [Required(ErrorMessage = "应用编码不能为空")]
    public string Code { get; set; }

    /// <summary>
    ///     是否默认激活（Y-是，N-否）,只能有一个系统默认激活
    ///     用户登录后默认展示此系统菜单
    /// </summary>
    public string Active { get; set; }

    /// <summary>
    ///     状态（字典 0正常 1停用 ）
    /// </summary>
    public bool Status { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    ///     打开方式：1：在新标签中打开；2：在门户内打开
    /// </summary>
    [Required]
    public string OpenType { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// </summary>
public class UpdateServiceInput
{
    /// <summary>
    ///     应用Id
    /// </summary>
    [Required(ErrorMessage = "应用Id不能为空")]
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public virtual string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    public virtual string Code { get; set; }

    /// <summary>
    ///     是否默认激活（Y-是，N-否）,只能有一个系统默认激活
    ///     用户登录后默认展示此系统菜单
    /// </summary>
    public string Active { get; set; }

    /// <summary>
    ///     状态（字典 0正常 1停用）
    /// </summary>
    public bool Status { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    ///     打开方式：1：在新标签中打开；2：在门户内打开
    /// </summary>
    public string OpenType { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// </summary>
public class ChangeUserServiceStatusInput : BaseId
{
    /// <summary>
    ///     状态（字典 0正常 1停用 2删除）
    /// </summary>
    public bool Status { get; set; }
}

/// <summary>
///     服务排序-请求参数
/// </summary>
public class SysServiceSortInput
{
    /// <summary>
    ///     服务名称
    /// </summary>
    public List<string> ServiceNames { get; set; }
}

/// <summary>
///     快捷菜单-新增
/// </summary>
public class ShortcutMenuAdd
{
    /// <summary>
    ///     菜单Id
    /// </summary>
    [Required]
    public List<long> MenuId { get; set; }
}

/// <summary>
///     快捷菜单返回
/// </summary>
public class ShortcutMenuList
{
    /// <summary>
    ///     服务Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     code
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     路由
    /// </summary>
    public string Router { get; set; }

    /// <summary>
    /// MenuId
    /// </summary>
    public long SysMenuId { get; set; }
}

/// <summary>
///     用户拥有服务及菜单
/// </summary>
public class CurrentUserServiceOutput
{
    /// <summary>
    ///     服务Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// </summary>
    public string GroupType { get; set; } = "service";

    /// <summary>
    ///     菜单
    /// </summary>
    public List<Shortcut> Shortcuts { get; set; }
}

/// <summary>
///     菜单
/// </summary>
public class Shortcut
{
    /// <summary>
    ///     服务Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     code
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     Router
    /// </summary>
    public string Router { get; set; }
}