global using Furion.Schedule;
global using Magicodes.ExporterAndImporter.Core;
global using Magicodes.ExporterAndImporter.Excel;
global using Microsoft.Extensions.Logging;
global using SqlSugar;
global using System.ComponentModel;
global using System.ComponentModel.DataAnnotations;
global using Extras.DatabaseAccessor.SqlSugar.Extensions;
global using Common.Security;
global using DateTime = System.DateTime;
global using Furion.DependencyInjection;
global using Common.Models;
global using Furion.AspNetCore;
global using Microsoft.AspNetCore.Mvc;
global using Systems.Entity.Model.System;