using IotPlatform.Core.Extension;
using DateTime = System.DateTime;

namespace IotPlatform.PolicyMessage.Entity.Dto.Template;

/// <summary>
///     消息推送模板返回
/// </summary>
public class SendTemplatePageOutput
{
    /// <summary>
    ///     Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     模板描述
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    ///     消息类型:1:文本；2：图文；3:卡片
    /// </summary>
    public SendTemplateTypeEnum Type { get; set; }

    /// <summary>
    /// </summary>
    public string TypeName => Type.GetDescription();

    /// <summary>
    ///     配置内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     文本消息
    /// </summary>
    public TextContent? TextContent =>
        Type == SendTemplateTypeEnum.Text ? JSON.Deserialize<TextContent>(Content) : null;

    /// <summary>
    ///     图文消息
    /// </summary>
    public ImageContent? ImageContent =>
        Type == SendTemplateTypeEnum.Image ? JSON.Deserialize<ImageContent>(Content) : null;

    /// <summary>
    ///     卡片消息
    /// </summary>
    public CardContent? CardContent =>
        Type == SendTemplateTypeEnum.Card ? JSON.Deserialize<CardContent>(Content) : null;

    /// <summary>
    ///     输入参数
    /// </summary>
    public List<SendTemplateInputData> SendTemplateInputs { get; set; }

    /// <summary>
    ///     租户Id
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }
}