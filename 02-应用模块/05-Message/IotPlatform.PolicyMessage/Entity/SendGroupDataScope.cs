namespace IotPlatform.PolicyMessage.Entity;

/// <summary>
///     推送消息分组关联数据
/// </summary>
[SugarTable("business_sendMsgGroupDataScope", "推送消息分组关联数据")]
public class SendGroupDataScope : EntityTenant
{
    /// <summary>
    ///     用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "用户Id")]
    public long UserId { get; set; }

    /// <summary>
    ///     企业微信UserId
    /// </summary>
    [SugarColumn(ColumnDescription = "企业微信UserId")]
    public string? WeChatId { get; set; }

    /// <summary>
    ///     邮箱
    /// </summary>
    [SugarColumn(ColumnDescription = "邮箱")]
    public string? Email { get; set; }

    /// <summary>
    ///     推送消息分组Id
    /// </summary>
    [SugarColumn(ColumnDescription = "推送消息分组Id")]
    public long SendGroupId { get; set; }

    #region 关联表

    /// <summary>
    ///     推送消息分组
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(SendGroupId))]
    public SendGroup SendGroup { get; set; }

    /// <summary>
    ///     user用户
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(UserId))]
    public SysUser User { get; set; }

    #endregion
}