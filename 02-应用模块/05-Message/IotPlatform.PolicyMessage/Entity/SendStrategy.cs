using Systems.Entity;

namespace IotPlatform.PolicyMessage.Entity;

/// <summary>
///     消息推送策略
/// </summary>
[SugarTable("business_sendMsgStrategy", "消息推送策略")]
public class SendStrategy : EntityTenant
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     推送对象
    /// </summary>
    [SugarColumn(ColumnDescription = "推送对象", IsNullable = true)]
    public string? PushObjects { get; set; }

    /// <summary>
    ///     额外推送对象
    /// </summary>
    [SugarColumn(ColumnDescription = "额外推送对象", IsNullable = true)]
    public string? ExPushObjects { get; set; }

    /// <summary>
    ///     推送模板Id
    /// </summary>
    [SugarColumn(ColumnDescription = "推送模板Id")]
    public long SendTemplateId { get; set; }

    /// <summary>
    ///     策略描述
    /// </summary>
    [SugarColumn(ColumnDescription = "策略描述", Length = 1024, IsNullable = true)]
    public string? Remark { get; set; }


    #region 忽略字段

    #endregion

    #region 关联表

    /// <summary>
    ///     推送渠道
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<SendChannel> SendChannel { get; set; }

    /// <summary>
    ///     推送模板
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SendTemplateId))]
    public SendTemplate SendTemplate { get; set; }

    /// <summary>
    ///     执行日志
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.SendStrategyLog.SendStrategyId))]
    [SugarColumn(IsIgnore = true)]
    public List<SendStrategyLog> SendStrategyLog { get; set; }

    #endregion
}

/// <summary>
///     推送对象
/// </summary>
public class PushObjectsModel
{
    /// <summary>
    ///     用户组集合
    /// </summary>
    public List<long> UserGroupIds { get; set; }
}

/// <summary>
///     额外推送对象
/// </summary>
public class ExPushObjectsModel
{
    /// <summary>
    ///     微信Id
    /// </summary>
    public List<string> WeChatIds { get; set; } = new();

    /// <summary>
    ///     邮箱
    /// </summary>
    public List<string> EmailIds { get; set; } = new();
}