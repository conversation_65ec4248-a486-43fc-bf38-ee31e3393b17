namespace IotPlatform.PolicyMessage.Servers;

/// <summary>
///     消息推送模板
/// </summary>
public class MessagePushTemplatePreviewService : ITransient, IDisposable
{
    // /// <summary>
    // /// </summary>
    // private readonly EngineExSingleton _engineEx;

    /// <summary>
    ///     消息推送模板
    /// </summary>
    public MessagePushTemplatePreviewService()
    {
    }

    /// <summary>
    ///     推送模板预览
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public Task<Dictionary<string, string>> MessagePushTemplatePreview(SendTemplateRun input)
    {
        Dictionary<string, string> dict = new Dictionary<string, string>();
        switch (input.Type)
        {
            case SendTemplateTypeEnum.Text:
            {
                dict["title"] = input.TextContent.Title;
                dict["content"] = input.TextContent.Content;
                break;
            }
            case SendTemplateTypeEnum.Image:
                dict["title"] = input.ImageContent.Title;
                dict["content"] = input.ImageContent.Content;
                dict["imageUrl"] = input.ImageContent.ImageUrl;
                dict["toUrl"] = input.ImageContent.ToUrl;
                break;
            case SendTemplateTypeEnum.Card:
                dict["title"] = input.CardContent.Title;
                dict["content"] = input.CardContent.Content;
                dict["linkUrl"] = input.CardContent.LinkUrl;
                dict["linkName"] = input.CardContent.LinkName;
                break;
        }

        foreach (AddSendTemplateInput inputs in input.SendTemplateInputs)
        {
            switch (inputs.Type)
            {
                case SendTemplateInputTypeEnum.Const:
                {
                    if (dict["title"].Contains("$" + "{" + $"{inputs.Name}" + "}"))
                    {
                        dict["title"] = dict["title"].Replace("$" + "{" + $"{inputs.Name}" + "}", inputs.Content);
                    }

                    if (dict["content"].Contains("$" + "{" + $"{inputs.Name}" + "}"))
                    {
                        dict["content"] = dict["content"].Replace("$" + "{" + $"{inputs.Name}" + "}", inputs.Content);
                    }

                    break;
                }
                case SendTemplateInputTypeEnum.Property:
                {
                    DataStorage.PropertyStorage? dicVal = DataStorage.Instance.GetData(inputs.Content);
                    string? val = "";
                    if (dicVal != null)
                    {
                        val = dicVal.Value;
                    }

                    if (dict["title"].Contains("$" + "{" + $"{inputs.Name}" + "}"))
                    {
                        dict["title"] = dict["title"].Replace("$" + "{" + $"{inputs.Name}" + "}", val!);
                    }

                    if (dict["content"].Contains("$" + "{" + $"{inputs.Name}" + "}"))
                    {
                        dict["content"] = dict["content"].Replace("$" + "{" + $"{inputs.Name}" + "}", val!);
                    }

                    break;
                }
                case SendTemplateInputTypeEnum.Script:
                {
                    // try
                    // {
                    //      var completionValue = _engineEx.Action(new EngineExecuteActionInput {Content = inputs.Content});
                    //     
                    //      if (dict["title"].Contains("$" + "{" + $"{inputs.Name}" + "}"))
                    //          dict["title"] = dict["title"].Replace("$" + "{" + $"{inputs.Name}" + "}",
                    //              completionValue.Value!.ToString());
                    //     
                    //      if (dict["content"].Contains("$" + "{" + $"{inputs.Name}" + "}"))
                    //          dict["content"] = dict["content"].Replace("$" + "{" + $"{inputs.Name}" + "}",
                    //              completionValue.Value!.ToString());
                    // }
                    // catch (Exception ex)
                    // {
                    //     throw Oops.Oh($"脚本执行失败,Error:【{ex.Message}】");
                    // }

                    break;
                }
            }
        }

        return Task.FromResult(dict);
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        // _engineEx.Dispose();
    }
}