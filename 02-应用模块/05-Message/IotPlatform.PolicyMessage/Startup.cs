using Furion;
using IotPlatform.PolicyMessage.Event;

namespace IotPlatform.PolicyMessage;

/// <summary>
///     注册缓存
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册事件订阅者(支持自定义消息队列组件)
        services.AddEventBus(builder => { builder.AddSubscriber<PolicyMessageEventSubscriber>(); });
    }
}