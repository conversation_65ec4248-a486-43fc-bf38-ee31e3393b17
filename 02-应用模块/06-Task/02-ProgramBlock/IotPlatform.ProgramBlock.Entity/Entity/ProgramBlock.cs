namespace IotPlatform.ProgramBlock.Entity;

/// <summary>
///     程序块
/// </summary>
[SugarTable("business_programBlock", "程序块")]
public class ProgramBlock : EntityTenantId
{
    /// <summary>
    ///     分组Id
    /// </summary>
    [SugarColumn(ColumnDescription = "分组Id")]
    public long ProgramBlockGroupId { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 64)]
    public string? Code { get; set; }

    /// <summary>
    /// JSON参数配置
    /// </summary>
    [SugarColumn(IsJson = true, ColumnDescription = "JSON参数配置", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public dynamic? Config { get; set; }
    
    /// <summary>
    ///     执行内容
    /// </summary>
    [SugarColumn(ColumnDescription = "执行内容", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public string? Content { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", IsNullable = true)]
    public string? Remark { get; set; }

    /// <summary>
    ///     共享模块
    /// </summary>
    [SugarColumn(IsJson = true, ColumnDescription = "共享模块", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public List<string>? SharedModule { get; set; }

    #region 关联表

    /// <summary>
    ///     程序块分组
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ProgramBlockGroupId))]
    public ProgramBlockGroup ProgramBlockGroup { get; set; }

    #endregion
}