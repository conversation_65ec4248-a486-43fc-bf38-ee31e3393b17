using Microsoft.Extensions.Hosting;
using SqlSugar;

namespace IotPlatform.ProgramBlock.Services;

/// <summary>
///     程序块缓存
/// </summary>
public class BlockSingleton : IHostedService
{
    /// <summary>
    /// </summary>
    private readonly ISqlSugarClient _db;

    public Dictionary<long, Entity.ProgramBlock> ProgramBlockCache = new();

    public BlockSingleton(ISqlSugarClient db)
    {
        _db = db;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        List<Entity.ProgramBlock>? blocks = await _db.Queryable<Entity.ProgramBlock>().ToListAsync(cancellationToken);
        ProgramBlockCache = blocks.ToDictionary(s => s.Id, s => s);
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}