namespace IotPlatform.Task.Entity;

/// <summary>
///     定时任务新增
/// </summary>
public class TimerAddInput
{
    /// <summary>
    ///     任务名称
    /// </summary>
    [Required(ErrorMessage = "任务名称不能为空")]
    public string Name { get; set; }

    /// <summary>
    ///     分组id
    /// </summary>
    [Required]
    public long TaskDefinitionGroupId { get; set; }
    
    /// <summary>
    ///     备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 任务配置修改
/// </summary>
public class TimerUpdateInput
{
    /// <summary>
    /// Id
    /// </summary>
    [Required]
    public long Id { get; set; }
    /// <summary>
    ///     任务名称
    /// </summary>
    [Required(ErrorMessage = "任务名称不能为空")]
    public string Name { get; set; }

    /// <summary>
    ///     分组id
    /// </summary>
    [Required]
    public long TaskDefinitionGroupId { get; set; }
    
    /// <summary>
    ///     备注
    /// </summary>
    public string? Remark { get; set; }
}

/// <summary>
/// 任务配置保存
/// </summary>
public class TimerSaveInput
{
    /// <summary>
    /// 任务Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    /// 任务详情
    /// </summary>
    [Required] 
    public List<JobTaskDetailAuInput> TaskDetails { get; set; }
}


/// <summary>
/// </summary>
public class JobTaskDetailAuInput
{
    /// <summary>
    ///     触发类型：1：定时触发；2：MQTT触发；3：属性触发；4:变量触发；5：消息触发
    /// </summary>
    public SysJobTaskDetailTypeEnum EventType { get; set; }

    /// <summary>
    ///     触发内容
    /// </summary>
    [Required(ErrorMessage = "触发内容不能为空")]
    public string EventParams { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    [Required(ErrorMessage = "脚本内容不能为空")]
    public string ExecuteContent { get; set; }

    /// <summary>
    ///     执行内容：1：脚本任务；2：程序块；3：消息推送；
    /// </summary>
    public ExecuteTypeEnum ExecuteType { get; set; }
}

/// <summary>
///     清空日志请求参数
/// </summary>
public class ClearLogsInput : BaseId
{
    /// <summary>
    ///     开始时间
    /// </summary>
    [Required]
    public DateTime StartTime { get; set; } = DateTime.Now.Date;

    /// <summary>
    ///     截止时间
    /// </summary>
    [Required]
    public DateTime EndTime { get; set; } = DateTime.Now.AddDays(+1);
}

/// <summary>
/// 任务配置分组-保存
/// </summary>
public class SysTimersGroupSaveInput
{
    /// <summary>
    /// 分组Id。不填默认新增
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    /// pId
    /// </summary>
    [Required]
    public long Pid { get; set; }
}

/// <summary>
/// 任务复制
/// </summary>
public class TaskCopyInput : BaseId
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}