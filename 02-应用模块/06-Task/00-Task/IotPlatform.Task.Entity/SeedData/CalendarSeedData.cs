namespace IotPlatform.Task.Entity.SeedData;

/// <summary>
///     任务日历
/// </summary>
public class CalendarSeedData : ISqlSugarEntitySeedData<TaskCalendar>
{
    /// <summary>
    /// </summary>
    /// <returns></returns>
    public IEnumerable<TaskCalendar> HasData()
    {
        yield return new TaskCalendar
        {
            Id = 142307070994321, Name = "任务日历", Pid = 0, Pids = "[0]", CreatedTime = DateTime.Now, CreatedUserId = 1, UpdatedTime = DateTime.Now,
            CreatedUserName = "admin",
            UpdatedUserId = 1, UpdatedUserName = "admin",
            CalendarDetails = new List<TaskCalendarDetail>(),
            Children = new List<TaskCalendar>()
        };
    }
}