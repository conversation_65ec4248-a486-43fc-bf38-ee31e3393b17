namespace IotPlatform.Task.Entity;

/// <summary>
///     任务日历表详情
/// </summary>
[SugarTable("business_taskCalendarDetail", "任务日历表详情")]
public class TaskCalendarDetail : EntityTenant
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称")]
    [Required]
    [MaxLength(64)]
    public string Name { get; set; }

    /// <summary>
    ///     任务日历Id
    /// </summary>
    [SugarColumn(ColumnDescription = "任务日历Id")]
    public long CalendarId { get; set; }

    /// <summary>
    ///     任务日历多层Id
    /// </summary>
    [SugarColumn(ColumnDescription = "任务日历多层Id")]
    public string CalendarIds { get; set; }

    /// <summary>
    ///     标记日期
    /// </summary>
    [SugarColumn(ColumnDescription = "标记日期")]
    public string Time { get; set; }

    /// <summary>
    ///     月份
    /// </summary>
    [SugarColumn(ColumnDescription = "月份")]
    public short Month { get; set; }

    #region 关联表

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(CalendarId))]
    public TaskCalendar TaskCalendar { get; set; }

    #endregion
}