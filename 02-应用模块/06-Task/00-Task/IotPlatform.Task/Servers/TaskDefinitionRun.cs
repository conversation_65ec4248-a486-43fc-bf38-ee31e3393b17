using System.Diagnostics;
using System.Text.Json;
using System.Text.Json.Serialization;
using Common.Models.Dto;
using IotPlatform.ProgramBlock.Entity;
using IotPlatform.ProgramBlock.Services;
using DateTime = System.DateTime;
using Microsoft.Extensions.Logging;

namespace IotPlatform.Task.Servers;

/// <summary>
/// 任务定义运行类
/// 负责管理和执行各种类型的任务，包括脚本执行、程序块执行和消息推送等
/// </summary>
public class TaskDefinitionRun : IDisposable
{
    /// <summary>
    /// 用于控制任务取消的令牌源
    /// </summary>
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    /// <summary>
    /// 事件总线工厂，用于处理事件订阅和发布
    /// </summary>
    private readonly IEventBusFactory _eventBusFactory;

    /// <summary>
    /// 程序块单例，用于缓存和管理程序块
    /// </summary>
    private readonly BlockSingleton _blockSingleton;

    /// <summary>
    /// 定时任务调度器工厂
    /// </summary>
    private readonly ISchedulerFactory _schedulerFactory;

    /// <summary>
    /// 数据库访问客户端
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// JavaScript脚本引擎池，用于管理和复用脚本引擎实例
    /// </summary>
    private readonly JsScriptEnginePool _enginePool;

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<TaskDefinitionRun> _logger;

    /// <summary>
    /// 标记是否是首次运行
    /// </summary>
    private bool _firstRun;

    /// <summary>
    /// 最后一次执行记录
    /// </summary>
    public RecordLine RecordLine = new();

    /// <summary>
    /// 任务运行次数统计
    /// </summary>
    public int RunCount { get; set; }

    /// <summary>
    /// 脚本执行的基础模型，包含执行类型、内容等信息
    /// </summary>
    public TaskBaseModel TaskBase;

    /// <summary>
    /// 任务定义详情
    /// </summary>
    public TaskDefinitionDetail TaskDefinitionDetail { get; set; }

    /// <summary>
    /// 初始化任务定义运行实例
    /// </summary>
    /// <param name="db">数据库访问客户端</param>
    /// <param name="schedulerFactory">调度器工厂</param>
    /// <param name="eventBusFactory">事件总线工厂</param>
    /// <param name="enginePool">脚本引擎池</param>
    /// <param name="logger">日志记录器</param>
    public TaskDefinitionRun(
        ISqlSugarClient db,
        ISchedulerFactory schedulerFactory,
        IEventBusFactory eventBusFactory,
        JsScriptEnginePool enginePool,
        ILogger<TaskDefinitionRun> logger)
    {
        _db = db;
        _schedulerFactory = schedulerFactory;
        _eventBusFactory = eventBusFactory;
        _enginePool = enginePool;
        _logger = logger;
        _blockSingleton = App.GetService<BlockSingleton>();
    }

    /// <summary>
    /// 启动订阅该设备的数据
    /// </summary>
    public async System.Threading.Tasks.Task Start()
    {
        CancellationToken cancellationToken = _cancellationTokenSource.Token;
        switch (TaskDefinitionDetail.EventType)
        {
            case SysJobTaskDetailTypeEnum.Property:
            {
                PropertyModel? tagModel = JSON.Deserialize<PropertyModel>(TaskDefinitionDetail.EventParams);
                if (tagModel.Name == null)
                {
                    throw Oops.Oh("暂不兼容，请重新维护！");
                }

                foreach (string code in tagModel.Name)
                {
                    // Log.Information($"---------------任务已经创建：{code}---------------------------");
                    TaskBase = new TaskBaseModel
                    {
                        ExecuteType = TaskDefinitionDetail.ExecuteType,
                        Content = TaskDefinitionDetail.ExecuteContent,
                        Compare = tagModel.Compare,
                        FirstEven = tagModel.FirstEven,
                        Code = code,
                        Value = tagModel.Value,
                        DetailId = TaskDefinitionDetail.Id
                    };
                    switch (TaskBase.Compare)
                    {
                        case CompareEnum.Change:
                            DataStorage.Instance.SubscribeValueChangedEvent(TaskBase.Code, ValueChangedHandler);
                            break;
                        case CompareEnum.Time:
                            DataStorage.Instance.SubscribeTimeChangedEvent(TaskBase.Code, TimeChangedHandler);
                            break;
                        case CompareEnum.Equi:
                        case CompareEnum.Greater:
                        case CompareEnum.Less:
                        case CompareEnum.False:
                            DataStorage.Instance.SubscribeCheckSubscriptionConditionEvent(TaskBase.Code, SubscribeTimeChanged);
                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }

                break;
            }
            case SysJobTaskDetailTypeEnum.Variable:
            {
                VariableModel? variableModel = JSON.Deserialize<VariableModel>(TaskDefinitionDetail.EventParams);
                TaskBase = new TaskBaseModel
                {
                    ExecuteType = TaskDefinitionDetail.ExecuteType,
                    Content = TaskDefinitionDetail.ExecuteContent,
                    DetailId = TaskDefinitionDetail.Id,
                    Code = variableModel.Key
                };

                // 动态订阅消息
                await _eventBusFactory.Subscribe(TaskBase.Code, async context =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }

                    await ExecuteScript(content: TaskBase.Content);
                }, cancellationToken: cancellationToken);
                break;
            }
            case SysJobTaskDetailTypeEnum.Message:
            {
                MessageModel? messageModel = JSON.Deserialize<MessageModel>(TaskDefinitionDetail.EventParams);
                TaskBase = new TaskBaseModel
                {
                    ExecuteType = TaskDefinitionDetail.ExecuteType,
                    Content = TaskDefinitionDetail.ExecuteContent,
                    DetailId = TaskDefinitionDetail.Id,
                    Code = messageModel.Message
                };
                // 动态订阅消息
                await _eventBusFactory.Subscribe(TaskBase.Code, async context =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }

                    await ExecuteScript(content: TaskBase.Content);
                }, cancellationToken: cancellationToken);
                break;
            }
            case SysJobTaskDetailTypeEnum.Http:
            {
                HttpModel? httpModel = JSON.Deserialize<HttpModel>(TaskDefinitionDetail.EventParams);
                TaskBase = new TaskBaseModel
                {
                    ExecuteType = TaskDefinitionDetail.ExecuteType,
                    Content = TaskDefinitionDetail.ExecuteContent,
                    DetailId = TaskDefinitionDetail.Id,
                    Code = httpModel.Url
                };
                // 动态订阅消息
                await _eventBusFactory.Subscribe(TaskBase.Code, async context =>
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }

                    await ExecuteScript(content: TaskBase.Content);
                }, cancellationToken: cancellationToken);
                break;
            }
            case SysJobTaskDetailTypeEnum.Time:
            {
                await TimerTaskAdd();
                break;
            }
        }
    }

    /// <summary>
    ///     Value改变事件处理方法
    /// </summary>
    /// <param name="key"></param>
    /// <param name="newValue"></param>
    /// <param name="oldValue"></param>
    private void ValueChangedHandler(string key, string newValue, string oldValue)
    {
        if (!TaskBase.FirstEven && !_firstRun)
        {
            _firstRun = true;
            return;
        }

        // Console.WriteLine($"【Value改变】 Value changed for key '{key}'. New value: {newValue}, Old value: {oldValue}");
        string content = TaskBase.Content;
        if (content.Contains("${this.deviceName}"))
        {
            content = content.Replace("${this.deviceName}", key.Split('/')[0]);
        }

        _ = ExecuteScript(content: content);
    }

    /// <summary>
    ///     Time改变事件处理方法
    /// </summary>
    /// <param name="key"></param>
    /// <param name="newTime"></param>
    /// <param name="oldTime"></param>
    private void TimeChangedHandler(string key, long newTime, long oldTime)
    {
        if (!TaskBase.FirstEven && !_firstRun)
        {
            _firstRun = true;
            return;
        }

        // Console.WriteLine($"【Time改变】Time changed for key '{key}'. New time: {newTime}, Old time: {oldTime}");
        string content = TaskBase.Content;
        if (content.Contains("${this.deviceName}"))
        {
            content = content.Replace("${this.deviceName}", key.Split('/')[0]);
        }

        _ = ExecuteScript(content: content);
    }

    /// <summary>
    ///     数据更新事件处理方法
    /// </summary>
    /// <param name="key"></param>
    /// <param name="newValue"></param>
    /// <param name="newTime"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private void SubscribeTimeChanged(string key, string newValue, long newTime)
    {
        if (!TaskBase.FirstEven && !_firstRun)
        {
            _firstRun = true;
            return;
        }

        string content = TaskBase.Content;
        if (content.Contains("${this.deviceName}"))
        {
            content = content.Replace("${this.deviceName}", key.Split('/')[0]);
        }

        switch (TaskBase.Compare)
        {
            case CompareEnum.Equi:
            {
                if (newValue == TaskBase.Value)
                {
                    // Console.WriteLine($"【实时数据-条件触发-等于】  Data updated for key '{key}'. New value: {newValue}, New time: {newTime}");
                    _ = ExecuteScript(content: content);
                }

                break;
            }
            case CompareEnum.Greater:
            {
                try
                {
                    if (Convert.ToDouble(newValue) > Convert.ToDouble(TaskBase.Value))
                    {
                        // Console.WriteLine($"【实时数据-条件触发-大于】  Data updated for key '{key}'. New value: {newValue}, New time: {newTime}");
                        _ = ExecuteScript(content: content);
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"【实时数据-条件触发-大于】 Data updated for key '{key}'. New value: {newValue}, New time: {newTime} ,Error:【{e.Message}】");
                }

                break;
            }
            case CompareEnum.Less:
            {
                try
                {
                    if (Convert.ToDouble(newValue) < Convert.ToDouble(TaskBase.Value))
                    {
                        // Console.WriteLine($"【实时数据-条件触发-小于】  Data updated for key '{key}'. New value: {newValue}, New time: {newTime}");
                        _ = ExecuteScript(content: content);
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"【实时数据-条件触发-小于】 Data updated for key '{key}'. New value: {newValue}, New time: {newTime} ,Error:【{e.Message}】");
                }

                break;
            }
            case CompareEnum.False:
            {
                if (newValue != TaskBase.Value)
                {
                    // Console.WriteLine($"【实时数据-条件触发-不等于】  Data updated for key '{key}'. New value: {newValue}, New time: {newTime}");
                    _ = ExecuteScript(content: content);
                }

                break;
            }
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    /// <summary>
    ///     Timer触发任务增加
    /// </summary>
    private System.Threading.Tasks.Task TimerTaskAdd()
    {
        // 检查作业是否存在
        bool isExist = _schedulerFactory.ContainsJob(TaskDefinitionDetail.Id.ToString());
        if (isExist)
        {
            return System.Threading.Tasks.Task.CompletedTask;
        }

        TimeModel? timeModel = JSON.Deserialize<TimeModel>(TaskDefinitionDetail.EventParams);

        JobBuilder? jobBuilder = JobBuilder.Create<ExecuteScriptJob>()
                .SetJobId(TaskDefinitionDetail.Id.ToString()) // 作业 Id
                .SetGroupName(TaskDefinitionDetail.SysJobTask?.TaskDefinitionGroup?.Name) // 作业组名称
                .SetJobType("IotPlatform.Task", "IotPlatform.Task.Job.ExportDatabaseJob") // 作业类型，支持多个重载
                .SetJobType<ExecuteScriptJob>() // 作业类型，支持多个重载
                .SetJobType(typeof(ExecuteScriptJob)) // 作业类型，支持多个重载
                .SetDescription(TaskDefinitionDetail.SysJobTask?.Name) // 作业描述
                .SetConcurrent(timeModel.ActionType == SysJobTaskDetailActionTypeEnum.Serial) // 并行还是串行方式，false 为 串行
                .SetIncludeAnnotations(true) // 是否扫描 IJob 类型的触发器特性，true 为 扫描
                .SetProperties(new Dictionary<string, object> {{"TaskDefinitionDetail", JSON.Serialize(TaskDefinitionDetail,new JsonSerializerOptions(){ReferenceHandler = ReferenceHandler.IgnoreCycles})}}) // 作业类型额外数据，支持多个重载，推荐！！！
            ;
        _schedulerFactory.TryAddJob(jobBuilder, new[]
        {
            Triggers.Cron(timeModel.Cron.ConvertCron(), CronStringFormat.WithSeconds).SetTriggerId(TaskDefinitionDetail.Id.ToString())
        }, out IScheduler? scheduler);
        scheduler.UpdateDetail(builder =>
        {
            builder.SetDescription(TaskDefinitionDetail.SysJobTask?.Name);
            builder.SetGroupName(TaskDefinitionDetail.SysJobTask?.TaskDefinitionGroup?.Name);
        });
        scheduler.Persist();

        return System.Threading.Tasks.Task.CompletedTask;
    }

    /// <summary>
    /// 执行脚本任务
    /// 支持多种执行类型：直接脚本执行、程序块执行和消息推送
    /// </summary>
    /// <param name="definitionDetail">任务定义详情，如果为null则使用当前任务的定义</param>
    /// <param name="content">要执行的脚本内容</param>
    /// <param name="cron">定时任务的cron表达式</param>
    private async System.Threading.Tasks.Task ExecuteScript(TaskDefinitionDetail? definitionDetail = null, string content = "", string cron = "")
    {
        await TaskQueued.EnqueueAsync(async (provider, token) =>
        {
            definitionDetail ??= TaskDefinitionDetail;
            await Scoped.CreateAsync(async (_, scope) =>
            {
                DateTime dateTime = DateTime.Now;
                var log = new TaskDefinitionRecord
                {
                    ActionTime = dateTime,
                    TaskDetailId = definitionDetail.Id,
                    TenantId = definitionDetail.TenantId,
                    SysJobTaskId = definitionDetail.SysJobTaskId
                };

                try
                {
                    switch (definitionDetail.ExecuteType)
                    {
                        case ExecuteTypeEnum.Script:
                            {
                                // 使用脚本引擎池执行普通脚本
                                var (result, scriptLog) = await _enginePool.ExecuteScriptWithLogAsync(
                                    $"task-script-{definitionDetail.Id}",
                                    content,
                                    new { timestamp = DateTime.Now }
                                );
                                log.Result = result;
                                break;
                            }

                        case ExecuteTypeEnum.Task:
                            {
                                var runTaskTemplate = content.ToObjectOld<RunProgramBlock>();
                                if (runTaskTemplate == null)
                                {
                                    throw Oops.Oh("配置参数异常！");
                                }

                                // 从缓存获取程序块
                                if (!_blockSingleton.ProgramBlockCache.TryGetValue(runTaskTemplate.Id, out var block))
                                {
                                    throw Oops.Oh("程序块已经被删除！");
                                }

                                // 使用脚本上下文执行程序块
                                await using var context = await _enginePool.CreateContextAsync();

                                // 设置变量
                                foreach (var (key, value) in runTaskTemplate.Values)
                                {
                                    try
                                    {
                                        context.SetVariable(key, value);
                                    }
                                    catch (Exception e)
                                    {
                                        throw Oops.Oh($"传入参数：{key}，值：{value}，Error：{e.Message}");
                                    }
                                }

                                // 执行程序块
                                var (result, scriptLog) = await context.ExecuteAsync(
                                    $"program-block-{runTaskTemplate.Id}",
                                    block.Content ?? string.Empty,
                                    new { timestamp = DateTime.Now }
                                );

                                log.Result = result;
                                break;
                            }

                        case ExecuteTypeEnum.Messgae:
                            {
                                var sendStrategy = JSON.Deserialize<StandardSelectOutput>(content, scope.ServiceProvider);
                                log.Result = await scope.ServiceProvider
                                    .GetRequiredService<ExecuteMessagePushStrategy>()
                                    .ExecuteMessagePushStrategyTask(new BaseId { Id = sendStrategy.Id });
                                break;
                            }
                    }

                    log.Success = true;
                }
                catch (Exception ex)
                {
                    string errorMsg = ActionErrorEx(ex.Message, definitionDetail.Id, cron, content, ex.InnerException?.Message);
                    log.Result = errorMsg;
                    log.Success = false;
                    RecordLine.LastTime = dateTime;
                    RecordLine.Message = log.Result;
                    _logger.LogError(ex, "任务执行失败: {ErrorMsg}", errorMsg);
                }

                // 更新执行记录
                log.ElapsedMilliseconds = DateTimeOffset.Now.ToUnixTimeMilliseconds() - new DateTimeOffset(dateTime).ToUnixTimeMilliseconds();
                RecordLine.LastTime = dateTime;
                RecordLine.Message = log.Result;
                await _db.CopyNew().Insertable(log).SplitTable().ExecuteCommandAsync();
                RunCount++;
            });
        });
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="ex"></param>
    /// <param name="jobDetailId"></param>
    /// <param name="cron"></param>
    /// <param name="content"></param>
    /// <param name="innerEx"></param>
    /// <returns></returns>
    private static string ActionErrorEx(string ex, long jobDetailId, string cron, string content, string? innerEx = "")
    {
        // 左对齐
        return TP.WrapperRectangle(new[]
        {
            "任务执行时发生异常",
            $"报错原因:【{ex}】",
            $"执行Id:【{jobDetailId}】",
            $"执行周期:【{cron}】",
            $"执行参数:【{content}】",
            $"堆栈异常:【{innerEx}】"
        }, -1);
    }

    /// <summary>
    /// </summary>
    public void Stop()
    {
        switch (TaskDefinitionDetail.EventType)
        {
            case SysJobTaskDetailTypeEnum.Time:
                _schedulerFactory.RemoveJob(TaskDefinitionDetail.Id.ToString());
                break;
            case SysJobTaskDetailTypeEnum.Property:
                PropertyModel? tagModel = JSON.Deserialize<PropertyModel>(TaskDefinitionDetail.EventParams);
                if (tagModel != null)
                {
                    foreach (string code in tagModel.Name)
                    {
                        // Log.Information($"---------------任务已经销毁：{code}---------------------------");
                        switch (tagModel.Compare)
                        {
                            case CompareEnum.Change:
                                DataStorage.Instance.UnsubscribeValueChangedEvent(code, ValueChangedHandler);
                                break;
                            case CompareEnum.Time:
                                DataStorage.Instance.UnsubscribeTimeChangedEvent(code, TimeChangedHandler);
                                break;
                            case CompareEnum.Equi:
                            case CompareEnum.Greater:
                            case CompareEnum.Less:
                            case CompareEnum.False:
                                DataStorage.Instance.UnsubscribeCheckSubscriptionConditionEvent(code, SubscribeTimeChanged);
                                break;
                            default:
                                throw new ArgumentOutOfRangeException();
                        }
                    }
                }

                break;
            case SysJobTaskDetailTypeEnum.Variable:
            case SysJobTaskDetailTypeEnum.Message:
            case SysJobTaskDetailTypeEnum.Http:
                _eventBusFactory.Unsubscribe(TaskBase.Code);
                break;
        }

        _cancellationTokenSource.Cancel();
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        _db.Dispose();
        _cancellationTokenSource.Dispose();
    }
}