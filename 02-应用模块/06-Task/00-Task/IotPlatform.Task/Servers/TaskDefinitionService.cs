using IotPlatform.Task.HostedService;
using DateTime = System.DateTime;

namespace IotPlatform.Task.Servers;

/// <summary>
///     任务定义-任务配置
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("任务定义")]
public class TaskDefinitionService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<TaskDefinition> _sysTimerRep;
    private readonly TaskHostedService _customTaskSingleton;

    /// <summary>
    ///     定时任务
    /// </summary>
    private readonly ISchedulerFactory _schedulerFactory;

    /// <summary>
    /// </summary>
    /// <param name="sysTimerRep"></param>
    /// <param name="customTaskSingleton"></param>
    /// <param name="schedulerFactory"></param>
    public TaskDefinitionService(ISqlSugarRepository<TaskDefinition> sysTimerRep, TaskHostedService customTaskSingleton,
        ISchedulerFactory schedulerFactory)
    {
        _customTaskSingleton = customTaskSingleton;
        _schedulerFactory = schedulerFactory;
        _sysTimerRep = sysTimerRep;
    }

    #region Get

    /// <summary>
    ///     递归生成任务配置分组树
    /// </summary>
    /// <param name="taskDefinitionGroups">原始任务配置分组列表</param>
    /// <param name="timers">任务列表</param>
    /// <returns>任务配置分组树结构</returns>
    private static async Task<List<dynamic>> RecursiveTaskDefinitionTree(List<TaskDefinitionGroup> taskDefinitionGroups, List<TaskDefinition> timers)
    {
        if (taskDefinitionGroups.IsNullOrEmpty())
        {
            return new List<dynamic>();
        }

        List<dynamic> output = new();

        foreach (TaskDefinitionGroup taskDefinitionGroup in taskDefinitionGroups)
        {
            List<TaskDefinitionGroup> children = taskDefinitionGroup.Children?.ToList() ?? new List<TaskDefinitionGroup>();
            var groupTasks = timers.Where(w => w.TaskDefinitionGroupId == taskDefinitionGroup.Id).Select(s => new
            {
                s.Id,
                s.Name,
                s.Enable,
                Pid = taskDefinitionGroup.Id
            });

            var data = new
            {
                taskDefinitionGroup.Id,
                taskDefinitionGroup.Name,
                taskDefinitionGroup.Pid,
                Enable = true,
                Children = await RecursiveTaskDefinitionTree(children, timers)
            };
            data.Children.AddRange(groupTasks);
            output.Add(data);
        }

        return output;
    }

    /// <summary>
    ///     任务配置树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysTimers/tree")]
    public async Task<dynamic> TaskDefinitionTree()
    {
        // 获取所有任务配置分组
        List<TaskDefinitionGroup>? allTaskDefinitionGroups = await _sysTimerRep.AsSugarClient().Queryable<TaskDefinitionGroup>()
            .ToTreeAsync(u => u.Children, u => u.Pid, 0);

        // 获取所有任务
        List<TaskDefinition>? allTimers = await _sysTimerRep.AsQueryable()
            .Includes(i => i.TaskDefinitionGroup)
            .ToListAsync();

        // 递归生成树结构
        List<dynamic> taskDefinitionTree = await RecursiveTaskDefinitionTree(allTaskDefinitionGroups, allTimers);

        return taskDefinitionTree;
    }

    /// <summary>
    ///     任务详情最后执行记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysTimers/detail/lastResult")]
    public async Task<dynamic> TaskDefinitionDetail([FromQuery] BaseId input)
    {
        // 任务
        TaskDefinitionDetail? taskDefinitionDetail = await _sysTimerRep.AsSugarClient().Queryable<TaskDefinitionDetail>().FirstAsync(f => f.Id == input.Id);
        if (taskDefinitionDetail == null)
        {
            throw Oops.Oh("该任务已经被删除！");
        }

        // 查找任务最后执行的结果
        if (_customTaskSingleton.TaskDefinitionRunThreads.TryGetValue(taskDefinitionDetail.Id, out TaskDefinitionRun? timeline))
        {
            if (timeline != null)
            {
                if (taskDefinitionDetail.EventType == SysJobTaskDetailTypeEnum.Time)
                {
                    Trigger? trigger = _schedulerFactory.GetJob(taskDefinitionDetail.Id.ToString())?.GetTrigger(taskDefinitionDetail.Id.ToString());
                    if (trigger != null)
                    {
                        taskDefinitionDetail.LastTime = trigger.LastRunTime;
                        taskDefinitionDetail.LastExecValue = trigger.Result;
                        taskDefinitionDetail.RunNumber = trigger.NumberOfRuns;
                    }
                }
                else
                {
                    taskDefinitionDetail.LastTime = timeline.RecordLine.LastTime;
                    taskDefinitionDetail.LastExecValue = timeline.RecordLine.Message;
                    taskDefinitionDetail.RunNumber = timeline.RunCount;
                }
            }
        }

        if (timeline == null)
        {
            taskDefinitionDetail.TimerStatus = TriggerStatus.NotStart;
            taskDefinitionDetail.Exception = "任务已取消或没有该任务";
        }
           
        return taskDefinitionDetail;
    }

    /// <summary>
    ///     任务配置-日志列表
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpGet("/sysTimersRecord/page")]
    public async Task<SqlSugarPagedList<TaskDefinitionRecord>> TimerLogsPage([FromQuery] TimerLogsPageInput input)
    {
        SqlSugarPagedList<TaskDefinitionRecord>? records = await _sysTimerRep.AsSugarClient().Queryable<TaskDefinitionRecord>()
            .Where(w => w.TaskDetailId == input.Id)
            .SplitTable(DateTime.Parse(input.SearchBeginTime!.Trim()), DateTime.Parse(input.SearchEndTime.Trim()))
            .OrderByDescending(u => u.ActionTime)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return records;
    }

    /// <summary>
    ///     任务配置-获取最近5次执行时间
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysTimers/getNextTime")]
    public Task<List<string>> GetNextTime([FromQuery] GetNextTimeInput input)
    {
        Crontab? crontab = Crontab.Parse(input.Cron.ConvertCron(), CronStringFormat.WithSeconds);
        DateTime nextTime = crontab.GetNextOccurrence(DateTime.Now);
        List<string> list = new();
        for (int i = 0; i < 5; i++)
        {
            DateTime time2 = crontab.GetNextOccurrence(nextTime);
            list.Add(time2.ToString("yyyy-MM-dd HH:mm:ss"));
            nextTime = time2;
        }

        return System.Threading.Tasks.Task.FromResult(list);
    }

    /// <summary>
    ///     任务配置-下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysTimers/select")]
    public async Task<List<TaskDefinition>> Select()
    {
        List<TaskDefinition>? timers = await _sysTimerRep.AsQueryable()
            .Where(w => w.Enable == true)
            .Select<TaskDefinition>()
            .ToListAsync();
        return timers;
    }

    /// <summary>
    ///     任务配置-详情
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpGet("/sysTimers/detail")]
    public async Task<TaskDefinition> TimerDetail([FromQuery] BaseId input)
    {
        TaskDefinition? task = await _sysTimerRep.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(i => i.SysJobTaskDetails)
            .FirstAsync();
        if (task == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        return task;
    }

    #endregion Get

    #region Post

    /// <summary>
    ///     任务配置分组-保存
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/sysTimers/group/save")]
    public async System.Threading.Tasks.Task SysTimersGroupSave(SysTimersGroupSaveInput input)
    {
        if (input.Id > 0)
        {
            // 名称是否唯一
            if (await _sysTimerRep.AsSugarClient().Queryable<TaskDefinitionGroup>().AnyAsync(a => a.Name == input.Name && a.Id != input.Id))
            {
                throw Oops.Oh(ErrorCode.Com1004);
            }

            // 
            TaskDefinitionGroup? programBlockGroup = await _sysTimerRep.AsSugarClient().Queryable<TaskDefinitionGroup>().FirstAsync(f => f.Id == input.Id);
            if (programBlockGroup == null)
            {
                throw Oops.Oh("程序块分组已经被删除！");
            }

            programBlockGroup.Name = input.Name;
            programBlockGroup.Pid = input.Pid;
            await _sysTimerRep.AsSugarClient().Updateable(programBlockGroup).UpdateColumns(w => new {w.Name, w.Pid}).ExecuteCommandAsync();
        }
        else
        {
            TaskDefinitionGroup taskDefinitionGroup = new()
            {
                Pid = input.Pid,
                Name = input.Name
            };
            await _sysTimerRep.AsSugarClient().Insertable(taskDefinitionGroup).ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     任务配置分组-删除
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/sysTimers/group/delete")]
    public async System.Threading.Tasks.Task SysTimersGroupDelete(BaseId input)
    {
        // 名称是否唯一
        if (await _sysTimerRep.AsSugarClient().Queryable<TaskDefinitionGroup>().AnyAsync(a => a.Pid == input.Id) 
            || await _sysTimerRep.AsSugarClient().Queryable<TaskDefinition>().AnyAsync(a => a.TaskDefinitionGroupId == input.Id) )
        {
            throw Oops.Oh("该分组已经被使用，请先删除下级节点！");
        }

        TaskDefinitionGroup? programBlockGroup = await _sysTimerRep.AsSugarClient().Queryable<TaskDefinitionGroup>().FirstAsync(f => f.Id == input.Id);
        if (programBlockGroup == null)
        {
            throw Oops.Oh("程序块分组已经被删除！");
        }

        await _sysTimerRep.AsSugarClient().Deleteable(programBlockGroup).ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务配置-新增
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/sysTimers/add")]
    [UnitOfWork]
    public async Task<long> TimerAdd(TimerAddInput input)
    {
        if (await _sysTimerRep.IsAnyAsync(a => a.Name == input.Name))
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        // 添加功能定义
        TaskDefinition jobTask = input.Adapt<TaskDefinition>();
        jobTask.Enable = true;
        // 保存数据
        await _sysTimerRep.AsSugarClient().InsertNav(jobTask).Include(w => w.SysJobTaskDetails).ExecuteCommandAsync();
        return jobTask.Id;
    }

    /// <summary>
    ///     任务配置-修改
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/sysTimers/update")]
    [UnitOfWork]
    public async System.Threading.Tasks.Task TimerUpdate(TimerUpdateInput input)
    {
        if (await _sysTimerRep.IsAnyAsync(a => a.Name == input.Name && a.Id != input.Id))
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        // 任务
        TaskDefinition? jobTask = await _sysTimerRep.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.SysJobTaskDetails).FirstAsync();
        if (jobTask == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        jobTask.Name = input.Name;
        jobTask.Remark = input.Remark;
        jobTask.TaskDefinitionGroupId = input.TaskDefinitionGroupId;
        // 保存数据
        await _sysTimerRep.AsSugarClient().Updateable(jobTask).UpdateColumns(w => new {w.Name, w.Remark, w.TaskDefinitionGroupId}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务配置-保存
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/sysTimers/save")]
    [UnitOfWork]
    public async System.Threading.Tasks.Task TimerSave(TimerSaveInput input)
    {
        // 任务
        TaskDefinition? jobTask = await _sysTimerRep.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.SysJobTaskDetails).FirstAsync();
        if (jobTask == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 停止任务
        foreach (TaskDefinitionDetail jobTaskDetail in jobTask.SysJobTaskDetails)
        {
            await _customTaskSingleton.Stop(jobTaskDetail.Id);
        }

        jobTask.SysJobTaskDetails.Clear();

        // 映射
        jobTask.SysJobTaskDetails = input.TaskDetails.Adapt<List<TaskDefinitionDetail>>();
        // 赋值任务
        foreach (TaskDefinitionDetail sysJobTaskDetail in jobTask.SysJobTaskDetails)
        {
            sysJobTaskDetail.Id = YitIdHelper.NextId();
            sysJobTaskDetail.SysJobTask = jobTask;
        }

        // 启动任务
        if (jobTask.Enable)
        {
            foreach (TaskDefinitionDetail jobTaskDetail in jobTask.SysJobTaskDetails)
            {
                try
                {
                    await _customTaskSingleton.AddTaskDefinitionRun(jobTaskDetail);
                }
                catch (Exception e)
                {
                    Log.Error("【任务配置-修改】 启动任务失败:" + e.Message);
                }
            }
        }

        await _sysTimerRep.AsSugarClient().UpdateNav(jobTask).Include(w => w.SysJobTaskDetails).ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务配置-复制
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/sysTimers/copy")]
    [UnitOfWork]
    public async System.Threading.Tasks.Task TimerCopy(TaskCopyInput input)
    {
        // 任务
        TaskDefinition? jobTask = await _sysTimerRep.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.SysJobTaskDetails).FirstAsync();
        if (jobTask == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        jobTask.Id = YitIdHelper.NextId();
        jobTask.Name = input.Name;
        jobTask.Remark = input.Remark;
        // 
        foreach (TaskDefinitionDetail jobTaskDetail in jobTask.SysJobTaskDetails)
        {
            jobTaskDetail.SysJobTaskId = jobTask.Id;
            jobTaskDetail.Id = YitIdHelper.NextId();
        }

        await _sysTimerRep.AsSugarClient().InsertNav(jobTask).Include(w => w.SysJobTaskDetails).ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务配置-删除
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/sysTimers/delete")]
    [UnitOfWork]
    public async System.Threading.Tasks.Task Delete(BaseId<List<long>> input)
    {
        List<TaskDefinition>? jobTaskList = await _sysTimerRep.AsQueryable().Where(f => input.Id.Contains(f.Id)).Includes(w => w.SysJobTaskDetails).ToListAsync();
        foreach (TaskDefinitionDetail jobTaskDetail in jobTaskList.SelectMany(jobTask => jobTask.SysJobTaskDetails))
        {
            await _customTaskSingleton.Stop(jobTaskDetail.Id);
        }

        await _sysTimerRep.AsSugarClient()
            .DeleteNav(jobTaskList)
            .Include(w => w.SysJobTaskDetails)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务配置-启用/禁用
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/sysTimers/enable")]
    [UnitOfWork]
    public async System.Threading.Tasks.Task Enable(EnableInput<List<long>> input)
    {
        List<TaskDefinition>? jobTaskList = await _sysTimerRep.AsQueryable().Where(f => input.Id.Contains(f.Id)).Includes(w => w.SysJobTaskDetails, w => w.SysJobTask).ToListAsync();
        foreach (TaskDefinition? jobTask in jobTaskList)
        {
            jobTask.Enable = input.Enable;
            if (jobTask.Enable)
            {
                foreach (TaskDefinitionDetail jobTaskDetail in jobTask.SysJobTaskDetails)
                {
                    await _customTaskSingleton.AddTaskDefinitionRun(jobTaskDetail);
                }
            }
            else
            {
                foreach (TaskDefinitionDetail jobTaskDetail in jobTask.SysJobTaskDetails)
                {
                    await _customTaskSingleton.Stop(jobTaskDetail.Id);
                }
            }
        }

        await _sysTimerRep.AsSugarClient().Updateable(jobTaskList).UpdateColumns(w => w.Enable).ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务配置-清空定时任务执行日志
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/sysTimers/clear")]
    public async System.Threading.Tasks.Task ClearLogs(ClearLogsInput input)
    {
        string? tableName = _sysTimerRep.AsSugarClient().SplitHelper<TaskDefinitionRecord>().GetTableName(input.StartTime); //根据时间获取表名
        await _sysTimerRep.AsSugarClient().Deleteable<TaskDefinitionRecord>().AS(tableName)
            .Where(it => it.ActionTime >= input.StartTime && it.ActionTime <= input.EndTime)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务配置-分组
    /// </summary>
    /// <returns></returns>
    [HttpPost("/sysTimers/updatePid")]
    public async System.Threading.Tasks.Task UpdatePid(TaskUpdatePidInput input)
    {
        // 任务
        TaskDefinition? jobTask = await _sysTimerRep.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.SysJobTaskDetails).FirstAsync();
        if (jobTask == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        jobTask.TaskDefinitionGroupId = input.GroupId;
        // 修改上级Id
        await _sysTimerRep.AsSugarClient().Updateable(jobTask).UpdateColumns(w => w.TaskDefinitionGroupId).ExecuteCommandAsync();
    }

    #endregion Post
}