namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     工作流节点
/// </summary>
[SugarTable("business_flowNode", "工作流节点")]
public class FlowNode
{
    /// <summary>
    ///     雪花Id
    /// </summary>
    [SugarColumn(ColumnDescription = "Id", IsPrimaryKey = true)]
    public Guid Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     类型:1:开始；2:条件；3:动作；4:结束
    /// </summary>
    [SugarColumn(ColumnDescription = "类型:1:开始；2:条件；3:动作；4:结束")]
    public NodeTypeEnum NodeType { get; set; }

    /// <summary>
    ///     坐标
    /// </summary>
    [SugarColumn(ColumnDescription = "坐标")]
    public double Left { get; set; }

    /// <summary>
    ///     坐标
    /// </summary>
    [SugarColumn(ColumnDescription = "坐标")]
    public double Top { get; set; }

    /// <summary>
    ///     图标
    /// </summary>
    [SugarColumn(ColumnDescription = "图标")]
    public string Icon { get; set; }

    /// <summary>
    ///     表达式
    /// </summary>
    [SugarColumn(ColumnDescription = "表达式")]
    public string Expression { get; set; }

    /// <summary>
    ///     表达式集合
    /// </summary>
    [SugarColumn(ColumnDescription = "表达式集合", IsJson = true)]
    public List<string> ExpressionList { get; set; }

    #region 忽略字段

    /// <summary>
    ///     节点状态：1：未开始；2：执行中；3：已结束
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public FlowNodeStatusEnum Status { get; set; }

    /// <summary>
    ///     节点状态改变时间
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DateTime StatusChangeTime { get; set; }

    /// <summary>
    ///     节点状态改变时间
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string StatusChangeTimeDesc { get; set; }
    #endregion

    #region 关联表

    /// <summary>
    ///     项目Id
    /// </summary>
    [SugarColumn(ColumnDescription = "项目Id")]
    public long ProjectId { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ProjectId))]
    public FlowProject FlowProject { get; set; }

    /// <summary>
    ///     动作触发节点数据
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(WorkFlow.Entity.ActionNodeData.NodeId))]
    [SugarColumn(IsIgnore = true)]
    public List<ActionNodeData> ActionNodeData { get; set; }

    /// <summary>
    ///     节点数据
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.FlowNodeData.NodeId))]
    [SugarColumn(IsIgnore = true)]
    public List<FlowNodeData> FlowNodeData { get; set; }

    #endregion
}

/// <summary>
/// </summary>
public enum NodeTypeEnum
{
    /// <summary>
    ///     开始
    /// </summary>
    [Description("开始")] Start = 1,

    /// <summary>
    ///     条件
    /// </summary>
    [Description("条件")] If = 2,

    /// <summary>
    ///     触发
    /// </summary>
    [Description("触发")] Trigger = 3,

    /// <summary>
    ///     动作
    /// </summary>
    [Description("动作")] Action = 4,

    /// <summary>
    ///     结束
    /// </summary>
    [Description("结束")] Finish = 5
}

/// <summary>
/// </summary>
public enum FlowNodeStatusEnum
{
    /// <summary>
    ///     未开始
    /// </summary>
    [Description("未开始")] NotStarted = 1,

    /// <summary>
    ///     进行中
    /// </summary>
    [Description("进行中")] Starting = 2,

    /// <summary>
    ///     结束
    /// </summary>
    [Description("结束")] Started = 3
}