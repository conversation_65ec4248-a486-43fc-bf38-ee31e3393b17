namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     工作流节点定义数据
/// </summary>
[SugarTable("business_flowNodeData", "工作流节点定义数据")]
public class FlowNodeData : EntityTenantId
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     节点Id
    /// </summary>
    [SugarColumn(ColumnDescription = "节点Id")]
    public Guid NodeId { get; set; }

    /// <summary>
    ///     节点
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(NodeId))]
    public FlowNode FlowNode { get; set; }

    /// <summary>
    ///     数据节点定义数据类型枚举:1:读属性值;2:读数据库;3:读变量值;4:定义变量;5:执行任务
    /// </summary>
    public NodeDataTypeEnum NodeDataType { get; set; }

    /// <summary>
    ///     内容
    /// </summary>
    [SugarColumn(ColumnDescription = "具体内容", ColumnDataType = "longtext,text,clob")]
    public string Content { get; set; }
    
    /// <summary>
    ///     排序
    /// </summary>
    public short SortNum { get; set; }

    /// <summary>
    /// 数据处理
    /// </summary>
    [SugarColumn(ColumnDescription = "数据处理", ColumnDataType = "longtext,text,clob")]
    public string? ScriptContent{ get; set; }
}

/// <summary>
///     读属性值
/// </summary>
public class NodeDataPropertyModel
{
    /// <summary>
    ///     多层Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     属性名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
///     读数据库
/// </summary>
public class NodeDataDbModel
{
    /// <summary>
    ///     数据库连接Id
    /// </summary>
    public long DbLinkEntityId { get; set; }

    /// <summary>
    ///     Sql语句
    /// </summary>
    public string Sql { get; set; }

    /// <summary>
    /// 自定义映射关系
    /// </summary>
    public Dictionary<string, string> Mapper { get; set; } = new();
}

/// <summary>
///     读变量值
/// </summary>
public class NodeDataReadVariableModel
{
    /// <summary>
    ///     变量名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
///     定义变量
/// </summary>
public class NodeDataVariableModel
{
    /// <summary>
    ///     变量名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     数据类型:1:文本；2:数值;3:布尔
    /// </summary>
    public NodeDataVariableTypeEnum NodeDataVariableType { get; set; }

    /// <summary>
    ///     默认值
    /// </summary>
    public string? Default { get; set; }
}

/// <summary>
///     执行任务
/// </summary>
public class NodeDataTaskModel
{
    /// <summary>
    ///     任务Id
    /// </summary>
    public long TaskId { get; set; }

    /// <summary>
    ///     参数
    /// </summary>
    public Dictionary<string, object> Values { get; set; } = new();
}

/// <summary>
///     数据节点定义数据类型枚举
/// </summary>
public enum NodeDataTypeEnum
{
    /// <summary>
    ///     读属性值
    /// </summary>
    [Description("读属性值")] Property = 1,

    /// <summary>
    ///     读数据库
    /// </summary>
    [Description("读数据库")] Db = 2,

    /// <summary>
    ///     读变量值
    /// </summary>
    [Description("读变量值")] ReadVariable = 3,

    /// <summary>
    ///     定义变量
    /// </summary>
    [Description("定义变量")] Variable = 4,

    /// <summary>
    ///     执行任务
    /// </summary>
    [Description("执行任务")] Task = 5
}

/// <summary>
/// </summary>
public enum NodeDataVariableTypeEnum
{
    /// <summary>
    ///     文本
    /// </summary>
    [Description("文本")] Txt = 1,

    /// <summary>
    ///     数值
    /// </summary>
    [Description("数值")] Number = 2,

    /// <summary>
    ///     布尔
    /// </summary>
    [Description("布尔")] Bool = 3
}