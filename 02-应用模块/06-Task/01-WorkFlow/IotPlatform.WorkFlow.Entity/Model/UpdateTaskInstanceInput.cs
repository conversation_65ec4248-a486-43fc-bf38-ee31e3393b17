namespace IotPlatform.WorkFlow.Entity;

using System.Collections.Generic;

/// <summary>
///     更新任务实例输入
/// </summary>
public class UpdateTaskInstanceInput
{
    /// <summary>
    ///     实例ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     实例名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    ///     配置数据（键值对字典，如 {"key":"value"}）
    /// </summary>
    public Dictionary<string, string>? Config { get; set; }
}