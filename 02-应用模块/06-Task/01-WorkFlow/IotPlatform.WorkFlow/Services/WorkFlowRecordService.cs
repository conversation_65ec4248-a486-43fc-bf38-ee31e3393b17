using IotPlatform.WorkFlow.Entity;

namespace IotPlatform.WorkFlow.Services;

/// <summary>
///     任务定义-任务流执行日志
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("任务定义")]
public class WorkFlowRecordService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<FlowNodeRecord> _flowNodeRecord;

    /// <summary>
    /// </summary>
    /// <param name="flowNodeRecord">执行日志</param>
    public WorkFlowRecordService(ISqlSugarRepository<FlowNodeRecord> flowNodeRecord)
    {
        _flowNodeRecord = flowNodeRecord;
    }

    /// <summary>
    ///     任务流执行日志列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/workflowLog/page")]
    public async Task<SqlSugarPagedList<FlowNodeRecord>> RecordPage([FromQuery] RecordPageInput input)
    {
        SqlSugarPagedList<FlowNodeRecord>? flowNodeRecords = await _flowNodeRecord.AsQueryable()
            .Where(w => w.ProjectId == input.Id)
            .WhereIF(!string.IsNullOrEmpty(input.SearchBeginTime?.Trim()), u => u.StartTime >= DateTime.Parse(input.SearchBeginTime!.Trim()) &&
                                                                                u.EndTime <= DateTime.Parse(input.SearchEndTime.Trim()))
            .SplitTable(DateTime.Parse(input.SearchBeginTime!.Trim()), DateTime.Parse(input.SearchEndTime.Trim()))
            .Includes(c => c.FlowProject)
            .OrderByDescending(o => o.StartTime)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return flowNodeRecords;
    }
    
    /// <summary>
    ///     任务流执行-错误日志列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/workflowLog/error/page")]
    public async Task<SqlSugarPagedList<FlowActionNodeErrorRecord>> ErrorRecordPage([FromQuery] BasePageInput input)
    {
        var flowNodeRecords = await _flowNodeRecord.AsSugarClient().Queryable<FlowActionNodeErrorRecord>()
            .WhereIF(!string.IsNullOrEmpty(input.SearchBeginTime?.Trim()), u => u.CreatedTime >= DateTime.Parse(input.SearchBeginTime!.Trim()) &&
                                                                                u.EndTime <= DateTime.Parse(input.SearchEndTime.Trim()))
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue),w => w.FlowProjectName.Contains(input.SearchValue))
            .SplitTable(DateTime.Parse(input.SearchBeginTime!.Trim()), DateTime.Parse(input.SearchEndTime.Trim()))
            .OrderByDescending(o => o.CreatedTime)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return flowNodeRecords;
    }
}