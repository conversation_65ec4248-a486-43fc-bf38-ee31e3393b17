namespace IotPlatform.Extend.Entity;

/// <summary>
///     知识文档
/// </summary>
[SugarTable("EXT_DOCUMENT")]
public class DocumentEntity : EntityTenant
{
    /// <summary>
    ///     文档父级.
    /// </summary>
    [SugarColumn(ColumnName = "F_PARENT_ID")]
    public long? ParentId { get; set; }

    /// <summary>
    ///     文档分类:【0-文件夹、1-文件】.
    /// </summary>
    [SugarColumn(ColumnName = "F_TYPE")]
    public int? Type { get; set; }

    /// <summary>
    ///     文件名称.
    /// </summary>
    [SugarColumn(ColumnName = "F_FULL_NAME")]
    public string? FullName { get; set; }

    /// <summary>
    ///     文件路径.
    /// </summary>
    [SugarColumn(ColumnName = "F_FILE_PATH")]
    public string? FilePath { get; set; }

    /// <summary>
    ///     文件大小.
    /// </summary>
    [SugarColumn(ColumnName = "F_FILE_SIZE")]
    public string? FileSize { get; set; }

    /// <summary>
    ///     文件后缀.
    /// </summary>
    [SugarColumn(ColumnName = "F_FILE_EXTENSION")]
    public string? FileExtension { get; set; }

    /// <summary>
    ///     阅读数量.
    /// </summary>
    [SugarColumn(ColumnName = "F_READ_COUNT")]
    public int? ReadCount { get; set; }

    /// <summary>
    ///     是否共享.
    /// </summary>
    [SugarColumn(ColumnName = "F_IS_SHARE")]
    public int? IsShare { get; set; }

    /// <summary>
    ///     共享时间.
    /// </summary>
    [SugarColumn(ColumnName = "F_SHARE_TIME")]
    public DateTime? ShareTime { get; set; }

    /// <summary>
    ///     描述.
    /// </summary>
    [SugarColumn(ColumnName = "F_DESCRIPTION")]
    public string? Description { get; set; }

    /// <summary>
    ///     下载地址.
    /// </summary>
    [SugarColumn(ColumnName = "F_UPLOAD_URL")]
    public string? UploadUrl { get; set; }
    
    /// <summary>
    /// 排序码.
    /// </summary>
    [SugarColumn(ColumnName = "F_SORT_CODE", ColumnDescription = "排序码")]
    public virtual long? SortCode { get; set; }

    /// <summary>
    /// 获取或设置 启用标识
    /// 0-禁用,1-启用.
    /// </summary>
    [SugarColumn(ColumnName = "F_ENABLED_MARK", ColumnDescription = "启用标识")]
    public virtual int? EnabledMark { get; set; } = 1;

}