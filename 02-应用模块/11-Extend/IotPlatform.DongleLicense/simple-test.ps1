Write-Host "=== IotPlatform.DongleLicense Cross-Platform Test ===" -ForegroundColor Green
Write-Host ""

Write-Host "Testing current platform..." -ForegroundColor Yellow

try {
    # Run the program and capture output
    $output = dotnet run 2>&1 | Out-String
    
    Write-Host "Program output:" -ForegroundColor Gray
    Write-Host $output -ForegroundColor White
    
    # Check for key indicators
    if ($output -match "编译时DLL文件: (.+)") {
        $compileDll = $matches[1].Trim()
        Write-Host "Compile-time DLL: $compileDll" -ForegroundColor Green
    }
    
    if ($output -match "运行时推荐DLL: (.+)") {
        $runtimeDll = $matches[1].Trim()
        Write-Host "Runtime recommended DLL: $runtimeDll" -ForegroundColor Green
    }
    
    if ($output -match "DLL文件匹配当前运行环境") {
        Write-Host "SUCCESS: DLL files match current environment" -ForegroundColor Green
    } elseif ($output -match "警告.*不一致") {
        Write-Host "WARNING: DLL files do not match" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Testing Linux ARM64 publish..." -ForegroundColor Yellow

try {
    $publishResult = dotnet publish -r linux-arm64 --no-self-contained -v quiet 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Linux ARM64 publish completed" -ForegroundColor Green
        
        $publishDir = "bin\Release\net8.0\linux-arm64\publish"
        if (Test-Path $publishDir) {
            Write-Host "SUCCESS: Publish directory exists" -ForegroundColor Green
            $exeFile = Join-Path $publishDir "IotPlatform.DongleLicense"
            if (Test-Path $exeFile) {
                Write-Host "SUCCESS: Executable file created" -ForegroundColor Green
            }
        }
    } else {
        Write-Host "ERROR: Linux ARM64 publish failed" -ForegroundColor Red
    }
}
catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
