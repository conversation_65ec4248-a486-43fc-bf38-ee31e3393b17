#!/usr/bin/env pwsh
# Linux ARM64 部署问题修复脚本
# 用于快速诊断和修复Linux ARM64环境下的DLL加载问题

param(
    [string]$TargetPath = "",
    [switch]$AutoFix = $false,
    [switch]$Verbose = $false
)

Write-Host "=== Linux ARM64 部署问题诊断和修复脚本 ===" -ForegroundColor Green
Write-Host ""

# 1. 检查当前编译结果
Write-Host "1. 检查当前编译结果..." -ForegroundColor Yellow

try {
    if (Test-Path "bin") {
        Write-Host "✅ 找到编译输出目录" -ForegroundColor Green
        
        # 查找现有的发布目录
        $publishDirs = Get-ChildItem "bin" -Recurse -Directory -Name "publish" | ForEach-Object {
            $fullPath = Join-Path "bin" $_
            $parentPath = Split-Path $fullPath -Parent
            @{
                Path = $fullPath
                Platform = Split-Path $parentPath -Leaf
                FullPath = $fullPath
            }
        }
        
        if ($publishDirs.Count -gt 0) {
            Write-Host "找到以下发布目录:" -ForegroundColor Cyan
            foreach ($dir in $publishDirs) {
                Write-Host "  - $($dir.Platform): $($dir.Path)" -ForegroundColor White
            }
        } else {
            Write-Host "⚠️  未找到发布目录" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  未找到编译输出目录" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 检查编译结果时出错: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 运行诊断测试
Write-Host ""
Write-Host "2. 运行诊断测试..." -ForegroundColor Yellow

try {
    $testOutput = dotnet run 2>&1 | Out-String
    
    if ($testOutput -match "编译时DLL文件: (.+)") {
        $compileDll = $matches[1].Trim()
        Write-Host "编译时DLL: $compileDll" -ForegroundColor Cyan
        
        if ($compileDll -eq "Dongle_d.dll") {
            Write-Host "❌ 问题确认: 编译时使用了Windows DLL" -ForegroundColor Red
            $needsFix = $true
        } elseif ($compileDll -eq "libRockeyARM.so.0.3") {
            Write-Host "✅ 编译时DLL正确" -ForegroundColor Green
            $needsFix = $false
        } else {
            Write-Host "⚠️  编译时DLL: $compileDll (请确认是否正确)" -ForegroundColor Yellow
            $needsFix = $true
        }
    }
    
    if ($testOutput -match "运行时推荐DLL: (.+)") {
        $runtimeDll = $matches[1].Trim()
        Write-Host "运行时推荐DLL: $runtimeDll" -ForegroundColor Cyan
    }
    
    if ($testOutput -match "当前运行平台: (.+)") {
        $platform = $matches[1].Trim()
        Write-Host "当前平台: $platform" -ForegroundColor Cyan
    }
    
    if ($testOutput -match "当前处理器架构: (.+)") {
        $architecture = $matches[1].Trim()
        Write-Host "处理器架构: $architecture" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "❌ 运行诊断测试时出错: $($_.Exception.Message)" -ForegroundColor Red
    $needsFix = $true
}

# 3. 提供修复方案
Write-Host ""
Write-Host "3. 修复方案..." -ForegroundColor Yellow

if ($needsFix -or $AutoFix) {
    Write-Host "检测到需要修复的问题" -ForegroundColor Red
    Write-Host ""
    
    Write-Host "=== 修复步骤 ===" -ForegroundColor Yellow
    Write-Host "1. 重新发布到Linux ARM64平台" -ForegroundColor White
    Write-Host "2. 确保使用正确的Runtime Identifier" -ForegroundColor White
    Write-Host "3. 验证修复结果" -ForegroundColor White
    Write-Host ""
    
    if ($AutoFix) {
        Write-Host "正在自动修复..." -ForegroundColor Green
        
        # 清理现有的linux-arm64发布
        $linuxArm64Dir = "bin/Release/net8.0/linux-arm64"
        if (Test-Path $linuxArm64Dir) {
            Write-Host "清理现有的Linux ARM64发布目录..." -ForegroundColor Gray
            Remove-Item $linuxArm64Dir -Recurse -Force
        }
        
        # 重新发布
        Write-Host "重新发布到Linux ARM64..." -ForegroundColor Yellow
        $publishResult = dotnet publish -r linux-arm64 --no-self-contained -c Release 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 重新发布成功！" -ForegroundColor Green
            
            # 验证修复结果
            Write-Host "验证修复结果..." -ForegroundColor Yellow
            $verifyOutput = dotnet run 2>&1 | Out-String
            
            if ($verifyOutput -match "编译时DLL文件: (.+)") {
                $newCompileDll = $matches[1].Trim()
                if ($newCompileDll -eq "libRockeyARM.so.0.3") {
                    Write-Host "✅ 修复成功！现在使用正确的DLL: $newCompileDll" -ForegroundColor Green
                } else {
                    Write-Host "⚠️  修复后的DLL: $newCompileDll (请确认)" -ForegroundColor Yellow
                }
            }
            
            if ($verifyOutput -match "DLL文件匹配当前运行环境") {
                Write-Host "✅ DLL文件匹配检查通过" -ForegroundColor Green
            }
            
        } else {
            Write-Host "❌ 重新发布失败" -ForegroundColor Red
            Write-Host $publishResult -ForegroundColor Red
        }
    } else {
        Write-Host "手动修复命令:" -ForegroundColor Cyan
        Write-Host "  dotnet publish -r linux-arm64 --no-self-contained -c Release" -ForegroundColor White
        Write-Host ""
        Write-Host "或使用自动修复:" -ForegroundColor Cyan
        Write-Host "  ./fix-linux-arm64-deployment.ps1 -AutoFix" -ForegroundColor White
    }
} else {
    Write-Host "✅ 未检测到需要修复的问题" -ForegroundColor Green
}

# 4. 部署检查清单
Write-Host ""
Write-Host "=== 部署检查清单 ===" -ForegroundColor Yellow
Write-Host "□ 使用正确的发布命令 (dotnet publish -r linux-arm64)" -ForegroundColor White
Write-Host "□ 编译时DLL为 libRockeyARM.so.0.3" -ForegroundColor White
Write-Host "□ 目标服务器上有 libRockeyARM.so.0.3 文件" -ForegroundColor White
Write-Host "□ DLL文件有正确的权限 (chmod 755)" -ForegroundColor White
Write-Host "□ 应用程序有执行权限 (chmod +x)" -ForegroundColor White

# 5. 生成部署包（如果指定了目标路径）
if ($TargetPath -ne "") {
    Write-Host ""
    Write-Host "5. 生成部署包..." -ForegroundColor Yellow
    
    $publishDir = "bin/Release/net8.0/linux-arm64/publish"
    if (Test-Path $publishDir) {
        try {
            if (!(Test-Path $TargetPath)) {
                New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
            }
            
            Copy-Item "$publishDir/*" $TargetPath -Recurse -Force
            Write-Host "✅ 部署包已生成到: $TargetPath" -ForegroundColor Green
            
            # 创建部署说明文件
            $deploymentNotes = @"
# Linux ARM64 部署说明

## 文件清单
$(Get-ChildItem $TargetPath | ForEach-Object { "- $($_.Name)" } | Out-String)

## 部署步骤
1. 将此目录中的所有文件复制到目标服务器
2. 确保目标服务器上有 libRockeyARM.so.0.3 文件
3. 设置执行权限: chmod +x IotPlatform.DongleLicense
4. 运行测试: ./IotPlatform.DongleLicense

## 预期的DLL文件
- libRockeyARM.so.0.3 (Linux ARM64版本的加密锁库文件)

生成时间: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
"@
            $deploymentNotes | Out-File -FilePath (Join-Path $TargetPath "DEPLOYMENT-NOTES.txt") -Encoding UTF8
            
        } catch {
            Write-Host "❌ 生成部署包时出错: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 发布目录不存在: $publishDir" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "诊断和修复完成！" -ForegroundColor Green
