using Microsoft.Extensions.Configuration;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密狗授权服务 - 负责授权密钥的持久化存储
/// </summary>
public class DongleAuthService
{
    private readonly string _authFilePath;
    private readonly object _lockObject = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">配置服务</param>
    public DongleAuthService(IConfiguration configuration)
    {
        // 获取授权文件存储路径，默认存储在应用程序目录下
        var authDirectory = configuration["DongleAuth:StoragePath"] ?? 
                           Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
        
        Directory.CreateDirectory(authDirectory);
        _authFilePath = Path.Combine(authDirectory, "dongle.auth");
    }

    /// <summary>
    /// 保存授权密钥（一次性操作）
    /// </summary>
    /// <param name="hardwareId">硬件ID</param>
    /// <returns>是否保存成功</returns>
    public bool SaveAuthKey(string hardwareId)
    {
        lock (_lockObject)
        {
            try
            {
                // 检查是否已存在授权文件
                if (File.Exists(_authFilePath))
                {
                    return false; // 已存在，不允许重复保存
                }

                // 生成MD5加密的授权密钥
                var authKey = GenerateMD5Hash(hardwareId);
                
                // 创建授权数据
                var authData = new
                {
                    AuthKey = authKey,
                    OriginalHardwareId = hardwareId, // 仅用于调试，实际验证不使用
                    CreatedTime = DateTime.Now,
                    Version = "1.0"
                };

                // 序列化并保存到文件
                var jsonData = JsonSerializer.Serialize(authData, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                
                File.WriteAllText(_authFilePath, jsonData, Encoding.UTF8);
                
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// 获取存储的授权密钥
    /// </summary>
    /// <returns>授权密钥，如果不存在则返回null</returns>
    public string? GetStoredAuthKey()
    {
        lock (_lockObject)
        {
            try
            {
                if (!File.Exists(_authFilePath))
                {
                    return null;
                }

                var jsonData = File.ReadAllText(_authFilePath, Encoding.UTF8);
                var authData = JsonSerializer.Deserialize<JsonElement>(jsonData);
                
                return authData.GetProperty("AuthKey").GetString();
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// 验证硬件ID是否与存储的授权密钥匹配
    /// </summary>
    /// <param name="currentHardwareId">当前硬件ID</param>
    /// <returns>验证结果</returns>
    public (bool IsValid, string Status) ValidateAuth(string currentHardwareId)
    {
        try
        {
            var storedAuthKey = GetStoredAuthKey();
            
            if (string.IsNullOrEmpty(storedAuthKey))
            {
                return (false, "未找到授权密钥");
            }

            if (string.IsNullOrEmpty(currentHardwareId))
            {
                return (false, "无法获取当前硬件ID");
            }

            // 生成当前硬件ID的MD5
            var currentAuthKey = GenerateMD5Hash(currentHardwareId);
            
            // 对比授权密钥
            var isValid = string.Equals(storedAuthKey, currentAuthKey, StringComparison.OrdinalIgnoreCase);
            
            return isValid ? (true, "授权验证通过") : (false, "硬件ID不匹配");
        }
        catch (Exception ex)
        {
            return (false, $"验证异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查是否已初始化授权
    /// </summary>
    /// <returns>是否已初始化</returns>
    public bool IsAuthInitialized()
    {
        return File.Exists(_authFilePath);
    }

    /// <summary>
    /// 获取授权信息（用于调试和状态查看）
    /// </summary>
    /// <returns>授权信息</returns>
    public dynamic GetAuthInfo()
    {
        lock (_lockObject)
        {
            try
            {
                if (!File.Exists(_authFilePath))
                {
                    return new { IsInitialized = false, Message = "未初始化授权" };
                }

                var jsonData = File.ReadAllText(_authFilePath, Encoding.UTF8);
                var authData = JsonSerializer.Deserialize<JsonElement>(jsonData);
                
                return new
                {
                    IsInitialized = true,
                    AuthKey = authData.GetProperty("AuthKey").GetString(),
                    CreatedTime = authData.GetProperty("CreatedTime").GetDateTime(),
                    Version = authData.GetProperty("Version").GetString(),
                    FilePath = _authFilePath
                };
            }
            catch (Exception ex)
            {
                return new { IsInitialized = false, Message = $"读取授权信息失败: {ex.Message}" };
            }
        }
    }

    /// <summary>
    /// 生成MD5哈希值
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>MD5哈希值</returns>
    private static string GenerateMD5Hash(string input)
    {
        using var md5 = MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(input);
        var hashBytes = md5.ComputeHash(inputBytes);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }
}
