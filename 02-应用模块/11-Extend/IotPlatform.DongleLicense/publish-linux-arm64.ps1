#!/usr/bin/env pwsh
# Linux ARM64 发布脚本
# 用于正确发布到Linux ARM64平台，确保使用正确的DLL文件

param(
    [string]$Configuration = "Release",
    [switch]$SelfContained = $false,
    [switch]$Verbose = $false
)

Write-Host "=== IotPlatform.DongleLicense Linux ARM64 发布脚本 ===" -ForegroundColor Green
Write-Host ""

# 设置发布参数
$RuntimeIdentifier = "linux-arm64"
$PublishArgs = @(
    "publish"
    "-r", $RuntimeIdentifier
    "-c", $Configuration
)

if ($SelfContained) {
    $PublishArgs += "--self-contained"
    Write-Host "发布模式: 自包含" -ForegroundColor Yellow
} else {
    $PublishArgs += "--no-self-contained"
    Write-Host "发布模式: 框架依赖" -ForegroundColor Yellow
}

if ($Verbose) {
    $PublishArgs += "-v", "normal"
}

Write-Host "目标平台: $RuntimeIdentifier" -ForegroundColor Cyan
Write-Host "配置: $Configuration" -ForegroundColor Cyan
Write-Host ""

# 清理之前的发布输出
$PublishDir = "bin/$Configuration/net8.0/$RuntimeIdentifier/publish"
if (Test-Path $PublishDir) {
    Write-Host "清理之前的发布输出..." -ForegroundColor Gray
    Remove-Item $PublishDir -Recurse -Force
}

# 执行发布
Write-Host "正在发布..." -ForegroundColor Yellow
Write-Host "命令: dotnet $($PublishArgs -join ' ')" -ForegroundColor Gray

try {
    $publishResult = & dotnet @PublishArgs 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 发布成功！" -ForegroundColor Green
        
        # 检查发布结果
        if (Test-Path $PublishDir) {
            Write-Host "✅ 发布目录: $PublishDir" -ForegroundColor Green
            
            # 检查主要文件
            $mainFiles = @(
                "IotPlatform.DongleLicense.dll",
                "IotPlatform.DongleLicense",
                "IotPlatform.DongleLicense.runtimeconfig.json"
            )
            
            foreach ($file in $mainFiles) {
                $filePath = Join-Path $PublishDir $file
                if (Test-Path $filePath) {
                    Write-Host "✅ $file" -ForegroundColor Green
                } else {
                    Write-Host "⚠️  $file (未找到)" -ForegroundColor Yellow
                }
            }
            
            # 检查是否需要的Linux ARM64 DLL文件
            $expectedDll = "libRockeyARM.so.0.3"
            Write-Host ""
            Write-Host "预期的DLL文件: $expectedDll" -ForegroundColor Cyan
            Write-Host "注意: 您需要手动将 $expectedDll 文件复制到发布目录或目标服务器上" -ForegroundColor Yellow
            
            # 显示发布目录内容
            Write-Host ""
            Write-Host "发布目录内容:" -ForegroundColor Gray
            Get-ChildItem $PublishDir | ForEach-Object {
                $size = if ($_.PSIsContainer) { "<DIR>" } else { "{0:N0} bytes" -f $_.Length }
                Write-Host "  $($_.Name.PadRight(40)) $size" -ForegroundColor White
            }
        } else {
            Write-Host "❌ 发布目录不存在: $PublishDir" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 发布失败！" -ForegroundColor Red
        Write-Host $publishResult -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 发布过程中发生异常: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== 部署说明 ===" -ForegroundColor Yellow
Write-Host "1. 将发布目录中的所有文件复制到目标Linux ARM64服务器" -ForegroundColor White
Write-Host "2. 确保目标服务器上有 $expectedDll 文件" -ForegroundColor White
Write-Host "3. 如果没有该文件，请从加密锁厂商获取Linux ARM64版本的库文件" -ForegroundColor White
Write-Host "4. 运行命令: ./IotPlatform.DongleLicense" -ForegroundColor White
Write-Host ""
Write-Host "发布完成！" -ForegroundColor Green
