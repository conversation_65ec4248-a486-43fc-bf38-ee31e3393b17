# IotPlatform.DongleLicense 跨平台部署指南

## 问题描述

在Linux ARM64环境下部署时，出现以下错误：
```
System.DllNotFoundException: Unable to load shared library 'Dongle_d.dll' or one of its dependencies.
```

这是因为编译时没有正确设置平台标识符，导致使用了Windows的DLL文件名。

## 解决方案

### 方案一：正确的发布命令（推荐）

#### 1. 使用专用发布脚本

**Linux ARM64发布：**
```powershell
# 使用专用脚本
./publish-linux-arm64.ps1

# 或使用通用脚本
./publish-cross-platform.ps1 -Platform linux-arm64
```

**其他平台发布：**
```powershell
# Windows x64
./publish-cross-platform.ps1 -Platform win-x64

# Linux x64
./publish-cross-platform.ps1 -Platform linux-x64

# macOS ARM64
./publish-cross-platform.ps1 -Platform osx-arm64

# 发布所有平台
./publish-cross-platform.ps1 -Platform all
```

#### 2. 手动发布命令

```bash
# Linux ARM64
dotnet publish -r linux-arm64 --no-self-contained -c Release

# Linux x64
dotnet publish -r linux-x64 --no-self-contained -c Release

# Windows x64
dotnet publish -r win-x64 --no-self-contained -c Release
```

**重要：** 必须使用 `-r <runtime-identifier>` 参数，这样才能正确定义条件编译宏。

### 方案二：验证发布结果

发布后，可以运行测试程序验证DLL名称是否正确：

```bash
# 在发布目录中运行
./IotPlatform.DongleLicense
```

正确的输出应该显示：
```
=== 加密锁API平台信息 ===
当前运行平台: Linux 5.4.0-xxx-generic #xxx-Ubuntu SMP
当前处理器架构: Arm64
编译时DLL文件: libRockeyARM.so.0.3
运行时推荐DLL: libRockeyARM.so.0.3
✅ DLL文件匹配当前运行环境
```

## 平台对应的DLL文件

| 平台 | Runtime Identifier | DLL文件名 | 说明 |
|------|-------------------|-----------|------|
| Windows x64/x86/ARM64 | win-x64, win-x86, win-arm64 | Dongle_d.dll | Windows通用版本 |
| Linux x64/ARM64 | linux-x64, linux-arm64 | libRockeyARM.so.0.3 | Linux版本 |
| Linux x86 | linux-x86 | libDongle_d_x86.so | Linux 32位版本 |
| macOS x64 | osx-x64 | libDongle_d_x64.dylib | macOS Intel版本 |
| macOS ARM64 | osx-arm64 | libDongle_d_arm64.dylib | macOS Apple Silicon版本 |

## 部署步骤

### 1. 发布应用程序

```bash
# 使用正确的Runtime Identifier发布
dotnet publish -r linux-arm64 --no-self-contained -c Release
```

### 2. 准备DLL文件

确保目标服务器上有对应的DLL文件：

**Linux ARM64环境：**
- 需要文件：`libRockeyARM.so.0.3`
- 放置位置：与应用程序同目录，或系统库目录（如 `/usr/lib`, `/usr/local/lib`）

### 3. 复制文件到目标服务器

```bash
# 复制发布目录到目标服务器
scp -r bin/Release/net8.0/linux-arm64/publish/* user@server:/app/dongle-license/

# 设置执行权限
chmod +x /app/dongle-license/IotPlatform.DongleLicense
```

### 4. 验证部署

```bash
# 运行测试程序
cd /app/dongle-license
./IotPlatform.DongleLicense
```

## 常见问题

### Q1: 仍然提示找不到DLL文件

**原因：** 目标服务器上缺少对应的DLL文件。

**解决：**
1. 从加密锁厂商获取Linux ARM64版本的库文件
2. 将库文件放置在应用程序目录或系统库目录
3. 确保库文件有正确的权限：`chmod 755 libRockeyARM.so.0.3`

### Q2: 编译时DLL与运行时DLL不匹配

**原因：** 发布时没有使用正确的Runtime Identifier。

**解决：** 重新使用正确的发布命令：
```bash
dotnet publish -r linux-arm64 --no-self-contained -c Release
```

### Q3: 如何检查当前编译使用的DLL名称

运行测试程序，查看输出中的"编译时DLL文件"和"运行时推荐DLL"是否一致。

## 技术原理

### 条件编译机制

项目使用条件编译来处理不同平台的DLL名称：

```csharp
#if LINUX_ARM64
    const string DLL_NAME = "libRockeyARM.so.0.3";
#elif LINUX_X64
    const string DLL_NAME = "libRockeyARM.so.0.3";
#else
    const string DLL_NAME = "Dongle_d.dll";
#endif
```

### Runtime Identifier的作用

在项目文件中定义了条件编译符号：

```xml
<PropertyGroup Condition="'$(RuntimeIdentifier)' == 'linux-arm64'">
  <DefineConstants>$(DefineConstants);LINUX_ARM64</DefineConstants>
</PropertyGroup>
```

只有在发布时指定了`-r linux-arm64`，才会定义`LINUX_ARM64`宏，从而使用正确的DLL名称。

## 自动化脚本

项目提供了以下自动化脚本：

1. `publish-linux-arm64.ps1` - Linux ARM64专用发布脚本
2. `publish-cross-platform.ps1` - 通用跨平台发布脚本
3. `test-cross-platform.ps1` - 跨平台测试脚本

使用这些脚本可以确保正确的发布过程。
