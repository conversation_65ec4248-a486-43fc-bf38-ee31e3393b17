#!/usr/bin/env pwsh
# 跨平台发布脚本
# 支持发布到多个目标平台，确保使用正确的DLL文件

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("win-x64", "win-x86", "win-arm64", "linux-x64", "linux-arm64", "linux-x86", "osx-x64", "osx-arm64", "all")]
    [string]$Platform,
    
    [string]$Configuration = "Release",
    [switch]$SelfContained = $false,
    [switch]$Verbose = $false
)

# 平台配置映射
$PlatformConfigs = @{
    "win-x64" = @{
        Name = "Windows x64"
        RID = "win-x64"
        ExpectedDll = "Dongle_d.dll"
        Description = "Windows 64位版本"
    }
    "win-x86" = @{
        Name = "Windows x86"
        RID = "win-x86"
        ExpectedDll = "Dongle_d.dll"
        Description = "Windows 32位版本"
    }
    "win-arm64" = @{
        Name = "Windows ARM64"
        RID = "win-arm64"
        ExpectedDll = "Dongle_d.dll"
        Description = "Windows ARM64版本"
    }
    "linux-x64" = @{
        Name = "Linux x64"
        RID = "linux-x64"
        ExpectedDll = "libRockeyARM.so.0.3"
        Description = "Linux 64位版本"
    }
    "linux-arm64" = @{
        Name = "Linux ARM64"
        RID = "linux-arm64"
        ExpectedDll = "libRockeyARM.so.0.3"
        Description = "Linux ARM64版本"
    }
    "linux-x86" = @{
        Name = "Linux x86"
        RID = "linux-x86"
        ExpectedDll = "libDongle_d_x86.so"
        Description = "Linux 32位版本"
    }
    "osx-x64" = @{
        Name = "macOS x64"
        RID = "osx-x64"
        ExpectedDll = "libDongle_d_x64.dylib"
        Description = "macOS Intel版本"
    }
    "osx-arm64" = @{
        Name = "macOS ARM64"
        RID = "osx-arm64"
        ExpectedDll = "libDongle_d_arm64.dylib"
        Description = "macOS Apple Silicon版本"
    }
}

function Publish-Platform {
    param($Config)
    
    Write-Host ""
    Write-Host "=== 发布 $($Config.Name) ===" -ForegroundColor Green
    Write-Host "目标平台: $($Config.RID)" -ForegroundColor Cyan
    Write-Host "预期DLL: $($Config.ExpectedDll)" -ForegroundColor Cyan
    Write-Host "描述: $($Config.Description)" -ForegroundColor Gray
    
    # 设置发布参数
    $PublishArgs = @(
        "publish"
        "-r", $Config.RID
        "-c", $Configuration
    )
    
    if ($SelfContained) {
        $PublishArgs += "--self-contained"
    } else {
        $PublishArgs += "--no-self-contained"
    }
    
    if ($Verbose) {
        $PublishArgs += "-v", "normal"
    }
    
    # 清理之前的发布输出
    $PublishDir = "bin/$Configuration/net8.0/$($Config.RID)/publish"
    if (Test-Path $PublishDir) {
        Write-Host "清理之前的发布输出..." -ForegroundColor Gray
        Remove-Item $PublishDir -Recurse -Force
    }
    
    # 执行发布
    Write-Host "正在发布..." -ForegroundColor Yellow
    
    try {
        $publishResult = & dotnet @PublishArgs 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 发布成功！" -ForegroundColor Green
            
            # 检查发布结果
            if (Test-Path $PublishDir) {
                Write-Host "✅ 发布目录: $PublishDir" -ForegroundColor Green
                
                # 显示发布目录大小
                $dirSize = (Get-ChildItem $PublishDir -Recurse | Measure-Object -Property Length -Sum).Sum
                $dirSizeMB = [math]::Round($dirSize / 1MB, 2)
                Write-Host "📁 发布大小: $dirSizeMB MB" -ForegroundColor Gray
                
                return $true
            } else {
                Write-Host "❌ 发布目录不存在: $PublishDir" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "❌ 发布失败！" -ForegroundColor Red
            if ($Verbose) {
                Write-Host $publishResult -ForegroundColor Red
            }
            return $false
        }
    } catch {
        Write-Host "❌ 发布过程中发生异常: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主程序
Write-Host "=== IotPlatform.DongleLicense 跨平台发布脚本 ===" -ForegroundColor Green
Write-Host "配置: $Configuration" -ForegroundColor Cyan
Write-Host "自包含: $(if ($SelfContained) { '是' } else { '否' })" -ForegroundColor Cyan
Write-Host ""

$successCount = 0
$totalCount = 0

if ($Platform -eq "all") {
    # 发布所有平台
    Write-Host "发布所有支持的平台..." -ForegroundColor Yellow
    
    foreach ($platformKey in $PlatformConfigs.Keys) {
        $totalCount++
        if (Publish-Platform $PlatformConfigs[$platformKey]) {
            $successCount++
        }
    }
} else {
    # 发布指定平台
    if ($PlatformConfigs.ContainsKey($Platform)) {
        $totalCount = 1
        if (Publish-Platform $PlatformConfigs[$Platform]) {
            $successCount = 1
        }
    } else {
        Write-Host "❌ 不支持的平台: $Platform" -ForegroundColor Red
        Write-Host "支持的平台: $($PlatformConfigs.Keys -join ', ')" -ForegroundColor Yellow
        exit 1
    }
}

# 总结
Write-Host ""
Write-Host "=== 发布总结 ===" -ForegroundColor Green
Write-Host "成功: $successCount/$totalCount" -ForegroundColor $(if ($successCount -eq $totalCount) { 'Green' } else { 'Yellow' })

if ($successCount -gt 0) {
    Write-Host ""
    Write-Host "=== 部署说明 ===" -ForegroundColor Yellow
    Write-Host "1. 将对应平台的发布目录复制到目标服务器" -ForegroundColor White
    Write-Host "2. 确保目标服务器上有对应的DLL/SO/DYLIB文件" -ForegroundColor White
    Write-Host "3. 对于Linux/macOS，可能需要设置执行权限: chmod +x IotPlatform.DongleLicense" -ForegroundColor White
    Write-Host "4. 运行程序测试DLL加载是否正常" -ForegroundColor White
}

Write-Host ""
Write-Host "发布完成！" -ForegroundColor Green
