<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
  </PropertyGroup>

  <!-- 根据目标平台定义条件编译符号 -->
  <!-- 使用更可靠的条件判断 -->
  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'linux-arm64'">
    <DefineConstants>$(DefineConstants);LINUX_ARM64</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'linux-x64'">
    <DefineConstants>$(DefineConstants);LINUX_X64</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'linux-x86'">
    <DefineConstants>$(DefineConstants);LINUX_X86</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'win-x64' OR '$(RuntimeIdentifier)' == 'win-x86' OR '$(RuntimeIdentifier)' == 'win-arm64'">
    <DefineConstants>$(DefineConstants);WINDOWS</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'osx-x64'">
    <DefineConstants>$(DefineConstants);OSX_X64</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(RuntimeIdentifier)' == 'osx-arm64'">
    <DefineConstants>$(DefineConstants);OSX_ARM64</DefineConstants>
  </PropertyGroup>

  <!-- 如果没有指定RuntimeIdentifier，根据当前系统设置默认值 -->
  <PropertyGroup Condition="'$(RuntimeIdentifier)' == '' AND '$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Linux)))' == 'true'">
    <DefineConstants Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::ProcessArchitecture)' == 'Arm64'">$(DefineConstants);LINUX_ARM64</DefineConstants>
    <DefineConstants Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::ProcessArchitecture)' == 'X64'">$(DefineConstants);LINUX_X64</DefineConstants>
    <DefineConstants Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::ProcessArchitecture)' == 'X86'">$(DefineConstants);LINUX_X86</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(RuntimeIdentifier)' == '' AND '$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::Windows)))' == 'true'">
    <DefineConstants>$(DefineConstants);WINDOWS</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(RuntimeIdentifier)' == '' AND '$([System.Runtime.InteropServices.RuntimeInformation]::IsOSPlatform($([System.Runtime.InteropServices.OSPlatform]::OSX)))' == 'true'">
    <DefineConstants Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::ProcessArchitecture)' == 'Arm64'">$(DefineConstants);OSX_ARM64</DefineConstants>
    <DefineConstants Condition="'$([System.Runtime.InteropServices.RuntimeInformation]::ProcessArchitecture)' == 'X64'">$(DefineConstants);OSX_X64</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Dongle_d.dll.readme" />
    <None Remove="publish-linux-arm64.ps1" />
    <None Remove="fix-linux-arm64-deployment.ps1" />
    <None Remove="README-CrossPlatform.md" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Services\DongleTestService.cs" />
  </ItemGroup>

</Project>
