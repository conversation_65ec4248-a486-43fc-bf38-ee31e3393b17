# 加密狗授权验证系统

## 概述

本系统提供了一个简化的加密狗授权验证方案，确保系统只能在授权的加密狗上运行。

## 核心特性

1. **一次性授权初始化**：只能初始化一次授权密钥
2. **MD5加密存储**：将加密狗HardwareId进行MD5加密后存储
3. **持续验证**：每次检查时自动验证当前加密狗是否匹配
4. **安全存储**：授权密钥存储在本地文件中

## API接口

### 1. 初始化授权密钥（一次性操作）

```http
POST /dongleLicense/initializeAuth
```

**功能**：读取当前加密狗的HardwareId，生成MD5加密的授权密钥并存储。

**响应示例**：
```json
{
  "success": true,
  "message": "授权密钥初始化成功",
  "hardwareId": "01 02 03 04 05 06 07 08",
  "timestamp": "2024-08-01T15:30:00"
}
```

**注意**：
- 此接口只能成功调用一次
- 如果已存在授权密钥，将返回失败
- 需要确保加密狗已正确连接

### 2. 检查是否已有授权密钥

```http
GET /dongleLicense/hasAuthKey
```

**功能**：快速检查系统是否已初始化授权密钥。

**响应示例**：
```json
{
  "hasAuthKey": true,
  "isInitialized": true,
  "message": "已初始化授权密钥",
  "timestamp": "2024-08-01T15:36:00"
}
```

**用途**：
- 前端界面判断是否显示初始化按钮
- 系统启动时检查授权状态
- API调用前的预检查

### 3. 获取授权状态

```http
GET /dongleLicense/authStatus
```

**功能**：查看当前授权状态和最后一次验证结果。

**响应示例**：
```json
{
  "authInfo": {
    "isInitialized": true,
    "authKey": "a1b2c3d4e5f6...",
    "createdTime": "2024-08-01T15:30:00",
    "version": "1.0",
    "filePath": "./Config/dongle.auth"
  },
  "lastAuthCheck": {
    "time": "2024-08-01T15:35:00",
    "isAuthorized": true,
    "authStatus": "授权验证通过",
    "currentHardwareId": "01 02 03 04 05 06 07 08"
  },
  "timestamp": "2024-08-01T15:36:00"
}
```

### 4. 手动触发检查（包含授权验证）

```http
POST /dongleLicense/triggerCheck
```

**功能**：手动触发加密狗检查，同时执行授权验证。

**响应示例**：
```json
{
  "isSuccess": true,
  "errorMessage": null,
  "checkTime": "2024-08-01T15:35:00",
  "dongleCount": 1,
  "dongleInfos": [...],
  "elapsedMilliseconds": 150,
  "isAuthorized": true,
  "authorizationStatus": "授权验证通过"
}
```

### 5. 获取系统健康状态

```http
GET /dongleLicense/health
```

**功能**：获取包含授权状态的系统健康信息。

**响应示例**：
```json
{
  "service": {
    "isRunning": true,
    "status": "运行中"
  },
  "lastCheck": {
    "time": "2024-08-01T15:35:00",
    "success": true,
    "deviceCount": 1,
    "message": null,
    "authStatus": "授权验证通过"
  },
  "overall": {
    "status": "健康",
    "timestamp": "2024-08-01T15:36:00"
  }
}
```

## 使用流程

### 1. 首次部署
1. 确保加密狗已正确连接
2. 调用 `GET /dongleLicense/hasAuthKey` 检查是否已有授权密钥
3. 如果未初始化，调用 `POST /dongleLicense/initializeAuth` 初始化授权
4. 系统会自动生成并存储授权密钥

### 2. 日常运行
1. 系统会定期自动检查加密狗状态
2. 每次检查都会验证当前加密狗是否与授权密钥匹配
3. 可通过 `GET /dongleLicense/health` 查看整体状态

### 3. 状态监控
- `isAuthorized`: 是否通过授权验证
- `authorizationStatus`: 授权验证状态描述
- 系统只有在 `isSuccess=true` 且 `isAuthorized=true` 时才认为是健康状态

## 安全机制

1. **一次性初始化**：防止授权密钥被恶意修改
2. **MD5加密**：原始HardwareId不会明文存储
3. **文件存储**：授权密钥存储在本地文件中，便于备份和迁移
4. **持续验证**：每次检查都会重新验证授权状态

## 配置说明

在 `appsettings.json` 中添加：

```json
{
  "DongleAuth": {
    "StoragePath": "./Config"
  }
}
```

- `StoragePath`: 授权文件存储路径，默认为 `./Config`

## 故障排除

### 授权验证失败
1. 检查加密狗是否正确连接
2. 确认是否使用了正确的加密狗（HardwareId匹配）
3. 检查授权文件是否存在且未损坏

### 无法初始化授权
1. 确认加密狗已正确连接并可读取
2. 检查是否已经初始化过授权
3. 确认应用程序有写入配置目录的权限

## 文件结构

```
Config/
└── dongle.auth          # 授权密钥文件（JSON格式）
```

授权文件内容示例：
```json
{
  "AuthKey": "a1b2c3d4e5f6789...",
  "OriginalHardwareId": "01 02 03 04 05 06 07 08",
  "CreatedTime": "2024-08-01T15:30:00",
  "Version": "1.0"
}
```
