using System;
using IotPlatform.DongleLicense.Services;

namespace IotPlatform.DongleLicense
{
    /// <summary>
    /// 测试程序 - 验证跨平台DLL加载功能
    /// </summary>
    public class TestProgram
    {
        public static void Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("=== IotPlatform.DongleLicense 跨平台测试 ===");
            Console.WriteLine();

            // 显示平台信息
            Console.WriteLine(DongleApiService.GetPlatformInfoString());
            Console.WriteLine();

            // 显示详细的平台信息
            var platformInfo = DongleApiService.GetPlatformInfo();
            Console.WriteLine("=== 详细平台信息 ===");
            foreach (var kvp in platformInfo)
            {
                Console.WriteLine($"{kvp.Key}: {kvp.Value}");
            }
            Console.WriteLine();

            // 尝试创建DongleApiService实例
            try
            {
                var dongleService = new DongleApiService();
                Console.WriteLine("✅ DongleApiService 实例创建成功");
                
                // 尝试枚举设备（这可能会失败，因为没有实际的加密锁设备）
                Console.WriteLine("正在尝试枚举加密锁设备...");
                var (result, dongleInfo, count) = dongleService.EnumDongle();
                
                if (result == 0)
                {
                    Console.WriteLine($"✅ 枚举成功！找到 {count} 个设备");
                }
                else
                {
                    Console.WriteLine($"⚠️  枚举失败，错误代码: 0x{result:X8}");
                    Console.WriteLine("这是正常的，因为可能没有连接加密锁设备或缺少相应的DLL文件");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建DongleApiService时发生异常: {ex.Message}");
                Console.WriteLine("这可能是因为缺少对应平台的DLL文件");
            }

            Console.WriteLine();
            Console.WriteLine("测试完成。按任意键退出...");
            Console.ReadKey();
        }
    }
}
