#!/usr/bin/env pwsh
# 跨平台DLL测试脚本
# 用于验证不同平台的编译结果是否正确

Write-Host "=== IotPlatform.DongleLicense 跨平台测试脚本 ===" -ForegroundColor Green
Write-Host ""

# 测试不同平台的发布
$platforms = @(
    @{ Name = "Windows x64"; RID = "win-x64"; ExpectedDll = "Dongle_d.dll" },
    @{ Name = "Linux x64"; RID = "linux-x64"; ExpectedDll = "libRockeyARM.so.0.3" },
    @{ Name = "Linux ARM64"; RID = "linux-arm64"; ExpectedDll = "libRockeyARM.so.0.3" },
    @{ Name = "macOS x64"; RID = "osx-x64"; ExpectedDll = "libDongle_d_x64.dylib" },
    @{ Name = "macOS ARM64"; RID = "osx-arm64"; ExpectedDll = "libDongle_d_arm64.dylib" }
)

foreach ($platform in $platforms) {
    Write-Host "测试平台: $($platform.Name)" -ForegroundColor Yellow
    Write-Host "Runtime ID: $($platform.RID)" -ForegroundColor Cyan
    Write-Host "期望的DLL: $($platform.ExpectedDll)" -ForegroundColor Cyan
    
    try {
        # 发布到指定平台
        Write-Host "正在发布..." -ForegroundColor Gray
        $publishResult = dotnet publish -r $platform.RID --no-self-contained -v quiet 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: 发布成功" -ForegroundColor Green
            
            # 检查发布目录
            $publishDir = "bin\Release\net8.0\$($platform.RID)\publish"
            if (Test-Path $publishDir) {
                Write-Host "SUCCESS: 发布目录存在: $publishDir" -ForegroundColor Green
                
                # 列出主要文件
                $mainFiles = Get-ChildItem $publishDir -Name "IotPlatform.DongleLicense*"
                Write-Host "📁 主要文件: $($mainFiles -join ', ')" -ForegroundColor Gray
            } else {
                Write-Host "ERROR: 发布目录不存在: $publishDir" -ForegroundColor Red
            }
        } else {
            Write-Host "ERROR: 发布失败" -ForegroundColor Red
            Write-Host $publishResult -ForegroundColor Red
        }
    }
    catch {
        Write-Host "ERROR: 发布过程中出现异常: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

Write-Host "=== 测试当前平台运行 ===" -ForegroundColor Green
Write-Host ""

try {
    Write-Host "正在运行当前平台版本..." -ForegroundColor Yellow
    
    # 运行程序并捕获输出
    $output = dotnet run 2>&1
    
    if ($output -match "编译时DLL文件: (.+)") {
        $compileDll = $matches[1]
        Write-Host "SUCCESS: 编译时DLL: $compileDll" -ForegroundColor Green
    }

    if ($output -match "运行时推荐DLL: (.+)") {
        $runtimeDll = $matches[1]
        Write-Host "SUCCESS: 运行时推荐DLL: $runtimeDll" -ForegroundColor Green
    }
    
    if ($output -match "DLL文件匹配当前运行环境") {
        Write-Host "SUCCESS: DLL文件匹配检查通过" -ForegroundColor Green
    } elseif ($output -match "警告.*不一致") {
        Write-Host "WARNING: DLL文件不匹配警告" -ForegroundColor Yellow
    }
    
    if ($output -match "当前运行平台: (.+)") {
        $platform = $matches[1]
        Write-Host "INFO: 当前平台: $platform" -ForegroundColor Cyan
    }

    if ($output -match "当前处理器架构: (.+)") {
        $arch = $matches[1]
        Write-Host "INFO: 处理器架构: $arch" -ForegroundColor Cyan
    }
}
catch {
    Write-Host "ERROR: 运行测试时出现异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果所有平台都能成功发布，说明跨平台配置正确！" -ForegroundColor Gray
