using System.IO.Compression;
using Common.Core.Manager.Files;
using Common.Core.Manager.User;
using Common.Security;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Furion.DatabaseAccessor;
using Furion.DataEncryption;
using IotPlatform.Core;
using IotPlatform.Extend.Entity;
using IotPlatform.Extend.Entity.Document;
using Mapster;
using DateTime = System.DateTime;

namespace IotPlatform.Extend;

/// <summary>
/// 知识管理
/// </summary>
[ApiDescriptionSettings("系统服务", Order = 601)]
public class DocumentService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<DocumentEntity> _repository;
    private readonly IFileManager _fileManager;
    private readonly IUserManager _userManager;
    private readonly SysCacheService _cacheManager;

    public DocumentService(ISqlSugarRepository<DocumentEntity> repository, IFileManager fileManager, IUserManager userManager, SysCacheService cacheManager)
    {
        _repository = repository;
        _fileManager = fileManager;
        _userManager = userManager;
        _cacheManager = cacheManager;
    }

    #region Get

    /// <summary>
    /// 列表（全部文档）.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <param name="parentId">文档层级.</param>
    /// <returns></returns>
    [HttpGet("/document/list")]
    public async Task<dynamic> GetAllList([FromQuery] KeywordInput input, long parentId)
    {
        var data = new List<DocumentListOutput>();

        if (input.keyword.IsNullOrEmpty())
        {
            data = (await _repository.AsQueryable().Where(m => m.CreatedUserId == _userManager.UserId && m.ParentId == parentId && m.EnabledMark == 1)
                .OrderBy(x => x.Type).OrderBy(x => x.CreatedTime, OrderByType.Desc)
                .ToListAsync()).Adapt<List<DocumentListOutput>>();
        }
        else
        {
            var list = new List<DocumentEntity>();
            await GetChildSrcList(parentId, list, 0, 1);
            data = (list.FindAll(it => it.Type == 1 && it.FullName.Contains(input.keyword))).Adapt<List<DocumentListOutput>>();
            foreach (var item in data) item.parentId = parentId;
        }

        string[]? typeList = new string[] { "doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "jpg", "jpeg", "gif", "png", "bmp" };
        foreach (var item in data)
        {
            string? type = item.fullName.Split('.').LastOrDefault();
            item.isPreview = typeList.Contains(type) ? "1" : null;
        }

        return new { list = data };
    }

    /// <summary>
    /// 列表（我的分享）.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <param name="parentId"></param>
    /// <returns></returns>
    [HttpGet("/document/Share")]
    public async Task<dynamic> GetShareOutList([FromQuery] KeywordInput input, long parentId)
    {
        var data = await _repository.AsQueryable()
            .Where(it => it.CreatedUserId == _userManager.UserId && it.EnabledMark == 1)
            .WhereIF(parentId == 0, it => it.IsShare > 0)
            .WhereIF(parentId != 0, it => it.ParentId == parentId)
            .OrderBy(it => it.Type).OrderBy(it => it.ShareTime, OrderByType.Desc)
            .ToListAsync();

        if (input.keyword.IsNotEmptyOrNull())
        {
            var newList = new List<DocumentEntity>();
            newList.AddRange(data);
            foreach (var item in data)
            {
                await GetChildSrcList(item.Id, newList, 0, 1);
            }

            data = newList.FindAll(it => it.Type == 1 && it.FullName.Contains(input.keyword));
            foreach (var item in data) item.ParentId = parentId;
        }

        foreach (var item in data)
        {
            if (parentId != 0) item.ShareTime = null;
        }

        return new { list = data.Adapt<List<DocumentShareOutput>>() };
    }

    /// <summary>
    /// 列表（共享给我）.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <param name="parentId"></param>
    /// <returns></returns>
    [HttpGet("/document/ShareTome")]
    public async Task<dynamic> GetShareTomeList([FromQuery] KeywordInput input, long parentId)
    {
        var data = await _repository.AsSugarClient().Queryable<DocumentEntity, DocumentShareEntity>((a, b) => new JoinQueryInfos(JoinType.Left, a.Id == b.DocumentId))
            .Where(a => a.EnabledMark == 1)
            .WhereIF(parentId == 0, (a, b) => b.ShareUserId == _userManager.UserId)
            .WhereIF(parentId != 0, a => a.ParentId == parentId)
            .OrderBy(a => a.Type).OrderBy(a => a.ShareTime, OrderByType.Desc)
            .ToListAsync();

        if (input.keyword.IsNotEmptyOrNull())
        {
            var newList = new List<DocumentEntity>();
            newList.AddRange(data);
            foreach (var item in data)
            {
                await GetChildSrcList(item.Id, newList, 1, 1);
            }

            data = newList.FindAll(it => it.Type == 1 && it.FullName.Contains(input.keyword));
            foreach (var item in data) item.ParentId = parentId;
        }

        foreach (var item in data)
        {
            if (parentId == 0)
            {
                // item.CreatedUserId = await _repository.AsSugarClient().Queryable<SysUser>().Where(it => it.Id == item.CreatedUserId).Select(it => SqlFunc.MergeString(it.RealName, "/", it.Account)).FirstAsync();
            }
            else
            {
                item.ShareTime = null;
                item.CreatedUserId = null;
            }
        }

        return new { list = data.Adapt<List<DocumentShareTomeOutput>>() };
    }

    /// <summary>
    /// 列表（回收站）.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("/document/page/Trash")]
    public async Task<dynamic> GetTrashList([FromQuery] KeywordInput input)
    {
        var logList = await _repository.AsSugarClient().Queryable<DocumentLogEntity>()
            .Select(it => it.DocumentId)
            .ToListAsync();

        var data = (await _repository.AsQueryable()
            .Where(it => it.CreatedUserId == _userManager.UserId && it.EnabledMark == 0 && logList.Contains(it.Id))
            .WhereIF(input.keyword.IsNotEmptyOrNull(), it => it.FullName.Contains(input.keyword))
            .OrderBy(it => it.Type).OrderBy(it => it.UpdatedTime, OrderByType.Desc)
            .ToListAsync()).Adapt<List<DocumentTrashOutput>>();
        return new { list = data };
    }

    /// <summary>
    /// 列表（共享人员）.
    /// </summary>
    /// <param name="documentId">文档主键.</param>
    /// <returns></returns>
    [HttpGet("/document/ShareUser/{documentId}")]
    public async Task<dynamic> GetShareUserList(long documentId)
    {
        var data = (await _repository.AsSugarClient().Queryable<DocumentShareEntity>().Where(x => x.DocumentId == documentId).OrderBy(x => x.ShareTime, OrderByType.Desc).ToListAsync()).Adapt<List<DocumentShareUserOutput>>();
        return new { list = data };
    }

    /// <summary>
    /// 信息.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpGet("/document/{id}")]
    public async Task<dynamic> GetInfo(long id)
    {
        var data = (await _repository.GetFirstAsync(x => x.Id == id && x.EnabledMark == 1)).Adapt<DocumentInfoOutput>();
        if (data.type == 1)
        {
            data.fullName = data.fullName.Replace("." + data.fileExtension, string.Empty);
        }
        return data;
    }
    #endregion

    #region Post
    
    /// <summary>
    /// 新建.
    /// </summary>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("/document/add")]
    public async Task Create([FromBody] DocumentCrInput input)
    {
        if (await _repository.IsAnyAsync(x => x.FullName == input.fullName && x.CreatedUserId == _userManager.UserId && x.ParentId == input.parentId && x.Type == 0 && x.EnabledMark == 1))
            throw Oops.Oh(ErrorCode.Ex0008);
        var entity = input.Adapt<DocumentEntity>();
        entity.EnabledMark = 1;
        var isOk = await _repository.AsSugarClient().Insertable(entity).IgnoreColumns(ignoreNullColumn: true).ExecuteCommandAsync();
        if (isOk < 1)
            throw Oops.Oh(ErrorCode.COM1000);
    }

    /// <summary>
    /// 更新.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPut("/document/{id}/update")]
    public async Task Update(long id, [FromBody] DocumentUpInput input)
    {
        if (await _repository.IsAnyAsync(x => x.Id != id && x.Type == input.type && x.FullName == input.fullName && x.CreatedUserId == _userManager.UserId && x.ParentId == input.parentId && x.EnabledMark == 1))
            throw Oops.Oh(ErrorCode.Ex0008);
        var entity = await _repository.GetFirstAsync(x => x.Id == id);
        entity.FullName = entity.Type == 1 ? string.Format("{0}.{1}", input.fullName, entity.FileExtension) : input.fullName;
        var isOk = await _repository.AsSugarClient().Updateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandHasChangeAsync();
        if (!isOk)
            throw Oops.Oh(ErrorCode.COM1001);
    }

    /// <summary>
    /// 删除.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpDelete("/document/{id}/delete")]
    public async Task Delete(long id)
    {
        if (await _repository.IsAnyAsync(x => x.ParentId == id ))
            throw Oops.Oh(ErrorCode.Ex0006);
        var entity = await _repository.GetFirstAsync(x => x.Id == id );
        if (entity == null)
            throw Oops.Oh(ErrorCode.COM1005);
        entity.EnabledMark = 0;
        var isOk = await _repository.AsSugarClient().Updateable(entity).UpdateColumns(it => new { it.EnabledMark }).ExecuteCommandHasChangeAsync();
        if (!isOk)
            throw Oops.Oh(ErrorCode.COM1002);
    }

    /// <summary>
    /// 批量删除.
    /// </summary>
    /// <param name="input">主键值.</param>
    /// <returns></returns>
    [HttpPost("/document/BatchDelete")]
    public async Task BatchDelete([FromBody] DocumentBatchInput input)
    {
        foreach (var id in input.ids)
        {
            var entity = await _repository.GetFirstAsync(x => x.Id == id && x.EnabledMark == 1);

            var list = new List<DocumentEntity>();
            if (entity.Type == 0) await GetChildSrcList(entity.Id, list, 0, 1);
            list.Add(entity);

            await _repository.AsUpdateable()
                .SetColumns(it => new DocumentEntity
                {
                    EnabledMark = 0,
                    UpdatedTime = SqlFunc.GetDate(),
                    UpdatedUserId = _userManager.UserId
                })
                .Where(it => list.Select(x => x.Id).Contains(it.Id)).ExecuteCommandHasChangeAsync();
            
            var childId = string.Join(",", list.Select(it => it.Id));
            var log = new DocumentLogEntity()
            {
                Id = Yitter.IdGenerator.YitIdHelper.NextId(),
                DocumentId = id,
                ChildDocument = childId,
                CreatedTime = DateTime.Now,
                CreatedUserId = _userManager.UserId
            };
            await _repository.AsSugarClient().Insertable(log).IgnoreColumns(ignoreNullColumn: true).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 上传文件.
    /// </summary>
    /// <param name="parentId"></param>
    /// <param name="fileName"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/document/Uploader")]
    public async Task Uploader(long parentId, string fileName, [FromForm] DocumentUploaderInput input)
    {
        var fileExtension = Path.GetExtension(input.file.FileName).Replace(".", string.Empty);
        var allowExtension = KeyVariable.AllowUploadFileType;
        if (!allowExtension.Contains(fileExtension)) throw Oops.Oh(ErrorCode.D1800);

        #region 上传图片
        var upFileName = fileName.IsNotEmptyOrNull() ? fileName : input.file.FileName;
        if (await _repository.IsAnyAsync(x => x.CreatedUserId == _userManager.UserId && x.FullName == upFileName && x.Type == 1 && x.EnabledMark == 1))
        {
            var random = new Random().NextLetterAndNumberString(5);
            var newFileName = Path.GetFileNameWithoutExtension(fileName);
            upFileName = string.Format("{0}副本{1}.{2}", newFileName, random, fileExtension);
        }
        var stream = input.file.OpenReadStream();
        var _filePath = _fileManager.GetPathByType("document");
        await _fileManager.UploadFileByType(stream, _filePath, input.file.FileName);
        Thread.Sleep(1000);
        #endregion

        #region 保存数据
        var entity = new DocumentEntity();
        entity.Type = 1;
        entity.FullName = upFileName;
        entity.ParentId = parentId;
        entity.FileExtension = fileExtension;
        entity.FilePath = upFileName;
        entity.FileSize = input.file.Length.ToString();
        entity.UploadUrl = string.Format("/api/file/Image/document/{0}", entity.FilePath);
        var isOk = await _repository.AsSugarClient().Insertable(entity).ExecuteCommandAsync();
        if (isOk < 1)
            throw Oops.Oh(ErrorCode.D8001);
        #endregion
    }

    /// <summary>
    /// 分片组装.
    /// </summary>
    /// <param name="input">请求参数.</param>
    [HttpPost("/document/merge")]
    public async Task<dynamic> Merge([FromForm] ChunkModel input)
    {
        var fileName = input.fileName;
        if (await _repository.IsAnyAsync(x => x.CreatedUserId == _userManager.UserId && x.FullName == fileName && x.Type == 1 && x.EnabledMark == 1))
        {
            var random = new Random().NextLetterAndNumberString(5);
            var newFileName = Path.GetFileNameWithoutExtension(fileName);
            fileName = string.Format("{0}副本{1}.{2}", newFileName, random, input.extension);
        }
        input.isUpdateName = false;
        input.type = "document";
        var _filePath = _fileManager.GetPathByType(input.type);
        var output = await _fileManager.Merge(input);
        #region 保存数据
        var entity = new DocumentEntity();
        entity.Type = 1;
        entity.FullName = fileName;
        entity.ParentId = input.parentId;
        entity.FileExtension = input.extension;
        entity.FilePath = output.name;
        entity.FileSize = input.fileSize;
        entity.UploadUrl = string.Format("/api/file/Image/document/{0}", entity.FilePath);
        var isOk = await _repository.AsSugarClient().Insertable(entity).ExecuteCommandAsync();
        if (isOk < 1)
            throw Oops.Oh(ErrorCode.D8001);
        #endregion
        return output;
    }

    /// <summary>
    /// 下载文件.
    /// </summary>
    /// <param name="id">主键值.</param>
    [HttpPost("/document/Download/{id}")]
    public async Task<dynamic> Download(long id)
    {
        var entity = await _repository.GetFirstAsync(x => x.Id == id && x.EnabledMark == 1);
        if (entity == null)
            throw Oops.Oh(ErrorCode.D8000);
        var fileName = _userManager.UserId + "|" + entity.FilePath + "|document";
        _cacheManager.Set(entity.FilePath, string.Empty);
        return new {
            name = entity.FullName,
            url = "/api/File/Download?encryption=" + DESEncryption.Encrypt(fileName, "JNPF")
        };
    }

    /// <summary>
    /// 批量下载文件.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/document/PackDownload")]
    public async Task<dynamic> PackDownload([FromBody] DocumentBatchInput input)
    {
        // 单个文件直接下载
        if (input.ids.Count == 1)
        {
            var entity = await _repository.GetFirstAsync(it => it.EnabledMark == 1 && it.Id == input.ids[0]);
            if (entity.Type == 1)
            {
                var fileName = _userManager.UserId + "|" + entity.FilePath + "|document";
                _cacheManager.Set(entity.FilePath, string.Empty);
                return new {
                    name = entity.FullName,
                    url = "/api/File/Download?encryption=" + DESEncryption.Encrypt(fileName, "JNPF")
                };
            }
        }

        var directoryName = DateTime.Now.ToString("yyyyMMddHHmmss");
        var path = Path.Combine(_fileManager.GetPathByType("document"), directoryName);
        await FileCompress(input.ids, path);
        string downloadPath = path + ".zip";

        // 判断是否存在同名称文件
        if (File.Exists(downloadPath))
            File.Delete(downloadPath);
        ZipFile.CreateFromDirectory(path, downloadPath);
        var downloadFileName = string.Format("{0}|{1}.zip|document", _userManager.UserId, directoryName);
        _cacheManager.Set(string.Format("{0}.zip", directoryName), string.Empty);
        return new { name = string.Format("{0}.zip", directoryName), url = "/api/File/Download?encryption=" + DESEncryption.Encrypt(downloadFileName, "JNPF") };
    }

    /// <summary>
    /// 回收站（彻底删除）.
    /// </summary>
    /// <param name="input">主键值.</param>
    /// <returns></returns>
    [HttpPost("/document/Trash")]
    [UnitOfWork]
    public async Task TrashDelete([FromBody] DocumentBatchInput input)
    {
        foreach (var id in input.ids)
        {
            var list = new List<DocumentEntity>();
            await GetChildSrcList(id, list, 0, 0);
            foreach (var item in list)
            {
                if (item.Type == 1)
                {
                    await _fileManager.DeleteFile(item.FilePath);
                }
                await _repository.AsSugarClient().Deleteable(item).ExecuteCommandAsync();
            }

            var isOk = await _repository.AsDeleteable()
                .Where(it => it.Id == id && it.CreatedUserId == _userManager.UserId).ExecuteCommandHasChangeAsync();
            if (!isOk)
                throw Oops.Oh(ErrorCode.COM1002);
        }
    }

    /// <summary>
    /// 回收站（还原文件）.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/document/Trash/Actions/Recovery")]
    public async Task TrashRecovery([FromBody] DocumentBatchInput input)
    {
        foreach (var id in input.ids)
        {
            var entity = await _repository.GetFirstAsync(it => it.Id == id);
            if (entity.ParentId != 0 && !await _repository.IsAnyAsync(it => it.Id == entity.ParentId && it.EnabledMark == 1))
            {
                await _repository.AsUpdateable().SetColumns(it => it.ParentId == 0).Where(it => it.Id == id).ExecuteCommandAsync();
            }
            var childId = await _repository.AsSugarClient().Queryable<DocumentLogEntity>().Where(it =>  it.DocumentId == id).Select(it => it.ChildDocument).FirstAsync();
            await _repository.AsUpdateable().SetColumns(it => it.EnabledMark == 1).Where(it => childId.Contains(it.Id.ToString())).ExecuteCommandAsync();

            await _repository.AsSugarClient().Deleteable<DocumentLogEntity>()
                .Where(it => it.DocumentId == id).ExecuteCommandAsync();
        }
    }

    /// <summary>
    /// 共享文件（创建）.
    /// </summary>
    /// <param name="input">共享人.</param>
    /// <returns></returns>
    [HttpPost("/document/Actions/Share")]
    [UnitOfWork]
    public async Task ShareCreate([FromBody] DocumentActionsShareInput input)
    {
        foreach (var id in input.ids)
        {
            List<DocumentShareEntity> documentShareEntityList = new List<DocumentShareEntity>();
            foreach (var item in input.userIds)
            {
                if (!await _repository.AsSugarClient().Queryable<DocumentShareEntity>().AnyAsync(it => it.DocumentId == id && it.ShareUserId == item))
                {
                    documentShareEntityList.Add(new DocumentShareEntity
                    {
                        Id = Yitter.IdGenerator.YitIdHelper.NextId(),
                        DocumentId = id,
                        ShareUserId = item,
                        ShareTime = DateTime.Now,
                    });
                }
            }

            var entity = await _repository.GetFirstAsync(x => x.Id == id && x.EnabledMark == 1);
            entity.IsShare = entity.IsShare.IsNotEmptyOrNull() ? entity.IsShare + documentShareEntityList.Count : documentShareEntityList.Count;
            entity.ShareTime = DateTime.Now;
            await _repository.AsSugarClient().Insertable(documentShareEntityList).ExecuteCommandAsync();
            var isOk = await _repository.AsSugarClient().Updateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandHasChangeAsync();
            if (!isOk)
                throw Oops.Oh(ErrorCode.COM1001);
        }
    }

    /// <summary>
    /// 共享文件（取消）.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/document/Actions/CancelShare")]
    [UnitOfWork]
    public async Task CancelShare([FromBody] DocumentBatchInput input)
    {
        foreach (var id in input.ids)
        {
            var entity = await _repository.GetFirstAsync(x => x.Id == id && x.EnabledMark == 1);
            entity.IsShare = null;
            entity.ShareTime = null;
            await _repository.AsSugarClient().Deleteable<DocumentShareEntity>().Where(x => x.DocumentId == id).ExecuteCommandAsync();
            var isOk = await _repository.AsSugarClient().Updateable(entity).ExecuteCommandHasChangeAsync();
            if (!isOk)
                throw Oops.Oh(ErrorCode.COM1001);
        }
    }

    /// <summary>
    /// 共享用户调整.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/document/Actions/ShareAdjustment/{id}")]
    [UnitOfWork]
    public async Task ShareAdjustment(long id, [FromBody] DocumentShareAdjustmentInput input)
    {
        var list = new List<DocumentShareEntity>();
        foreach (var userId in input.userIds)
        {
            var shareEntity = new DocumentShareEntity();
            shareEntity.Id = Yitter.IdGenerator.YitIdHelper.NextId();
            shareEntity.DocumentId = id;
            shareEntity.ShareUserId = userId;
            shareEntity.ShareTime = DateTime.Now;
            shareEntity.CreatedUserId = _userManager.UserId;
            shareEntity.CreatedTime = DateTime.Now;
            list.Add(shareEntity);
        }

        await _repository.AsSugarClient().Deleteable<DocumentShareEntity>().Where(it => it.DocumentId == id).ExecuteCommandAsync();
        await _repository.AsSugarClient().Insertable(list).ExecuteCommandAsync();
        await _repository.AsUpdateable().SetColumns(it => it.IsShare == list.Count).SetColumns(it => it.ShareTime == SqlFunc.GetDate()).Where(it => it.Id == id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 列表（文件夹树）.
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/document/FolderTree")]
    public async Task<dynamic> GetFolderTree([FromBody] DocumentBatchInput input)
    {
        var data = new List<DocumentFolderTreeOutput>()
        {
            new() { id = "-1", fullName = "全部文档", parentId = "0", icon = "0"}
        };
        var list = (await _repository.AsQueryable()
            .Where(x => x.CreatedUserId == _userManager.UserId && x.Type == 0 && x.EnabledMark == 1)
            .OrderBy(x => x.CreatedTime, OrderByType.Desc)
            .ToListAsync()).Adapt<List<DocumentFolderTreeOutput>>();
        data.AddRange(list);

        if (input.ids != null)
        {
            data.RemoveAll(x => input.ids.Contains(Convert.ToInt64(x.id)));
        }

        var treeList = data.ToTree();
        return new { list = treeList };
    }

    /// <summary>
    /// 文件/夹移动到.
    /// </summary>
    /// <param name="toId">将要移动到Id.</param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPut("/document/Actions/MoveTo/{toId}")]
    public async Task MoveTo(long toId, [FromBody] DocumentBatchInput input)
    {
        foreach (var id in input.ids)
        {
            var entity = await _repository.GetFirstAsync(x => x.Id == id);
            var entityTo = await _repository.GetFirstAsync(x => x.Id == toId);
            if (id == toId && entity.Type == 0 && entityTo.Type == 0)
                throw Oops.Oh(ErrorCode.Ex0002);
            if (entityTo.IsNotEmptyOrNull() && id == entityTo.ParentId && entity.Type == 0 && entityTo.Type == 0)
                throw Oops.Oh(ErrorCode.Ex0005);
            entity.ParentId = toId;
            var isOk = await _repository.AsSugarClient().Updateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandHasChangeAsync();
            if (!isOk)
                throw Oops.Oh(ErrorCode.COM1001);
        }
    }
    
    //
    // /// <summary>
    // /// 流程归档.
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [HttpPost("UploadBlob")]
    // public async Task UploadBlob([FromForm] DocumentUploaderInput input)
    // {
    //     try
    //     {
    //         var taskEntity = await _repository.AsSugarClient().Queryable<WorkFlowTaskEntity>().FirstAsync(x => x.Id == input.taskId);
    //         if (taskEntity.IsNullOrEmpty()) throw Oops.Oh(ErrorCode.COM1005);
    //         var globalNode = await _repository.AsSugarClient().Queryable<WorkFlowNodeEntity>().FirstAsync(x => x.FlowId == taskEntity.FlowId && x.NodeType == "global");
    //         if (globalNode.IsNullOrEmpty()) throw Oops.Oh(ErrorCode.COM1005);
    //
    //         #region 参数处理
    //         var globalPro = globalNode.NodeJson.ToObject<GlobalProperties>();
    //         var fileName = string.Format("{0}-{1}.pdf", taskEntity.FullName, taskEntity.EndTime.ParseToDateTime().ToString("yyyyMMddHHmmss"));
    //         var entity = new DocumentEntity();
    //         entity.Id = Yitter.IdGenerator.YitIdHelper.NextId();
    //         entity.Type = 1;
    //         entity.FullName = fileName;
    //         entity.FileExtension = "pdf";
    //         entity.FilePath = fileName;
    //         entity.FileSize = input.file.Length.ToString();
    //         entity.UploadUrl = string.Format("/api/file/Image/document/{0}", entity.FilePath);
    //         entity.Description = taskEntity.Id;
    //         entity.CreatedUserId = taskEntity.CreatedUserId;
    //         var shareInput = new DocumentActionsShareInput();
    //         shareInput.userIds = new List<string>();
    //         if (globalPro.fileConfig.permissionType == 1)
    //         {
    //             shareInput.ids = new List<string> { entity.Id };
    //             var handleUserIds = _repository.AsSugarClient().Queryable<WorkFlowOperatorEntity>().Where(x => x.TaskId == taskEntity.Id && x.HandleStatus != null && x.Status != 2 && x.Status != 3 && x.Status != -1 && x.HandleId != "jnpf").Select(x => x.HandleId).ToList();
    //             shareInput.userIds = shareInput.userIds.Union(handleUserIds).ToList();
    //             var circulateUserIds = _repository.AsSugarClient().Queryable<WorkFlowCirculateEntity>().Where(x => x.TaskId == taskEntity.Id).Select(x => x.UserId).ToList();
    //             shareInput.userIds = shareInput.userIds.Union(circulateUserIds).Distinct().ToList();
    //         }
    //         if (globalPro.fileConfig.permissionType == 3)
    //         {
    //             shareInput.ids = new List<string> { entity.Id };
    //             var OperatorList = _repository.AsSugarClient().Queryable<WorkFlowOperatorEntity>().Where(x => x.TaskId == taskEntity.Id && x.HandleStatus != null && x.Status != 2 && x.Status != 3 && x.Status != -1 && x.HandleId != "jnpf").OrderByDescending(x => x.HandleTime).ToList();
    //             if (OperatorList.Any())
    //             {
    //                 var operatorEntity = OperatorList.First();
    //                 var lastOperatorList = OperatorList.FindAll(x => x.NodeId == operatorEntity.NodeId).ToList();
    //                 entity.CreatedUserId = lastOperatorList.FirstOrDefault().HandleId;
    //                 var handIds = lastOperatorList.Select(x => x.HandleId).ToList();
    //                 shareInput.userIds = shareInput.userIds.Union(handIds).ToList();
    //                 var circulateUserIds = _repository.AsSugarClient().Queryable<WorkFlowCirculateEntity>().Where(x => x.TaskId == taskEntity.Id && x.NodeCode == operatorEntity.NodeCode).Select(x => x.UserId).ToList();
    //                 shareInput.userIds = shareInput.userIds.Union(circulateUserIds).Distinct().ToList();
    //             }
    //         } 
    //         #endregion
    //
    //         #region 归档路径判断
    //         var documentEntity = await _repository.GetFirstAsync(x => x.FullName == "流程归档" && x.CreatedUserId == entity.CreatedUserId && x.ParentId == "0" && x.Type == 0 && x.EnabledMark == 1);
    //         if (documentEntity.IsNullOrEmpty())
    //         {
    //             documentEntity = new DocumentEntity();
    //             documentEntity.Id = Yitter.IdGenerator.YitIdHelper.NextId();
    //             documentEntity.ParentId = "0";
    //             documentEntity.FullName = "流程归档";
    //             documentEntity.Type = 0;
    //             documentEntity.EnabledMark = 1;
    //             documentEntity.CreatedUserId = entity.CreatedUserId;
    //             await _repository.AsSugarClient().Insertable(documentEntity).IgnoreColumns(ignoreNullColumn: true).CallEntityMethod(m => m.Create()).ExecuteCommandAsync();
    //         }
    //         entity.ParentId = documentEntity.Id;
    //         #endregion
    //
    //         #region 文件上传
    //         var stream = input.file.OpenReadStream();
    //         var _filePath = _fileManager.GetPathByType("document");
    //         await _fileManager.UploadFileByType(stream, _filePath, fileName);
    //         Thread.Sleep(1000); 
    //         #endregion
    //
    //         #region 保存数据
    //         var isOk = await _repository.AsSugarClient().Insertable(entity).CallEntityMethod(m => m.Create()).ExecuteCommandAsync();
    //         if (isOk < 1)
    //             throw Oops.Oh(ErrorCode.D8001);
    //
    //         shareInput.userIds.Remove(entity.CreatedUserId);
    //         if (shareInput.userIds.Any()) //共享文件
    //         {
    //             await ShareCreate(shareInput);
    //         }
    //         taskEntity.IsFile = 1;
    //         await _repository.AsSugarClient().Updateable(taskEntity).ExecuteCommandAsync();
    //         #endregion
    //
    //     }
    //     catch (Exception ex)
    //     {
    //         throw Oops.Oh(ErrorCode.D8001);
    //     }
    // }
    #endregion

    #region Private

    /// <summary>
    /// 获取文件夹下的所有子文件.
    /// </summary>
    /// <param name="id">文件夹id.</param>
    /// <param name="list">数据.</param>
    /// <param name="type">0 自己的文件，1 所有文件.</param>
    /// <param name="enabledMark">启用标识.</param>
    private async Task GetChildSrcList(long id, List<DocumentEntity> list, int type, int enabledMark)
    {
        var entityList = await _repository.AsQueryable()
            .Where(it => it.EnabledMark == enabledMark && it.ParentId == id)
            .WhereIF(type == 0, it => it.CreatedUserId == _userManager.UserId)
            .OrderBy(it => it.CreatedTime, OrderByType.Desc)
            .ToListAsync();
        foreach (var item in entityList)
        {
            if (!list.Any(it => it.Id == item.Id)) list.Add(item);
            if (item.Type == 0) await GetChildSrcList(item.Id, list, type, enabledMark);
        }
    }

    /// <summary>
    /// 文件打包.
    /// </summary>
    /// <param name="ids"></param>
    /// <param name="directoryName"></param>
    /// <returns></returns>
    private async Task FileCompress(List<long> ids, string directoryName)
    {
        foreach (var id in ids)
        {
            var entity = await _repository.GetFirstAsync(it => it.EnabledMark == 1 && it.Id == id);
            if (entity.IsNotEmptyOrNull())
            {
                if (entity.Type == 0)
                {
                    var nextDirectoryName = Path.Combine(directoryName, entity.FullName);
                    if (!Directory.Exists(nextDirectoryName))
                        Directory.CreateDirectory(nextDirectoryName);
                    var idList = (await _repository.GetListAsync(x => x.ParentId == entity.Id && x.EnabledMark == 1 )).Select(x => x.Id).ToList();
                    await FileCompress(idList, nextDirectoryName);
                }
                else
                {
                    if (!Directory.Exists(directoryName))
                        Directory.CreateDirectory(directoryName);
                    await _fileManager.CopyFile(Path.Combine(_fileManager.GetPathByType("document"), entity.FilePath), Path.Combine(directoryName, entity.FilePath));
                }
            }
        }
    }
    #endregion
}