global using SqlSugar;
global using Furion.DependencyInjection;
global using System.Web;
global using Common.Configuration;
global using Common.Models;
global using Common.Options;
global using Furion;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using IotPlatform.Core.Enum;
global using IotPlatform.Core.Extension;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using System.Text;
global using Common.Core.Manager.Files;
global using IotPlatform.Extend.Entity.DocumentPreview;
global using Mapster;
global using Microsoft.AspNetCore.Http;