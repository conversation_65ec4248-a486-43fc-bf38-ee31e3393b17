using System.Data;
using Common.Dto.DataBase;
using Common.Models.VisualDev;
using Extras.DatabaseAccessor.SqlSugar.Internal;
using Extras.DatabaseAccessor.SqlSugar.Models;
using SqlSugar;
using Systems.Entity;
using Systems.Entity.Dto;
using Systems.Entity.Dto.DataBase;
using Systems.Entity.Model.System.DataBase;
using VisualDev.Entity.Dto.VisualDevModelData;
using DbType = SqlSugar.DbType;

namespace Common.Core.Manager.DataBase;

/// <summary>
/// 数据库管理器
/// </summary>
public interface IDataBaseManager
{
    /// <summary>
    ///     数据库切换.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <returns>切库后的SqlSugarClient.</returns>
    SqlSugarScope ChangeDataBase(DbLink link);

    /// <summary>
    ///     获取租户SqlSugarClient客户端.
    /// </summary>
    /// <param name="tenantId">租户id.</param>
    /// <returns></returns>
    ISqlSugarClient GetTenantSqlSugarClient(string tenantId, GlobalTenantCacheModel globalTenantCache = null);

    /// <summary>
    /// 生成连接字符串
    /// </summary>
    /// <param name="dbLink"></param>
    /// <param name="decrypt"></param>
    /// <returns></returns>
    string ToConnectionString(DbLink dbLink, bool decrypt = true);

    /// <summary>
    /// 根据链接获取分页数据
    /// </summary>
    /// <param name="link"></param>
    /// <param name="strSql"></param>
    /// <param name="pageInput"></param>
    /// <param name="columnDesign"></param>
    /// <param name="dataPermissions"></param>
    /// <param name="outColumnName"></param>
    /// <returns></returns>
    SqlSugarPagedList<Dictionary<string, object>> GetInterFaceData(DbLink link, string strSql, VisualDevModelListQueryInput pageInput, MainBeltViceQueryModel columnDesign,
        List<IConditionalModel> dataPermissions, Dictionary<string, string> outColumnName = null);

    /// <summary>
    /// 根据链接获取数据
    /// </summary>
    /// <param name="link">数据连接</param>
    /// <param name="strSql">sqlyuju </param>
    /// <param name="parameters">参数</param>
    /// <returns></returns>
    DataTable GetInterFaceData(DbLink link, string strSql, params SugarParameter[] parameters);

    #region Sql

    /// <summary>
    /// 执行增删改sql
    /// </summary>
    /// <param name="link">数据连接</param>
    /// <param name="strSql">sqlyuju </param>
    /// <param name="parameters">参数</param>
    /// <returns></returns>
    void ExecuteCommand(DbLink link, string strSql, params SugarParameter[] parameters);

    /// <summary>
    /// 查询返回单条数据
    /// </summary>
    /// <param name="link"></param>
    /// <param name="sql"></param>
    /// <returns></returns>
    Task<IDictionary<string, object?>> QuerySingle(DbLink link, string sql);

    /// <summary>
    ///     执行增删改sql.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="strSql">sql语句.</param>
    /// <param name="isCopyNew">是否CopyNew.</param>
    /// <param name="parameters">参数.</param>
    /// <returns></returns>
    Task<int> ExecuteSql(DbLink link, string strSql, bool isCopyNew = false, params SugarParameter[] parameters);

    /// <summary>
    ///     执行Sql(新增、修改).
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <param name="dicList">数据.</param>
    /// <param name="primaryField">主键字段.</param>
    /// <returns></returns>
    Task<int> ExecuteSql(DbLink link, string table, List<Dictionary<string, object>> dicList, string primaryField = "");

    /// <summary>
    ///     执行Sql 新增 并返回自增长Id.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <param name="dicList">数据.</param>
    /// <returns>id.</returns>
    int ExecuteReturnIdentity(DbLink link, string table, List<Dictionary<string, object>> dicList);

    /// <summary>
    ///     查询sql.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="strSql">sql语句.</param>
    /// <param name="isCopyNew">是否CopyNew.</param>
    /// <param name="parameters">参数.</param>
    /// <returns></returns>
    DataTable GetSqlData(DbLink link, string strSql, bool isCopyNew = false, params SugarParameter[] parameters);

    /// <summary>
    ///     条件动态过滤.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="strSql">sql语句.</param>
    /// <returns>条件是否成立.</returns>
    bool WhereDynamicFilter(DbLink link, string strSql);

    /// <summary>
    ///     执行统计sql.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="strSql">sql语句.</param>
    /// <param name="isCopyNew">是否CopyNew.</param>
    /// <param name="parameters">参数.</param>
    int GetCount(DbLink link, string strSql, bool isCopyNew = false, params SugarParameter[] parameters);

    /// <summary>
    ///     使用存储过程.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="stored">存储过程名称.</param>
    /// <param name="parameters">参数.</param>
    void UseStoredProcedure(DbLink link, string stored, List<SugarParameter> parameters);

    /// <summary>
    ///     获取数据表分页(SQL语句).
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="dbSql">数据SQL.</param>
    /// <param name="pageIndex">页数.</param>
    /// <param name="pageSize">条数.</param>
    /// <param name="parameters">SQL参数.</param>
    /// <returns></returns>
    Task<dynamic> GetDataTablePage(DbLink link, string dbSql, int pageIndex, int pageSize, params SugarParameter[] parameters);

    /// <summary>
    ///     获取数据表分页(实体).
    /// </summary>
    /// <typeparam name="TEntity">T.</typeparam>
    /// <param name="link">数据连接.</param>
    /// <param name="pageIndex">页数.</param>
    /// <param name="pageSize">条数.</param>
    /// <returns></returns>
    Task<List<TEntity>> GetDataTablePage<TEntity>(DbLink link, int pageIndex, int pageSize);

    #endregion

    #region 数据库

    /// <summary>
    ///     删除表.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <returns></returns>
    bool Delete(DbLink link, string table);

    /// <summary>
    /// 新建表
    /// </summary>
    /// <param name="link"></param>
    /// <param name="tableModel"></param>
    /// <param name="tableFieldList"></param>
    /// <returns></returns>
    bool Create(DbLink link, DbTableModel tableModel, List<DbTableFieldModel> tableFieldList);

    /// <summary>
    /// 修改表
    /// </summary>
    /// <param name="link"></param>
    /// <param name="oldTable"></param>
    /// <param name="tableModel"></param>
    /// <param name="tableFieldList"></param>
    /// <returns></returns>
    bool Update(DbLink link, string oldTable, DbTableModel tableModel, List<DbTableFieldModel> tableFieldList);

    /// <summary>
    ///     表是否存在.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <returns></returns>
    bool IsAnyTable(DbLink link, string table);

    /// <summary>
    ///     表是否存在数据.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <returns></returns>
    bool IsAnyData(DbLink link, string table);

    /// <summary>
    ///     表字段是否存在.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <param name="column">表字段名.</param>
    /// <returns></returns>
    bool IsAnyColumn(DbLink link, string table, string column);

    /// <summary>
    ///     获取表字段列表.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableName">表名.</param>
    /// <returns>TableFieldListModel.</returns>
    List<Systems.Entity.Dto.DbTableFieldModel> GetFieldList(DbLink? link, string? tableName);

    /// <summary>
    ///     获取表数据.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    DataTable GetData(DbLink link, string tableName);

    /// <summary>
    ///     获取表信息.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    DatabaseTableInfoOutput GetDataBaseTableInfo(DbLink link, string tableName);

    /// <summary>
    ///     获取数据库表信息.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <returns></returns>
    List<DatabaseTableListOutput> GetDBTableList(DbLink link);

    /// <summary>
    ///     获取数据库表信息.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="isView">视图.</param>
    /// <returns></returns>
    List<DbTableInfo> GetTableInfos(DbLink link, bool isView = false);

    /// <summary>
    ///     同步数据.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="dt">数据.</param>
    /// <param name="table">表名.</param>
    /// <returns></returns>
    Task<bool> SyncData(DbLink link, DataTable dt, string table);

    /// <summary>
    ///     同步表操作.
    /// </summary>
    /// <param name="linkFrom">原数据库.</param>
    /// <param name="linkTo">目前数据库.</param>
    /// <param name="table">表名称.</param>
    /// <param name="type">操作类型.</param>
    /// <param name="fieldType">数据类型.</param>
    public void SyncTable(DbLink linkFrom, DbLink linkTo, string table, int type, Dictionary<string, string> fieldType);

    /// <summary>
    ///     测试数据库连接.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <returns></returns>
    bool IsConnection(DbLink link);

    /// <summary>
    ///     视图数据类型转换.
    /// </summary>
    /// <param name="fields">字段数据.</param>
    /// <param name="databaseType">数据库类型.</param>
    List<TableFieldOutput> ViewDataTypeConversion(List<TableFieldOutput> fields, DbType databaseType);

    /// <summary>
    ///     转换数据库类型.
    /// </summary>
    /// <param name="dbType">数据库类型.</param>
    /// <returns></returns>
    DbType ToDbType(DbTypeEnum dbType);

    /// <summary>
    /// sqlsugar添加表字段
    /// </summary>
    /// <param name="link"></param>
    /// <param name="tableInfo"></param>
    /// <param name="tableFieldList"></param>
    void AddTableColumn(DbLink link, TableInfoOutput tableInfo, List<DbTableFieldModel> tableFieldList);

    /// <summary>
    /// sqlsugar添加表字段
    /// </summary>
    /// <param name="link"></param>
    /// <param name="tableName"></param>
    /// <param name="tableFieldList"></param>
    void AddTableColumn(DbLink link, string tableName, List<DbTableFieldModel> tableFieldList);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="tenantId"></param>
    /// <param name="tenantName"></param>
    /// <returns></returns>
    DbLink GetTenantDbLink(long tenantId = 0, string tenantName = "");

    #endregion
}