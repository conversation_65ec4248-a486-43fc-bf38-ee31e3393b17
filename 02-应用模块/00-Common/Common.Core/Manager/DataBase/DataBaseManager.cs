using System.Data;
using System.Dynamic;
using System.Text;
using Common.Dto.DataBase;
using Common.Models.VisualDev;
using Extras.DatabaseAccessor.SqlSugar.Internal;
using Extras.DatabaseAccessor.SqlSugar.Models;
using Extras.DatabaseAccessor.SqlSugar.Options;
using IotPlatform.Core;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Microsoft.Extensions.Options;
using SqlSugar;
using Systems.Entity;
using Systems.Entity.Dto.DataBase;
using Systems.Entity.Model.System.DataBase;
using VisualDev.Entity.Dto.VisualDevModelData;
using DateTime = System.DateTime;
using DbTableFieldModel = Systems.Entity.Dto.DbTableFieldModel;
using DbType = SqlSugar.DbType;

namespace Common.Core.Manager.DataBase;

/// <summary>
///     实现切换数据库后操作.
/// </summary>
public class DataBaseManager : IDataBaseManager, ITransient, IDisposable
{
    /// <summary>
    ///     初始化客户端.
    /// </summary>
    private static SqlSugarScope? _sqlSugarClient;

    /// <summary>
    ///     缓存管理.
    /// </summary>
    private readonly SysCacheService _cacheManager;

    /// <summary>
    ///     默认数据库配置.
    /// </summary>
    private readonly DbConnectionConfig defaultConnectionConfig;

    /// <summary>
    ///     是否打印sql
    /// </summary>
    private readonly bool enableConsoleSql;

    private readonly ILogger<DataBaseManager> _logger;

    /// <summary>
    ///     构造函数.
    /// </summary>
    public DataBaseManager(
        IOptions<ConnectionStringsOptions> connectionOptions,
        ISqlSugarClient context,
        SysCacheService cacheManager, ILogger<DataBaseManager> logger)
    {
        _sqlSugarClient = (SqlSugarScope)context;
        _sqlSugarClient.Aop.OnLogExecuting = (sql, pars) =>
        {
            if (sql.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
            {
                Console.ForegroundColor = ConsoleColor.Green;
            }

            if (sql.StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase) || sql.StartsWith("INSERT", StringComparison.OrdinalIgnoreCase))
            {
                Console.ForegroundColor = ConsoleColor.White;
            }

            if (sql.StartsWith("DELETE", StringComparison.OrdinalIgnoreCase))
            {
                Console.ForegroundColor = ConsoleColor.Blue;
            }

            // 在控制台输出sql语句
            Console.WriteLine("【" + DateTime.Now + "——执行SQL】\r\n" + UtilMethods.GetSqlString(_sqlSugarClient.CurrentConnectionConfig.DbType, sql, pars) + "\r\n");
            // App.PrintToMiniProfiler("SqlSugar", "Info", sql + "\r\n" + base.Context.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
        };

        _sqlSugarClient.Aop.OnError = ex =>
        {
            Console.ForegroundColor = ConsoleColor.Red;
            string? pars = _sqlSugarClient.Utilities.SerializeObject(((SugarParameter[])ex.Parametres).ToDictionary(it => it.ParameterName, it => it.Value));
            Console.WriteLine("【" + DateTime.Now + "——错误SQL】\r\n" + UtilMethods.GetSqlString(_sqlSugarClient.CurrentConnectionConfig.DbType, ex.Sql, (SugarParameter[])ex.Parametres) + "\r\n");
            // App.PrintToMiniProfiler("SqlSugar", "Error", $"{ex.Message}{Environment.NewLine}{ex.Sql}{pars}{Environment.NewLine}");
        };
        enableConsoleSql = connectionOptions.Value.EnableConsoleSql;
        _cacheManager = cacheManager;
        _logger = logger;
        defaultConnectionConfig = connectionOptions.Value.DefaultConnectionConfig;
    }

    #region 公共

    /// <summary>
    ///     根据链接获取分页数据.
    /// </summary>
    /// <returns></returns>
    public SqlSugarPagedList<Dictionary<string, object>> GetInterFaceData(DbLink link, string strSql,
        VisualDevModelListQueryInput pageInput, MainBeltViceQueryModel columnDesign,
        List<IConditionalModel> dataPermissions, Dictionary<string, string> outColumnName = null)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            int total = 0;

            if (_sqlSugarClient.CurrentConnectionConfig.DbType == DbType.Oracle)
            {
                strSql = strSql.Replace(";", string.Empty);
            }

            List<OrderByModel> sidx = new();
            if (pageInput.sidx.IsNotEmptyOrNull())
            {
                foreach (string item in pageInput.sidx.Split(",").ToList())
                {
                    if (item[0].ToString().Equals("-"))
                    {
                        string itemName = item.Remove(0, 1);
                        sidx.Add(new OrderByModel
                        {
                            FieldName = itemName,
                            OrderByType = OrderByType.Desc
                        });
                    }
                    else
                    {
                        sidx.Add(new OrderByModel
                        {
                            FieldName = item,
                            OrderByType = OrderByType.Asc
                        });
                    }
                }
            }

            List<IConditionalModel>? dataRuleJson = new();
            if (pageInput.dataRuleJson.IsNotEmptyOrNull())
            {
                dataRuleJson = _sqlSugarClient.Utilities.JsonToConditionalModels(pageInput.dataRuleJson);
            }

            List<IConditionalModel>? dataRuleJsonCustomJson = new();
            if (pageInput.dataRuleJsonCustom.IsNotEmptyOrNull())
            {
                dataRuleJsonCustomJson = _sqlSugarClient.Utilities.JsonToConditionalModels(pageInput.dataRuleJsonCustom);
            }

            List<IConditionalModel>? querJson = new();
            if (pageInput.queryJson.IsNotEmptyOrNull())
            {
                querJson = _sqlSugarClient.Utilities.JsonToConditionalModels(pageInput.queryJson);
            }

            List<IConditionalModel>? superQueryJson = new();
            if (pageInput.superQueryJson.IsNotEmptyOrNull())
            {
                superQueryJson = _sqlSugarClient.Utilities.JsonToConditionalModels(pageInput.superQueryJson);
            }

            // var sql = _sqlSugarClient.SqlQueryable<object>(strSql)
            // .Where(dataRuleJson).Where(querJson).Where(superQueryJson, true).Where(dataPermissions).ToSqlString();
            DataTable dt = _sqlSugarClient.SqlQueryable<object>(strSql)
                .Where(dataRuleJsonCustomJson)
                .Where(dataRuleJson).Where(querJson).Where(superQueryJson, true).Where(dataPermissions)
                .OrderBy(sidx)
                .ToDataTablePage(pageInput.currentPage, pageInput.pageSize, ref total);

            // 如果有字段别名 替换 ColumnName
            if (outColumnName != null && outColumnName.Count > 0)
            {
                string? resultKey = string.Empty;
                for (int i = 0; i < dt.Columns.Count; i++)
                {
                    dt.Columns[i].ColumnName =
                        outColumnName.TryGetValue(dt.Columns[i].ColumnName.ToUpper(), out resultKey)
                            ? outColumnName[dt.Columns[i].ColumnName.ToUpper()]
                            : dt.Columns[i].ColumnName.ToUpper();
                }
            }

            int totalPages = (int)Math.Ceiling(total / (double)pageInput.pageSize);
            SqlSugarPagedList<Dictionary<string, object>> data = new()
            {
                PageNo = pageInput.currentPage,
                PageSize = pageInput.pageSize,
                TotalRows = total,
                TotalPage = totalPages,
                HasNextPage = pageInput.currentPage < totalPages,
                HasPrevPage = pageInput.currentPage - 1 > 0,
                Rows = dt.ToObjectOld<List<Dictionary<string, object>>>()
            };

            foreach (Dictionary<string, object> dict in data.Rows)
            {
                foreach ((string key, object value) in dict)
                {
                    try
                    {
                        dict[key] = value.GetJsonElementValue();
                    }
                    catch
                    {
                        dict[key] = null;
                    }
                }
            }

            return data;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     根据链接获取数据
    /// </summary>
    /// <param name="link">数据连接</param>
    /// <param name="strSql">sqlyuju </param>
    /// <param name="parameters">参数</param>
    /// <returns></returns>
    public DataTable GetInterFaceData(DbLink link, string strSql, params SugarParameter[] parameters)
    {
        if (link != null)
        {
            _sqlSugarClient = ChangeDataBase(link);
        }

        if (_sqlSugarClient.CurrentConnectionConfig.DbType == DbType.Oracle)
        {
            strSql = strSql.Replace(";", "");
        }

        DataTable? dt = _sqlSugarClient.Ado.GetDataTable(strSql, parameters);
        return dt;
    }

    /// <summary>
    ///     执行增删改sql
    /// </summary>
    /// <param name="link">数据连接</param>
    /// <param name="strSql">sqlyuju </param>
    /// <param name="parameters">参数</param>
    /// <returns></returns>
    public void ExecuteCommand(DbLink link, string strSql, params SugarParameter[] parameters)
    {
        if (link != null)
        {
            _sqlSugarClient = ChangeDataBase(link);
        }

        if (_sqlSugarClient.CurrentConnectionConfig.DbType == DbType.Oracle)
        {
            strSql = strSql.Replace(";", "");
        }

        _sqlSugarClient.Ado.ExecuteCommand(strSql, parameters);
    }

    /// <summary>
    ///     数据库切换.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <returns>切库后的SqlSugarClient.</returns>
    public SqlSugarScope ChangeDataBase(DbLink link)
    {
        if (link != null && _sqlSugarClient.CurrentConnectionConfig.ConfigId.ToString() != link.Id.ToString())
        {
            if (_sqlSugarClient.AsTenant().IsAnyConnection(link.Id))
            {
                _sqlSugarClient.ChangeDatabase(link.Id);
            }
            else
            {
                string connect = ToConnectionString(link, true);
                _sqlSugarClient.AddConnection(new ConnectionConfig
                {
                    ConfigId = link.Id,
                    DbType = ToDbType(link.DbType),
                    ConnectionString = connect,
                    InitKeyType = InitKeyType.Attribute,
                    IsAutoCloseConnection = true
                });

                _sqlSugarClient.Ado.CommandTimeOut = 30;
                _sqlSugarClient.ChangeDatabase(link.Id);
                if (enableConsoleSql)
                {
                    _sqlSugarClient.Aop.OnLogExecuting = (sql, pars) =>
                    {
                        if (sql.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
                        {
                            Console.ForegroundColor = ConsoleColor.Green;
                        }

                        if (sql.StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase) ||
                            sql.StartsWith("INSERT", StringComparison.OrdinalIgnoreCase))
                        {
                            Console.ForegroundColor = ConsoleColor.White;
                        }

                        if (sql.StartsWith("DELETE", StringComparison.OrdinalIgnoreCase))
                        {
                            Console.ForegroundColor = ConsoleColor.Blue;
                        }

                        // 在控制台输出sql语句
                        Console.WriteLine("【" + DateTime.Now + "——执行SQL】\r\n" +
                                          UtilMethods.GetSqlString(_sqlSugarClient.CurrentConnectionConfig.DbType, sql,
                                              pars) + "\r\n");
                    };

                    _sqlSugarClient.Aop.OnError = ex =>
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        // string? pars = _sqlSugarClient.Utilities.SerializeObject(
                        //     ((SugarParameter[])ex.Parametres).ToDictionary(it => it.ParameterName, it => it.Value));
                        Console.WriteLine("【" + DateTime.Now + "——错误SQL】\r\n" +
                                          UtilMethods.GetSqlString(_sqlSugarClient.CurrentConnectionConfig.DbType,
                                              ex.Sql, (SugarParameter[])ex.Parametres) +
                                          "\r\n");
                    };
                }
                if (_sqlSugarClient.CurrentConnectionConfig.DbType == DbType.Oracle)
                {
                    _sqlSugarClient.Aop.OnExecutingChangeSql = (sql, pars) =>
                    {
                        if (pars != null)
                        {
                            foreach (SugarParameter? item in pars)
                            {
                                // 如果是DbTppe=string设置成OracleDbType.Nvarchar2
                                item.IsNvarchar2 = true;
                            }
                        }

                        return new KeyValuePair<string, SugarParameter[]>(sql, pars);
                    };
                }
            }
        }

        return _sqlSugarClient;
    }

    /// <summary>
    ///     获取租户SqlSugarClient客户端.
    /// </summary>
    /// <param name="tenantId">租户id.</param>
    /// <returns></returns>
    public ISqlSugarClient GetTenantSqlSugarClient(string tenantId, GlobalTenantCacheModel globalTenantCache = null)
    {
        GlobalTenantCacheModel? tenant = globalTenantCache ?? GetGlobalTenantCache(tenantId);
        if (!_sqlSugarClient.AsTenant().IsAnyConnection(tenantId))
        {
            _sqlSugarClient.AddConnection(new ConnectionConfig
            {
                ConfigId = tenant.TenantId,
                DbType = tenant.connectionConfig.ConfigList.FirstOrDefault().dbType,
                ConnectionString = tenant.connectionConfig.ConfigList.FirstOrDefault().connectionStr,
                InitKeyType = InitKeyType.Attribute,
                IsAutoCloseConnection = true
            });
        }

        _sqlSugarClient.ChangeDatabase(tenantId);

        if (tenant != null && !"default".Equals(tenantId) && tenant.type == 1)
        {
            _sqlSugarClient.QueryFilter.Clear();
            _sqlSugarClient.QueryFilter.AddTableFilter<ITenantFilter>(it =>
                it.TenantId == tenant.connectionConfig.IsolationField);
            _sqlSugarClient.Aop.DataExecuting = (oldValue, entityInfo) =>
            {
                if (entityInfo.PropertyName == "TenantId" && entityInfo.OperationType == DataFilterType.InsertByObject)
                {
                    entityInfo.SetValue(tenant.connectionConfig.IsolationField);
                }

                if (entityInfo.PropertyName == "TenantId" && entityInfo.OperationType == DataFilterType.UpdateByObject)
                {
                    entityInfo.SetValue(tenant.connectionConfig.IsolationField);
                }

                if (entityInfo.PropertyName == "TenantId" && entityInfo.OperationType == DataFilterType.DeleteByObject)
                {
                    entityInfo.SetValue(tenant.connectionConfig.IsolationField);
                }
            };
        }

        _sqlSugarClient.Ado.CommandTimeOut = 30;

        if (enableConsoleSql)
        {
            _sqlSugarClient.Aop.OnLogExecuting = (sql, pars) =>
            {
                if (sql.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.Green;
                }

                if (sql.StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase) ||
                    sql.StartsWith("INSERT", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.White;
                }

                if (sql.StartsWith("DELETE", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.Blue;
                }

                // 在控制台输出sql语句
                Console.WriteLine("【" + DateTime.Now + "——执行SQL】\r\n" +
                                  UtilMethods.GetSqlString(_sqlSugarClient.CurrentConnectionConfig.DbType, sql, pars) +
                                  "\r\n");
            };

            _sqlSugarClient.Aop.OnError = ex =>
            {
                Console.ForegroundColor = ConsoleColor.Red;
                string? pars = _sqlSugarClient.Utilities.SerializeObject(
                    ((SugarParameter[])ex.Parametres).ToDictionary(it => it.ParameterName, it => it.Value));
                Console.WriteLine("【" + DateTime.Now + "——错误SQL】\r\n" +
                                  UtilMethods.GetSqlString(_sqlSugarClient.CurrentConnectionConfig.DbType, ex.Sql,
                                      (SugarParameter[])ex.Parametres) + "\r\n");
            };
        }

        if (_sqlSugarClient.CurrentConnectionConfig.DbType == DbType.Oracle)
        {
            _sqlSugarClient.Aop.OnExecutingChangeSql = (sql, pars) =>
            {
                if (pars != null)
                {
                    foreach (SugarParameter? item in pars)
                    {
                        // 如果是DbTppe=string设置成OracleDbType.Nvarchar2
                        item.IsNvarchar2 = true;
                    }
                }

                return new KeyValuePair<string, SugarParameter[]>(sql, pars);
            };
        }

        return _sqlSugarClient;
    }

    /// <summary>
    ///     获取多租户Link.
    /// </summary>
    /// <param name="tenantId">租户ID.</param>
    /// <param name="tenantName">租户数据库.</param>
    /// <returns></returns>
    public DbLink GetTenantDbLink(long tenantId = 0, string tenantName = "")
    {
        DbLink entity = new()
        {
            Id = tenantId != 0 ? defaultConnectionConfig.ConfigId.ParseToLong() : tenantId,
            DbType = defaultConnectionConfig.DbType.ToString().ToLower() == "mysql"
                ? DbTypeEnum.MySql
                : DbTypeEnum.SqlServer,
            Host = defaultConnectionConfig.Host,
            Port = defaultConnectionConfig.Port,
            UserName = defaultConnectionConfig.UserName,
            Password = defaultConnectionConfig.Password
        };
        entity.DataBaseName = tenantName.IsNullOrEmpty() ? defaultConnectionConfig.DBName : tenantName;
        return entity;
    }

    /// <summary>
    ///     获取全局租户缓存.
    /// </summary>
    /// <returns></returns>
    private GlobalTenantCacheModel GetGlobalTenantCache(string tenantId)
    {
        string cacheKey = string.Format("{0}", CommonConst.GLOBALTENANT);
        return _cacheManager.Get<List<GlobalTenantCacheModel>>(cacheKey).Find(it => it.TenantId.Equals(tenantId));
    }

    #endregion

    #region Sql

    /// <summary>
    ///     执行增删改sql.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="strSql">sql语句.</param>
    /// <param name="isCopyNew">是否CopyNew.</param>
    /// <param name="parameters">参数.</param>
    /// <returns></returns>
    public async Task<int> ExecuteSql(DbLink link, string strSql, bool isCopyNew = false,
        params SugarParameter[] parameters)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);
            if (_sqlSugarClient.CurrentConnectionConfig.DbType == DbType.Oracle)
            {
                strSql = strSql.TrimEnd(';');
            }

            if (isCopyNew)
            {
                return await _sqlSugarClient.CopyNew().Ado.ExecuteCommandAsync(strSql, parameters);
            }
            else
            {
                return await _sqlSugarClient.Ado.ExecuteCommandAsync(strSql, parameters);
            }
        }
        catch (Exception e)
        {
            throw Oops.Oh(e.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     执行Sql(新增、修改).
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <param name="dicList">数据.</param>
    /// <param name="primaryField">主键字段.</param>
    /// <returns></returns>
    public async Task<int> ExecuteSql(DbLink link, string table, List<Dictionary<string, object>> dicList,
        string primaryField = "")
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            int flag = 0;
            if (string.IsNullOrEmpty(primaryField))
            {
                foreach (Dictionary<string, object> item in dicList)
                {
                    flag = await _sqlSugarClient.Insertable(item).AS(table).ExecuteCommandAsync();
                }
            }
            else
            {
                foreach (Dictionary<string, object> item in dicList)
                {
                    flag = await _sqlSugarClient.Updateable(item).AS(table).WhereColumns(primaryField)
                        .ExecuteCommandAsync();
                }
            }

            return flag;
        }
        catch (Exception e)
        {
            throw Oops.Oh(e.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     执行Sql 新增 并返回自增长Id.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <param name="dicList">数据.</param>
    /// <returns>id.</returns>
    public int ExecuteReturnIdentity(DbLink link, string table, List<Dictionary<string, object>> dicList)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            int id = _sqlSugarClient.Insertable(dicList).AS(table).ExecuteReturnIdentity();
            return id;
        }
        catch (Exception e)
        {
            throw Oops.Oh(e.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     查询sql.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="strSql">sql语句.</param>
    /// <param name="isCopyNew">是否CopyNew.</param>
    /// <param name="parameters">参数.</param>
    /// <returns></returns>
    public DataTable GetSqlData(DbLink link, string strSql, bool isCopyNew = false, params SugarParameter[] parameters)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            if (_sqlSugarClient.CurrentConnectionConfig.DbType == DbType.Oracle)
            {
                strSql = strSql.Replace(";", string.Empty);
            }

            DataTable? data = isCopyNew
                ? _sqlSugarClient.CopyNew().Ado.GetDataTable(strSql, parameters)
                : _sqlSugarClient.Ado.GetDataTable(strSql, parameters);

            return data;
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     动态执行SQL
    /// </summary>
    /// <param name="link">数据连接</param>
    /// <param name="sql"></param>
    /// <returns></returns>
    public async Task<IDictionary<string, object?>> QuerySingle(DbLink link, string sql)
    {
        // 切换数据库
        _sqlSugarClient = ChangeDataBase(link);
        var obj = await _sqlSugarClient.Ado.SqlQuerySingleAsync<object>(sql);
        return DictionaryExtensions.ToDictionary(obj);
    }

    /// <summary>
    ///     条件动态过滤.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="strSql">sql语句.</param>
    /// <returns>条件是否成立.</returns>
    public bool WhereDynamicFilter(DbLink link, string strSql)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);
            bool flag = _sqlSugarClient.Ado.SqlQuery<dynamic>(strSql).Count > 0;
            ChangeDefaultDatabase();
            return flag;
        }
        catch (Exception e)
        {
            throw Oops.Oh(e.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     执行统计sql.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="strSql">sql语句.</param>
    /// <param name="isCopyNew">是否CopyNew.</param>
    /// <param name="parameters">参数.</param>
    public int GetCount(DbLink link, string strSql, bool isCopyNew = false, params SugarParameter[] parameters)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            if (_sqlSugarClient.CurrentConnectionConfig.DbType == DbType.Oracle)
            {
                strSql = strSql.Replace(";", string.Empty);
            }

            int count = isCopyNew
                ? _sqlSugarClient.CopyNew().Ado.GetInt(strSql, parameters)
                : _sqlSugarClient.CopyNew().Ado.GetInt(strSql, parameters);

            return count;
        }
        catch (Exception e)
        {
            throw Oops.Oh(e.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     使用存储过程.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="stored">存储过程名称.</param>
    /// <param name="parameters">参数.</param>
    public void UseStoredProcedure(DbLink link, string stored, List<SugarParameter> parameters)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            _sqlSugarClient.Ado.UseStoredProcedure().GetDataTable(stored, parameters);
        }
        catch (Exception e)
        {
            throw Oops.Oh(e.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     获取数据表分页(SQL语句).
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="dbSql">数据SQL.</param>
    /// <param name="pageIndex">页数.</param>
    /// <param name="pageSize">条数.</param>
    /// <param name="parameters">SQL参数.</param>
    /// <returns></returns>
    public async Task<dynamic> GetDataTablePage(DbLink link, string dbSql, int pageIndex, int pageSize, params SugarParameter[] parameters)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            RefAsync<int> totalNumber = 0;
            DataTable? list = await _sqlSugarClient.SqlQueryable<object>(dbSql)
                .AddParameters(parameters)
                .ToDataTablePageAsync(pageIndex, pageSize, totalNumber);
            int totalPages = (int)Math.Ceiling(totalNumber / (double)pageSize);
            dynamic data = new SqlSugarPagedList<dynamic>
            {
                Rows = ToDynamicList(list),
                PageSize = pageSize,
                TotalPage = totalPages,
                TotalRows = totalNumber,
                HasNextPage = pageIndex < totalPages,
                HasPrevPage = pageIndex - 1 > 0,
                PageNo = pageIndex
            };
            return data;
        }
        catch (Exception e)
        {
            throw Oops.Oh(e.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     获取数据表分页(实体).
    /// </summary>
    /// <typeparam name="TEntity">T.</typeparam>
    /// <param name="link">数据连接.</param>
    /// <param name="pageIndex">页数.</param>
    /// <param name="pageSize">条数.</param>
    /// <returns></returns>
    public async Task<List<TEntity>> GetDataTablePage<TEntity>(DbLink link, int pageIndex, int pageSize)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);
            List<TEntity>? data = await _sqlSugarClient.Queryable<TEntity>().ToPageListAsync(pageIndex, pageSize);
            return data;
        }
        catch (Exception e)
        {
            throw Oops.Oh(e.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    #endregion

    #region 数据库

    /// <summary>
    ///     创建表.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableModel">表对象.</param>
    /// <param name="tableFieldList">字段对象.</param>
    /// <returns></returns>
    public bool Create(DbLink link, DbTableModel tableModel, List<DbTableFieldModel> tableFieldList)
    {
        _sqlSugarClient = ChangeDataBase(link);
        try
        {
            CreateTable(tableModel, tableFieldList);
            return true;
        }
        catch (AppFriendlyException ex)
        {
            throw Oops.Oh(ex.ErrorCode, ex.Data);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     删除表.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <returns></returns>
    public bool Delete(DbLink link, string table)
    {
        _sqlSugarClient = ChangeDataBase(link);
        try
        {
            _sqlSugarClient.DbMaintenance.DropTable(table);
            return true;
        }
        catch
        {
            return false;
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     修改表.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="oldTable">原数据.</param>
    /// <param name="tableModel">表对象.</param>
    /// <param name="tableFieldList">字段对象.</param>
    /// <returns></returns>
    public bool Update(DbLink link, string oldTable, DbTableModel tableModel, List<DbTableFieldModel> tableFieldList)
    {
        _sqlSugarClient = ChangeDataBase(link);
        try
        {
            _sqlSugarClient.DbMaintenance.DropTable(oldTable);
            CreateTable(tableModel, tableFieldList);
            return true;
        }
        catch (AppFriendlyException ex)
        {
            throw Oops.Oh(ex.ErrorCode, ex.Data);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     sqlsugar建表.
    /// </summary>
    /// <param name="tableModel">表.</param>
    /// <param name="tableFieldList">字段.</param>
    private void CreateTable(DbTableModel tableModel, List<DbTableFieldModel> tableFieldList)
    {
        List<DbColumnInfo> cloumnList = tableFieldList.Adapt<List<DbColumnInfo>>();
        DelDataLength(cloumnList);
        _sqlSugarClient.DbMaintenance.CreateTable(tableModel.table, cloumnList);
        _sqlSugarClient.DbMaintenance.AddTableRemark(tableModel.table, tableModel.tableName);
        // mysql不需要单独添加字段注释
        if (_sqlSugarClient.CurrentConnectionConfig.DbType != DbType.MySql)
        {
            foreach (DbColumnInfo item in cloumnList)
            {
                _sqlSugarClient.DbMaintenance.AddColumnRemark(item.DbColumnName, tableModel.table,
                    item.ColumnDescription);
            }
        }
    }

    /// <summary>
    ///     sqlsugar添加表字段.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableInfo">表信息.</param>
    /// <param name="tableFieldList">表字段.</param>
    public void AddTableColumn(DbLink link, TableInfoOutput tableInfo, List<DbTableFieldModel> tableFieldList)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);
            List<DbColumnInfo> columnList = tableFieldList.Adapt<List<DbColumnInfo>>();
            DelDataLength(columnList);
            foreach (DbColumnInfo item in columnList)
            {
                _sqlSugarClient.DbMaintenance.AddColumn(tableInfo.table, item);
                if (_sqlSugarClient.CurrentConnectionConfig.DbType != DbType.MySql)
                {
                    _sqlSugarClient.DbMaintenance.AddColumnRemark(item.DbColumnName, tableInfo.table,
                        item.ColumnDescription);
                }
            }

            _sqlSugarClient.DbMaintenance.DeleteTableRemark(tableInfo.table);
            _sqlSugarClient.DbMaintenance.AddTableRemark(tableInfo.table, tableInfo.tableName);
            if (!tableInfo.table.Equals(tableInfo.newTable))
            {
                _sqlSugarClient.DbMaintenance.RenameTable(tableInfo.table, tableInfo.newTable);
            }
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     sqlsugar添加表字段.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableName">表名.</param>
    /// <param name="tableFieldList">表字段.</param>
    public void AddTableColumn(DbLink link, string tableName, List<DbTableFieldModel> tableFieldList)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);
            List<DbColumnInfo> cloumnList = tableFieldList.Adapt<List<DbColumnInfo>>();
            DelDataLength(cloumnList);
            foreach (DbColumnInfo item in cloumnList)
            {
                _sqlSugarClient.DbMaintenance.AddColumn(tableName, item);
                if (_sqlSugarClient.CurrentConnectionConfig.DbType != DbType.MySql)
                {
                    _sqlSugarClient.DbMaintenance.AddColumnRemark(item.DbColumnName, tableName, item.ColumnDescription);
                }
            }
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     表是否存在.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <returns></returns>
    public bool IsAnyTable(DbLink link, string table)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            bool flag = _sqlSugarClient.DbMaintenance.IsAnyTable(table, false);

            return flag;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     表是否存在数据.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <returns></returns>
    public bool IsAnyData(DbLink link, string table)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            bool flag = _sqlSugarClient.Queryable<dynamic>().AS(table).Any();
            return flag;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     表字段是否存在.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="table">表名.</param>
    /// <param name="column">表字段名.</param>
    /// <returns></returns>
    public bool IsAnyColumn(DbLink link, string table, string column)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            bool flag = _sqlSugarClient.DbMaintenance.IsAnyColumn(table, column, false);

            return flag;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     获取表字段列表.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableName">表名.</param>
    /// <returns>TableFieldListModel.</returns>
    public List<DbTableFieldModel> GetFieldList(DbLink? link, string? tableName)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            List<DbColumnInfo>? list = _sqlSugarClient.DbMaintenance.GetColumnInfosByTableName(tableName, false);

            return list.Adapt<List<DbTableFieldModel>>();
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     获取表数据.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    public DataTable GetData(DbLink link, string tableName)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);
            DataTable? data = _sqlSugarClient.Queryable<dynamic>().AS(tableName).ToDataTable();
            return data;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     获取表信息.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    public DatabaseTableInfoOutput GetDataBaseTableInfo(DbLink link, string tableName)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            DatabaseTableInfoOutput data = new()
            {
                tableInfo = _sqlSugarClient.DbMaintenance.GetTableInfoList(false).Find(m => m.Name == tableName)
                    .Adapt<TableInfoOutput>(),
                tableFieldList = _sqlSugarClient.DbMaintenance.GetColumnInfosByTableName(tableName, false)
                    .Adapt<List<TableFieldOutput>>()
            };

            data.tableFieldList =
                ViewDataTypeConversion(data.tableFieldList, _sqlSugarClient.CurrentConnectionConfig.DbType);
            return data;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     获取数据库表信息.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <returns></returns>
    public List<DatabaseTableListOutput> GetDBTableList(DbLink link)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            DbTypeEnum dbType = link.DbType;
            string sql = DBTableSql(dbType);
            List<DatabaseTableListOutput> data = new();

            List<DynamicDbTableModel> modelList = _sqlSugarClient.Ado.SqlQuery<DynamicDbTableModel>(sql).ToList();
            data = modelList.Select(x => new DatabaseTableListOutput
            { table = x.F_TABLE, tableName = x.F_TABLENAME, sum = x.F_SUM.ParseToInt() }).ToList();
            return data;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     获取数据库表信息.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="isView">视图.</param>
    /// <returns></returns>
    public List<DbTableInfo> GetTableInfos(DbLink link, bool isView = false)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            List<DbTableInfo>? data = isView
                ? _sqlSugarClient.DbMaintenance.GetViewInfoList(false)
                : _sqlSugarClient.DbMaintenance.GetTableInfoList(false);
            return data;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     同步数据.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <param name="dt">数据.</param>
    /// <param name="table">表名.</param>
    /// <returns></returns>
    public async Task<bool> SyncData(DbLink link, DataTable dt, string table)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);

            List<Dictionary<string, object>>
                dic = _sqlSugarClient.Utilities.DataTableToDictionaryList(dt); // 5.0.23版本支持
            int isOk = await _sqlSugarClient.Insertable(dic).AS(table).ExecuteCommandAsync();
            return isOk > 0;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     同步表操作.
    /// </summary>
    /// <param name="linkFrom">原数据库.</param>
    /// <param name="linkTo">目前数据库.</param>
    /// <param name="table">表名称.</param>
    /// <param name="type">操作类型.</param>
    /// <param name="fieldType">数据类型.</param>
    public void SyncTable(DbLink linkFrom, DbLink linkTo, string table, int type, Dictionary<string, string> fieldType)
    {
        try
        {
            switch (type)
            {
                case 2:
                    {
                        if (linkFrom != null)
                        {
                            _sqlSugarClient = ChangeDataBase(linkFrom);
                        }

                        List<DbColumnInfo>? columns = _sqlSugarClient.DbMaintenance.GetColumnInfosByTableName(table, false);
                        if (linkTo != null)
                        {
                            _sqlSugarClient = ChangeDataBase(linkTo);
                        }

                        DelDataLength(columns, fieldType);
                        _sqlSugarClient.DbMaintenance.CreateTable(table, columns);
                    }
                    break;
                case 3:
                    {
                        if (linkTo != null)
                        {
                            _sqlSugarClient = ChangeDataBase(linkTo);
                        }

                        _sqlSugarClient.DbMaintenance.TruncateTable(table);
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     测试数据库连接.
    /// </summary>
    /// <param name="link">数据连接.</param>
    /// <returns></returns>
    public bool IsConnection(DbLink link)
    {
        try
        {
            _sqlSugarClient = ChangeDataBase(link);
            bool flag = _sqlSugarClient.Ado.IsValidConnection();
            return flag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ErrorCode.D1507 + "：" + ex.Message);
            throw new Exception(ErrorCode.D1507 + "：" + ex.Message);
        }
        finally
        {
            ChangeDefaultDatabase();
        }
    }

    /// <summary>
    ///     转换连接字符串
    /// </summary>
    /// <param name="dbLink"></param>
    /// <returns></returns>
    public static void DecryptPassword(DbLink dbLink)
    {
        string key = "b2252bd356842342"; // 16个字符的密钥
        byte[] encryptedBytes = Convert.FromBase64String(dbLink.Password);
        string password = StringExtension.DecryptAes(encryptedBytes, Encoding.UTF8.GetBytes(key));
        dbLink.ConnectString = dbLink.ConnectString.Replace(dbLink.Password, password);
    }

    /// <summary>
    ///     转换连接字符串
    /// </summary>
    /// <param name="dbLink"></param>
    /// <param name="decrypt">解密</param>
    /// <returns></returns>
    public string ToConnectionString(DbLink dbLink, bool decrypt = true)
    {
        string password = dbLink.Password;
        if (decrypt)
        {
            string key = "b2252bd356842342"; // 16个字符的密钥
            byte[] encryptedBytes = Convert.FromBase64String(dbLink.Password);
            password = StringExtension.DecryptAes(encryptedBytes, Encoding.UTF8.GetBytes(key));
        }

        // 返回自定义连接字符串
        if (dbLink.ConnectString.IsNotEmptyOrNull())
        {
            return dbLink.ConnectString.Replace(dbLink.Password, password);
        }

        switch (dbLink.DbType)
        {
            case DbTypeEnum.MySql:
                return string.Format(
                    "server={0};port={1};database={2};user={3};password={4};AllowLoadLocalInfile=true;", dbLink.Host,
                    dbLink.Port.ToString(), dbLink.DataBaseName,
                    dbLink.UserName,
                    password);
            case DbTypeEnum.SqlServer:
                return string.Format(
                    "Data Source={0},{4};Initial Catalog={1};User ID={2};Password={3};MultipleActiveResultSets=true;",
                    dbLink.Host, dbLink.DataBaseName,
                    dbLink.UserName, password,
                    dbLink.Port);
            case DbTypeEnum.PostgreSql:
                return $"server={dbLink.Host};port={dbLink.Port};Database={dbLink.DataBaseName};User Id={dbLink.UserName};Password={dbLink.Password};searchpath={dbLink.DBSchema};";
            case DbTypeEnum.KingbaseEs:
                return $"server={dbLink.Host};port={dbLink.Port};database={dbLink.DataBaseName};UID={dbLink.UserName};PWD={dbLink.Password};searchpath={dbLink.DBSchema};";
            default:
                throw Oops.Oh("暂不支持");
        }
    }

    /// <summary>
    ///     视图数据类型转换.
    /// </summary>
    /// <param name="fields">字段数据.</param>
    /// <param name="databaseType">数据库类型.</param>
    public List<TableFieldOutput> ViewDataTypeConversion(List<TableFieldOutput> fields, DbType databaseType)
    {
        foreach (TableFieldOutput item in fields)
        {
            item.dataType = item.dataType.ToLower();
            switch (item.dataType)
            {
                // 字符串类型统一为varchar
                case "string":
                case "char":
                case "nchar":
                case "nvarchar":
                case "character varying":
                case "character":
                    {
                        item.dataType = "varchar";
                        if (item.dataLength.ParseToInt() > 2000)
                        {
                            item.dataType = "text";
                            item.dataLength = "50";
                        }
                    }
                    break;

                // 整型统一为int
                case "int32":
                case "int4":
                case "integer":
                case "smallint":
                case "int2":
                case "tinyint":
                    item.dataType = "int";
                    break;

                // 长整型统一为bigint
                case "int64":
                case "int8":
                case "long":
                case "bigserial":
                    item.dataType = "bigint";
                    break;

                // 浮点数统一为decimal
                case "single":
                case "float":
                case "double":
                case "real":
                case "money":
                case "float4":
                case "float8":
                case "double precision":
                case "numeric":
                    item.dataType = "decimal";
                    break;

                // 日期时间统一为datetime
                case "date":
                case "time":
                case "timestamp":
                case "timestamptz":
                case "timestamp without time zone":
                case "timestamp with time zone":
                case "datetime2":
                case "smalldatetime":
                    item.dataType = "datetime";
                    break;

                // 文本类型统一为text
                case "longtext":
                case "clob":
                case "text[]":
                case "json":
                case "jsonb":
                case "xml":
                    item.dataType = "text";
                    break;

                // 布尔类型统一为tinyint
                case "bit":
                case "bool":
                case "boolean":
                    item.dataType = "tinyint";
                    break;
            }
        }

        return fields;
    }

    /// <summary>
    ///     转换数据库类型.
    /// </summary>
    /// <param name="dbType">数据库类型.</param>
    /// <returns></returns>
    public DbType ToDbType(DbTypeEnum dbType)
    {
        switch (dbType)
        {
            case DbTypeEnum.MySql:
                return DbType.MySql;
            case DbTypeEnum.SqlServer:
                return DbType.SqlServer;
            case DbTypeEnum.KingbaseEs:
                return DbType.Kdbndp;
            case DbTypeEnum.PostgreSql:
                return DbType.PostgreSQL;
            default:
                throw Oops.Oh(ErrorCode.D1505);
        }
    }

    /// <summary>
    ///     将DataTable 转换成 List<dynamic />
    /// </summary>
    /// <param name="table">DataTable</param>
    /// <param name="reverse">
    ///     反转：控制返回结果中是只存在 FilterField 指定的字段,还是排除.
    ///     [flase 返回FilterField 指定的字段]|[true 返回结果剔除 FilterField 指定的字段]
    /// </param>
    /// <param name="filterField">字段过滤，FilterField 为空 忽略 reverse 参数；返回DataTable中的全部数据</param>
    /// <returns></returns>
    public static List<dynamic> ToDynamicList(DataTable table, bool reverse = true, params string[] filterField)
    {
        List<dynamic> modelList = new();
        foreach (DataRow row in table.Rows)
        {
            dynamic model = new ExpandoObject();
            IDictionary<string, object> dict = (IDictionary<string, object>)model;
            foreach (DataColumn column in table.Columns)
            {
                if (filterField.Length != 0)
                {
                    if (reverse)
                    {
                        if (!filterField.Contains(column.ColumnName))
                        {
                            dict[column.ColumnName] = row[column];
                        }
                    }
                    else
                    {
                        if (filterField.Contains(column.ColumnName))
                        {
                            dict[column.ColumnName] = row[column];
                        }
                    }
                }
                else
                {
                    dict[column.ColumnName] = row[column];
                }
            }

            modelList.Add(model);
        }

        return modelList;
    }

    /// <summary>
    ///     数据库表SQL.
    /// </summary>
    /// <param name="dbType">数据库类型.</param>
    /// <returns></returns>
    private string DBTableSql(DbTypeEnum dbType)
    {
        StringBuilder sb = new();
        switch (dbType)
        {
            // case "oracle":
            //     sb.Append(@"SELECT table_name F_TABLE , (select COMMENTS from user_tab_comments where t.table_name=table_name ) as F_TABLENAME, T.NUM_ROWS F_SUM from user_tables t where table_name!='HELP' AND table_name NOT LIKE '%$%' AND table_name NOT LIKE 'LOGMNRC_%' AND table_name!='LOGMNRP_CTAS_PART_MAP' AND table_name!='LOGMNR_LOGMNR_BUILDLOG' AND table_name!='SQLPLUS_PRODUCT_PROFILE'");
            //     break;
            // case "dm8":
            // case "dm":
            //     sb.Append(@"SELECT table_name F_TABLE , (select COMMENTS from user_tab_comments where t.table_name=table_name ) as F_TABLENAME, T.NUM_ROWS F_SUM from user_tables t where table_name!='HELP' AND table_name NOT LIKE '%$%' AND table_name NOT LIKE 'LOGMNRC_%' AND table_name!='LOGMNRP_CTAS_PART_MAP' AND table_name!='LOGMNR_LOGMNR_BUILDLOG' AND table_name!='SQLPLUS_PRODUCT_PROFILE'");
            //     break;
            // case "kdbndp":
            case DbTypeEnum.KingbaseEs:
                sb.Append(@"select a.relname F_TABLE,a.n_live_tup F_SUM,b.description F_TABLENAME from sys_stat_user_tables a left outer join sys_description b on a.relid = b.objoid where a.schemaname='public' and b.objsubid='0'");
                break;
            case DbTypeEnum.PostgreSql:
                sb.Append(@"select cast(relname as varchar) as F_TABLE,cast(reltuples as int) as F_SUM, cast(obj_description(relfilenode,'pg_class') as varchar) as F_TABLENAME from pg_class c inner join pg_namespace n on n.oid = c.relnamespace and nspname='public' inner join pg_tables z on z.tablename=c.relname where relkind = 'r' and relname not like 'pg_%' and relname not like 'sql_%' and schemaname='public' order by relname");
                break;
            case DbTypeEnum.MySql:
                sb.Append(
                    @"select TABLE_NAME as F_TABLE,TABLE_ROWS as F_SUM ,TABLE_COMMENT as F_TABLENAME from information_schema.tables where TABLE_SCHEMA=(select database()) AND TABLE_TYPE='BASE TABLE'");
                break;
            case DbTypeEnum.SqlServer:
                sb.Append(
                    @"SELECT s.Name F_TABLE, Convert(nvarchar(max), tbp.value) as F_TABLENAME, b.ROWS F_SUM FROM sysobjects s LEFT JOIN sys.extended_properties as tbp ON s.id = tbp.major_id and tbp.minor_id = 0 AND ( tbp.Name = 'MS_Description' OR tbp.Name is null ) LEFT JOIN sysindexes AS b ON s.id = b.id WHERE s.xtype IN('U') AND (b.indid IN (0, 1))");
                break;
            default:
                throw new Exception("不支持");
        }

        return sb.ToString();
    }

    /// <summary>
    ///     删除列长度(SqlSugar除了字符串其他不需要类型长度).
    /// </summary>
    /// <param name="dbColumnInfos"></param>
    /// <param name="dataTypeDic"></param>
    private void DelDataLength(List<DbColumnInfo> dbColumnInfos, Dictionary<string, string> dataTypeDic = null)
    {
        foreach (DbColumnInfo item in dbColumnInfos)
        {
            if (item.IsIdentity)
            {
                if ("int".Equals(item.DataType.ToLower()) || "bigint".Equals(item.DataType.ToLower()))
                {
                    if (_sqlSugarClient.CurrentConnectionConfig.DbType.Equals(DbType.Oracle) ||
                        _sqlSugarClient.CurrentConnectionConfig.DbType.Equals(DbType.Kdbndp) ||
                        _sqlSugarClient.CurrentConnectionConfig.DbType.Equals(DbType.PostgreSQL))
                    {
                        throw Oops.Oh(ErrorCode.D1519);
                    }
                }
                else
                {
                    throw Oops.Oh(ErrorCode.D1518);
                }
            }

            // if (item.DataType.ToLower() != "varchar" && item.DataType.ToLower() != "nvarchar" && item.DataType.ToLower() != "decimal")
            // if (item.DataType.ToLower() != "varchar" && item.DataType.ToLower() != "nvarchar" && item.DataType.ToLower() != "decimal")
            // {
            //     item.Length = 0;
            // }
            //
            // if (item.DataType.ToLower() != "varchar" && item.DataType.ToLower() != "nvarchar" && item.DataType.ToLower() != "decimal")
            // {
            //     item.DecimalDigits = 0;
            // }

            if (dataTypeDic == null)
            {
                item.DataType = DataTypeConversion(item.DataType.ToLower(), _sqlSugarClient.CurrentConnectionConfig.DbType);
            }
            else
            {
                if (dataTypeDic.ContainsKey(item.DataType.ToLower()))
                {
                    item.DataType = dataTypeDic[item.DataType.ToLower().Replace("(默认)", string.Empty)];
                }
            }
        }
    }

    /// <summary>
    ///     数据库数据类型转换.
    /// </summary>
    /// <param name="dataType">数据类型.</param>
    /// <param name="databaseType">数据库类型</param>
    /// <returns></returns>
    private string DataTypeConversion(string dataType, DbType databaseType)
    {
        if (databaseType.Equals(DbType.Oracle))
        {
            switch (dataType)
            {
                case "text":
                case "longtext":
                    return "CLOB";
                case "decimal":
                    return "DECIMAL";
                case "datetime":
                    return "TIMESTAMP";
                case "bigint":
                    return "NUMBER";
                default:
                    return dataType.ToUpper();
            }
        }

        if (databaseType.Equals(DbType.Dm))
        {
            switch (dataType)
            {
                case "text":
                case "longtext":
                    return "CLOB";
                case "datetime":
                    return "TIMESTAMP";
                default:
                    return dataType.ToUpper();
            }
        }

        if (databaseType.Equals(DbType.Kdbndp))
        {
            switch (dataType)
            {
                case "int":
                    return "INT4";
                case "datetime":
                    return "DATE";
                case "bigint":
                    return "INT8";
                default:
                    return dataType.ToUpper();
            }
        }

        if (databaseType.Equals(DbType.PostgreSQL))
        {
            switch (dataType)
            {
                case "varchar":
                    return "varchar";
                case "int":
                    return "INT4";
                case "datetime":
                    return "timestamp";
                case "decimal":
                    return "DECIMAL";
                case "bigint":
                    return "INT8";
                case "text":
                case "longtext":
                    return "TEXT";
                default:
                    return dataType;
            }
        }

        if (databaseType.Equals(DbType.SqlServer))
        {
            switch (dataType)
            {
                case "longtext":
                    return "nvarchar(max)";
                default:
                    return dataType;
            }
        }

        if (databaseType.Equals(DbType.MySql))
        {
            switch (dataType)
            {
                case "text":
                    return "longtext";
                default:
                    return dataType;
            }
        }

        return dataType;
    }

    /// <summary>
    ///     切换默认库.
    /// </summary>
    private void ChangeDefaultDatabase()
    {
        _sqlSugarClient?.ChangeDatabase(defaultConnectionConfig.ConfigId);
    }

    #endregion

    public void Dispose()
    {
        ChangeDefaultDatabase();
    }
}