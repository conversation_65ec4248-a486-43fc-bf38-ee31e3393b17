using System.Security.Claims;
using Extras.DatabaseAccessor.SqlSugar.Models;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using IotPlatform.Core;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Systems.Entity;

namespace Common.Core.Manager.User;

/// <summary>
///     当前登录用户.
/// </summary>
public class UserManager : IUserManager, IScoped
{
    /// <summary>
    ///     用户表仓储.
    /// </summary>
    private readonly ISqlSugarRepository<SysUser> _repository;

    /// <summary>
    ///     当前Http请求.
    /// </summary>
    private readonly HttpContext _httpContext;

    /// <summary>
    ///     用户Claim主体.
    /// </summary>
    private readonly ClaimsPrincipal _user;

    /// <summary>
    ///     初始化一个<see cref="UserManager" />类型的新实例.
    /// </summary>
    /// <param name="repository">用户仓储.</param>
    public UserManager(ISqlSugarRepository<SysUser> repository)
    {
        _repository = repository;
        _httpContext = App.HttpContext;
        _user = _httpContext?.User;
    }

    /// <summary>
    ///     用户信息.
    /// </summary>
    public SysUser User
    {
        get
        {
            if (_userEntity == null)
            {
                if (UserId == 0)
                {
                    _userEntity = new SysUser()
                    {
                        OrgId = 0,
                        AdminType = AdminTypeEnum.None,
                        Id = 0,
                        Name = "未知用户"
                    };
                }
                else
                    _userEntity = _repository.GetSingle(u => u.Id == UserId);
            }

            return _userEntity;
        }
    }

    public GlobalTenantCacheModel CurrentTenantInformation { get; }

    private SysUser _userEntity { get; set; }

    /// <summary>
    ///     用户ID.
    /// </summary>
    public long UserId => (long) (_user.FindFirst(ClaimConst.UserId)?.Value != null ? Convert.ToUInt64(_user.FindFirst(ClaimConst.UserId)?.Value) : 0);

    /// <summary>
    ///     用户账号.
    /// </summary>
    public string Account => _user.FindFirst(ClaimConst.Account)?.Value;

    /// <summary>
    ///     真实姓名.
    /// </summary>
    public string RealName => _user.FindFirst(ClaimConst.RealName)?.Value;

    /// <summary>
    ///     用户昵称.
    /// </summary>
    public string NickName => _user.FindFirst(ClaimConst.NickName)?.Value;

    /// <summary>
    ///     组织机构Id.
    /// </summary>
    public long OrgId => (long) (_user.FindFirst(ClaimConst.OrgId)?.Value != null ? Convert.ToUInt64(_user.FindFirst(ClaimConst.OrgId)?.Value) : 0);

    /// <summary>
    ///     组织机构.
    /// </summary>
    public string OrgName => _user.FindFirst(ClaimConst.OrgName)?.Value;

    /// <summary>
    ///     租户ID.
    /// </summary>
    public long TenantId => (long) (_user.FindFirst(ClaimConst.TenantId)?.Value != null ? Convert.ToUInt64(_user.FindFirst(ClaimConst.TenantId)?.Value) : 0);

    /// <summary>
    ///     租户数据库名称.
    /// </summary>
    public string TenantDbName => null;

    /// <summary>
    ///     当前用户 token.
    /// </summary>
    public string ToKen => string.IsNullOrEmpty(App.HttpContext?.Request.Headers["Authorization"]) ? App.HttpContext?.Request.Query["token"] : App.HttpContext?.Request.Headers["Authorization"];

    /// <summary>
    ///     是否是管理员.
    /// </summary>
    public bool IsAdministrator => _user.FindFirst(ClaimConst.AdminType)?.Value == ((int) AdminTypeEnum.SuperAdmin).ToString();

    /// <summary>
    ///     获取请求端类型 pc 、 app.
    /// </summary>
    // public string UserOrigin => _httpContext?.Request.Headers["iot-origin"];
    public string UserOrigin => "pc";

    /// <summary>
    ///     获取用户名称.
    /// </summary>
    /// <param name="userId">用户id.</param>
    /// <param name="isAccount">是否带账号.</param>
    /// <returns></returns>
    public string GetUserName(long userId, bool isAccount = true)
    {
        SysUser? entity = _repository.GetFirst(x => x.Id == userId);
        if (entity.IsNullOrEmpty())
        {
            return string.Empty;
        }

        return isAccount ? entity.Name + "/" + entity.Account : entity.Name;
    }

    /// <summary>
    ///     获取用户名称.
    /// </summary>
    /// <param name="userId">用户id.</param>
    /// <param name="isAccount">是否带账号.</param>
    /// <returns></returns>
    public async Task<string> GetUserNameAsync(long userId, bool isAccount = true)
    {
        SysUser? entity = await _repository.GetFirstAsync(x => x.Id == userId);
        if (entity.IsNullOrEmpty())
        {
            return string.Empty;
        }

        return isAccount ? entity.Name + "/" + entity.Account : entity.Name;
    }

    /// <summary>
    ///     获取管理员用户id.
    /// </summary>
    public long GetAdminUserId()
    {
        SysUser? user = _repository.AsSugarClient().Queryable<SysUser>().First(x => x.Account == "admin");
        if (user.IsNotEmptyOrNull())
        {
            return user.Id;
        }

        return 0;
    }
}