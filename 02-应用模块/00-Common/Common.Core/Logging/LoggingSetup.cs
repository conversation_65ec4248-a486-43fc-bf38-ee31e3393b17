namespace Common.Core.Logging;

public static class LoggingSetup
{
    /// <summary>
    ///     日志注册
    /// </summary>
    /// <param name="services"></param>
    public static void AddLoggingSetup(this IServiceCollection services)
    {
        // 日志监听
        services.AddMonitorLogging(options =>
        {
            options.IgnorePropertyNames = new[] {"Byte"};
            options.IgnorePropertyTypes = new[] {typeof(byte[])};
        });
        // 控制台日志
        bool consoleLog = App.GetConfig<bool>("Logging:Monitor:ConsoleLog", true);
        services.AddConsoleFormatter(options =>
        {
            options.DateFormat = "yyyy-MM-dd HH:mm:ss(zzz) dddd";
            options.WithTraceId = true; // 显示线程Id
            options.WithStackFrame = false; // 显示程序集
            options.WriteFilter = logMsg => { return consoleLog; };
        });

        if (App.GetConfig<bool>("Logging:File:Enabled")) // 日志写入文件
        {
            Array.ForEach(new[] {LogLevel.Information, LogLevel.Warning, LogLevel.Error}, logLevel =>
            {
                services.AddFileLogging("logs/" + logLevel + "/{0:yyyy}-{0:MM}-{0:dd}.log", options =>
                {
                    options.FileNameRule = fileName => string.Format(fileName, DateTime.Now); // 每天创建一个文件
                    options.WriteFilter = logMsg => logMsg.LogLevel == logLevel;
                    options.FileSizeLimitBytes = 10 * 1024 * 1024;
                    options.MaxRollingFiles = 15;
                });
            });
        }

        // 日志写入数据库
        if (App.GetConfig<bool>("Logging:Database:Enabled", true))
        {
            services.AddDatabaseLogging<DatabaseLoggingWriter>(options =>
            {
                options.WithTraceId = true; // 显示线程Id
                options.WithStackFrame = false; // 显示程序集
                options.IgnoreReferenceLoop = false; // 忽略循环检测
                // options.MinimumLevel = LogLevel.Information;
                options.WriteFilter = logMsg =>
                {
                    return logMsg.LogName == "System.Logging.LoggingMonitor"; // 只写LoggingMonitor日志
                };
            });
        }
    }
}