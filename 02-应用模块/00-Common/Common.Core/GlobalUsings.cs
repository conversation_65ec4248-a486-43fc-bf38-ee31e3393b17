global using Furion;
global using Furion.DependencyInjection;
global using Furion.EventBus;
global using Furion.FriendlyException;
global using Furion.JsonSerialization;
global using Furion.Logging;
global using Furion.Schedule;
global using Mapster;
global using Microsoft.AspNetCore.Http;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using System.Reflection;
global using UAParser;
global using IotPlatform.Core.Const;