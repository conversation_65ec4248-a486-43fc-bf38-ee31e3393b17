namespace Common.Models;

/// <summary>
///     ELink网关上报平台标准格式
/// </summary>
public class ELinkDevSendInput
{
    /// <summary>
    ///     设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    ///     整组点位发起采集毫秒时间戳
    /// </summary>
    public long ParentTime { get; set; }

    /// <summary>
    ///     属性
    /// </summary>
    public ConcurrentDictionary<string, ParamValue> Params { get; set; }
}

/// <summary>
/// </summary>
public class ParamValue
{
    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    ///     点位采集毫秒时间戳
    /// </summary>
    public long Time { get; set; }
}

/// <summary>
/// </summary>
public class ELinkDevSendEx : ELinkDevSendInput
{
}