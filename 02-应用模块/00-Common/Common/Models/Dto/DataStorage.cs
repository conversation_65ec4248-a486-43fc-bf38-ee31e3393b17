using IotPlatform.Core.Enum;

namespace Common.Models;

// 当value发生改变时触发事件
public delegate void ValueChangedEventHandler(string key, string newValue, string oldValue);

// 当时间发生改变时候触发事件
public delegate void TimeChangedEventHandler(string key, long newTime, long oldTime);

// 订阅值发生改变时候且满足条件触发事件
public delegate void CheckSubscriptionConditionEventHandler(string key, string newValue, long newTime);

// 当调用UpdateData时候触发事件
public delegate void DataUpdatedEventHandler(string key, string newValue, long newTime);

/// <summary>
///     实时数据存储
/// </summary>
public class DataStorage
{
    private readonly ConcurrentDictionary<string, PropertyStorage> data = new();
    public readonly ConcurrentDictionary<string, bool> deviceConnectStatus = new();
    private readonly ConcurrentDictionary<string, List<ValueChangedEventHandler>> valueSubscribers = new();
    private readonly ConcurrentDictionary<string, List<TimeChangedEventHandler>> timeSubscribers = new();
    private readonly ConcurrentDictionary<string, List<CheckSubscriptionConditionEventHandler>> checkConditionSubscribers = new();
    public event DataUpdatedEventHandler DataUpdated;


    private static readonly Lazy<DataStorage> instance = new(() => new DataStorage());
    public static DataStorage Instance => instance.Value;

    /// <summary>
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public PropertyStorage? GetData(string key)
    {
        data.TryGetValue(key, out PropertyStorage? property);
        return property;
    }

    /// <summary>
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="property"></param>
    /// <returns></returns>
    public PropertyStorage? GetData(string deviceName, string property)
    {
        string key = $"{deviceName}/{property}";
        return data.TryGetValue(key, out PropertyStorage? value) ? value : null;
    }

    /// <summary>
    ///     Add
    /// </summary>
    /// <param name="key"></param>
    /// <param name="property"></param>
    public void AddData(string key, PropertyStorage property)
    {
        data.TryAdd(key, property);
    }

    /// <summary>
    /// </summary>
    /// <param name="key"></param>
    /// <param name="value"></param>
    /// <param name="time"></param>
    /// <param name="dataType"></param>
    public void UpdateData(string key, object value, long time, WriteParamValueDataTypeEnum dataType = WriteParamValueDataTypeEnum.String)
    {
        //先更新在触发
        data[key] = new PropertyStorage
        {
            CookieValue = data.TryGetValue(key, out PropertyStorage? value1) ? value1?.Value : null,
            Value = value.ToString(),
            Time = time,
            DataType = dataType,
            Key = key
        };

        OnDataUpdated(key, value.ToString(), time);
    }

    /// <summary>
    /// </summary>
    /// <param name="deviceName"></param>
    /// <param name="propertyKey"></param>
    /// <param name="objValue"></param>
    /// <param name="time"></param>
    /// <param name="dataType"></param>
    public void UpdateData(string deviceName, string propertyKey, object objValue, long time, WriteParamValueDataTypeEnum dataType = WriteParamValueDataTypeEnum.String)
    {
        string value = objValue.ToString();
        string key = $"{deviceName}/{propertyKey}";
        if (!data.ContainsKey(key))
        {
            data.TryAdd(key, new PropertyStorage
            {
                Key = key,
                Value = value,
                Time = time,
                DataType = dataType,
            });
        }
        else
        {
            if (data.TryGetValue(key, out PropertyStorage? property))
            {
                property.CookieValue = property.Value;
                property.Value = value;
                property.Time = time;
                property.Key = key;
                property.DataType = dataType;
                OnDataUpdated(key, value, time);
            }
        }

        OnDataUpdated(key, value, time);
    }


    /// <summary>
    ///     移除参数
    /// </summary>
    /// <param name="key"></param>
    public void RemoveData(string key)
    {
        data.TryRemove(key, out _);
        valueSubscribers.TryRemove(key, out _);
    }

    /// <summary>
    ///     是否存在
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public bool ContainsData(string key)
    {
        return data.ContainsKey(key);
    }

    /// <summary>
    /// </summary>
    /// <param name="key"></param>
    /// <param name="handler"></param>
    public void SubscribeValueChangedEvent(string key, ValueChangedEventHandler handler)
    {
        if (!valueSubscribers.ContainsKey(key))
        {
            valueSubscribers.TryAdd(key, new List<ValueChangedEventHandler>());
        }

        valueSubscribers[key].Add(handler);
    }

    public void UnsubscribeValueChangedEvent(string key, ValueChangedEventHandler handler)
    {
        if (valueSubscribers.ContainsKey(key))
        {
            valueSubscribers[key].Remove(handler);
        }
    }

    public void SubscribeTimeChangedEvent(string key, TimeChangedEventHandler handler)
    {
        if (!timeSubscribers.ContainsKey(key))
        {
            timeSubscribers.TryAdd(key, new List<TimeChangedEventHandler>());
        }

        timeSubscribers[key].Add(handler);
    }

    public void UnsubscribeTimeChangedEvent(string key, TimeChangedEventHandler handler)
    {
        if (timeSubscribers.ContainsKey(key))
        {
            timeSubscribers[key].Remove(handler);
        }
    }

    public void SubscribeCheckSubscriptionConditionEvent(string key, CheckSubscriptionConditionEventHandler handler)
    {
        if (!checkConditionSubscribers.ContainsKey(key))
        {
            checkConditionSubscribers.TryAdd(key, new List<CheckSubscriptionConditionEventHandler>());
        }

        checkConditionSubscribers[key].Add(handler);
    }

    public void UnsubscribeCheckSubscriptionConditionEvent(string key, CheckSubscriptionConditionEventHandler handler)
    {
        if (checkConditionSubscribers.ContainsKey(key))
        {
            checkConditionSubscribers[key].Remove(handler);
        }
    }

    /// <summary>
    ///     调用UpdateData触发事件
    /// </summary>
    /// <param name="key"></param>
    /// <param name="newValue"></param>
    /// <param name="newTime"></param>
    private void OnDataUpdated(string key, string newValue, long newTime)
    {
        DataUpdated?.Invoke(key, newValue, newTime);
    }

    /// <summary>
    ///     实际存储数据
    /// </summary>
    public class PropertyStorage
    {
        /// <summary>
        ///     数据类型
        /// </summary>
        public WriteParamValueDataTypeEnum DataType { get; set; }

        private string? value;

        private long time;

        public string? Value
        {
            get => value;
            set
            {
                if (value != this.value)
                {
                    string? oldValue = this.value;
                    this.value = value;
                    NotifyValueChanged(Key, value, oldValue);
                }

                CheckSubscriptionCondition(Key, value, time);
            }
        }

        public string Key { get; set; } // Add a Key property to uniquely identify each data item

        // rest of the class...


        private void NotifyValueChanged(string key, string newValue, string oldValue)
        {
            if(key == null )
                return;
            List<ValueChangedEventHandler> handlers;
            if (Instance.valueSubscribers.TryGetValue(key, out handlers))
            {
                foreach (ValueChangedEventHandler handler in handlers)
                {
                    handler.Invoke(key, newValue, oldValue);
                }
            }
        }


        private void NotifyTimeChanged(string key, long newTime, long oldTime)
        {
            if(key == null)
                return;
            List<TimeChangedEventHandler> handlers;
            if (Instance.timeSubscribers.TryGetValue(key, out handlers))
            {
                foreach (TimeChangedEventHandler handler in handlers)
                {
                    handler.Invoke(key, newTime, oldTime);
                }
            }
        }

        private void CheckSubscriptionCondition(string key, string newValue, long newTime)
        {
            if(key == null )
                return;
            List<CheckSubscriptionConditionEventHandler> handlers;
            if (Instance.checkConditionSubscribers.TryGetValue(key, out handlers))
            {
                foreach (CheckSubscriptionConditionEventHandler handler in handlers)
                {
                    handler.Invoke(key, newValue, newTime);
                }
            }
        }

        /// <summary>
        ///     上一次的值
        /// </summary>
        public string? CookieValue { get; set; }

        public long Time
        {
            get => time;
            set
            {
                if (value != time)
                {
                    long oldTime = time;
                    time = value;
                    NotifyTimeChanged(Key, value, oldTime);
                }
            }
        }
    }
}