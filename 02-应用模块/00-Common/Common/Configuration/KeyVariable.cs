using Common.Options;
using Furion;

namespace Common.Configuration;

/// <summary>
/// Key常量.
/// </summary>
[SuppressSniffer]
public class KeyVariable
{
    private static readonly AppOptions _jnfp = App.GetConfig<AppOptions>("JNPF_App", true);

    /// <summary>
    /// 系统文件路径.
    /// </summary>
    public static string SystemPath
    {
        get
        {
            return  string.IsNullOrEmpty(_jnfp.SystemPath) ? Directory.GetCurrentDirectory() : _jnfp.SystemPath;
        }
    }

    /// <summary>
    /// 允许上传图片类型.
    /// </summary>
    public static List<string> AllowImageType
    {
        get
        {
            return string.IsNullOrEmpty(_jnfp.AllowUploadImageType.ToString()) ? new List<string>() : _jnfp.AllowUploadImageType;
        }
    }

    /// <summary>
    /// 允许上传文件类型.
    /// </summary>
    public static List<string> AllowUploadFileType
    {
        get
        {
            return string.IsNullOrEmpty(_jnfp.AllowUploadFileType.ToString()) ? new List<string>() : _jnfp.AllowUploadFileType;
        }
    }

    /// <summary>
    /// 过滤上传文件名称特殊字符.
    /// </summary>
    public static List<string> SpecialString
    {
        get
        {
            return string.IsNullOrEmpty(_jnfp.SpecialString.ToString()) ? new List<string>() : _jnfp.SpecialString;
        }
    }

    /// <summary>
    /// 文件储存类型.
    /// </summary>
    public static string AppUpdateContent
    {
        get
        {
            return string.IsNullOrEmpty(App.Configuration["JNPF_APP:AppUpdateContent"]) ? string.Empty : App.Configuration["JNPF_APP:AppUpdateContent"];
        }
    }
}