

namespace IotPlatform.VisualData.Entity;

/// <summary>
///     大屏基本信息
/// </summary>
[SugarTable("business_visual")]
public class Visual : EntityTenant
{
    /// <summary>
    ///     大屏标题
    /// </summary>
    [SugarColumn(ColumnDescription = "大屏标题")]
    public string Title { get; set; }

    /// <summary>
    ///     大屏背景
    /// </summary>
    [SugarColumn(ColumnDescription = "大屏背景",IsNullable = true)]
    public string? BackgroundUrl { get; set; }

    /// <summary>
    ///     大屏类型
    /// </summary>
    [SugarColumn(ColumnDescription = "大屏类型")]
    public long Category { get; set; }

    /// <summary>
    ///     发布密码
    /// </summary>
    [SugarColumn(ColumnDescription = "发布密码",IsNullable = true)]
    public string? Password { get; set; }

    /// <summary>
    ///     业务状态
    /// </summary>
    [SugarColumn(ColumnDescription = "状态")]
    public int Status { get; set; }
    
    /// <summary>
    ///     外连配置
    /// </summary>
    [SugarColumn(ColumnDescription = "外连配置",IsJson = true,IsNullable = true)]
    public VisualLinkModel? VisualLink { get; set; }
}

/// <summary>
/// 大屏基本信息-外联配置
/// </summary>
public class VisualLinkModel
{
    /// <summary>
    ///  来源
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string Url { get; set; }
}

/// <summary>
///     大屏图片枚举
/// </summary>
public enum ScreenImgEnum
{
    /// <summary>
    ///     背景图片
    /// </summary>
    [Description("bg")] BG = 0,

    /// <summary>
    ///     图片框
    /// </summary>
    [Description("border")] BORDER = 1,

    /// <summary>
    ///     图片
    /// </summary>
    [Description("source")] SOURCE = 1,

    /// <summary>
    ///     banner
    /// </summary>
    [Description("banner")] BANNER = 3,

    /// <summary>
    ///     大屏截图
    /// </summary>
    [Description("screenShot")] SCREENSHOT = 4
}