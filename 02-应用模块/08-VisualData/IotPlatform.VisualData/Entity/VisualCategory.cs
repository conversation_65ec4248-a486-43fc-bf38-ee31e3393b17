namespace IotPlatform.VisualData.Entity;

/// <summary>
///     可视化分类表
/// </summary>
[SugarTable("business_visualCategory", "可视化分类表")]
public class VisualCategory : EntityTenantId
{
    /// <summary>
    ///     分类键值
    /// </summary>
    [SugarColumn(ColumnDescription = "分类键值")]
    public string CategoryKey { get; set; }

    /// <summary>
    ///     分类名称
    /// </summary>
    [SugarColumn(ColumnDescription = "分类名称")]
    public string CategoryValue { get; set; }
}