using IotPlatform.Core.Enum;

namespace IotPlatform.VisualData.Services;

/// <summary>
///     可视化管理-大屏设计
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-05
/// </summary>
[ApiDescriptionSettings("可视化管理")]
public class ScreenCategoryService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<VisualCategory> _visualCategoryRepository;

    /// <summary>
    ///     初始化一个<see cref="ScreenCategoryService" />类型的新实例
    /// </summary>
    public ScreenCategoryService(ISqlSugarRepository<VisualCategory> visualCategoryRepository)
    {
        _visualCategoryRepository = visualCategoryRepository;
    }

    #region Get

    /// <summary>
    ///     获取大屏分类分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/blade-visual/category/page")]
    public async Task<SqlSugarPagedList<ScreenCategoryListOutput>> GetPagetList(
        [FromQuery] ScreenCategoryListQueryInput input)
    {
        SqlSugarPagedList<ScreenCategoryListOutput>? data = await _visualCategoryRepository.AsQueryable()
            .Select<ScreenCategoryListOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return data;
    }

    /// <summary>
    ///     获取大屏分类列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/blade-visual/category/list")]
    public async Task<SqlSugarPagedList<ScreenCategoryListOutput>> GetList(
        [FromQuery] ScreenCategoryListQueryInput input)
    {
        SqlSugarPagedList<ScreenCategoryListOutput>? list = await _visualCategoryRepository.AsQueryable()
            .Select<ScreenCategoryListOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return list;
    }

    /// <summary>
    ///     详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/blade-visual/category/detail")]
    public async Task<ScreenCategoryInfoOutput> GetInfo([FromQuery] BaseId input)
    {
        VisualCategory? entity = await _visualCategoryRepository.GetSingleAsync(v => v.Id == input.Id);
        ScreenCategoryInfoOutput data = entity.Adapt<ScreenCategoryInfoOutput>();
        return data;
    }

    #endregion

    #region Post

    /// <summary>
    ///     新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/blade-visual/category/add")]
    public async Task Create(ScreenCategoryCrInput input)
    {
        bool isExist =
            await _visualCategoryRepository.IsAnyAsync(v => v.CategoryValue == input.CategoryValue);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        VisualCategory entity = input.Adapt<VisualCategory>();
        entity.Id = YitIdHelper.NextId();
        int isOk = await _visualCategoryRepository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (!(isOk > 0))
        {
            throw Oops.Oh(ErrorCode.COM1000);
        }
    }

    /// <summary>
    ///     修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/blade-visual/category/update")]
    public async Task Update(ScreenCategoryUpInput input)
    {
        bool isExist = await _visualCategoryRepository.IsAnyAsync(v => v.CategoryValue == input.CategoryValue && v.Id != input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        VisualCategory entity = input.Adapt<VisualCategory>();
        int isOk = await _visualCategoryRepository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (!(isOk > 0))
        {
            throw Oops.Oh(ErrorCode.COM1001);
        }
    }

    /// <summary>
    ///     删除
    /// </summary>
    /// <returns></returns>
    [HttpPost("/blade-visual/category/delete")]
    public async Task Delete(BaseId<string> input)
    {
        string[] ids = input.Id.Split(",").ToArray();
        List<VisualCategory>? entity = await _visualCategoryRepository.AsQueryable().In(v => v.Id, ids).ToListAsync();
        _ = entity ?? throw Oops.Oh(ErrorCode.D1002);
        bool isOk = await _visualCategoryRepository.DeleteAsync(entity);
        if (!isOk)
        {
            throw Oops.Oh(ErrorCode.COM1002);
        }
    }

    #endregion
}