namespace IotPlatform.VisualData.Services.Dto;

/// <summary>
///     大屏修改输入
/// </summary>
public class ScreenUpInput
{
    /// <summary>
    /// </summary>
    public ScreenConfigUpInput Config { get; set; }

    /// <summary>
    /// </summary>
    public ScreenEntityUpInput Visual { get; set; }
}

/// <summary>
///     大屏实体修改输入
/// </summary>
public class ScreenEntityUpInput : ScreenEntityCrInput
{
    /// <summary>
    ///     主键
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     业务状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    ///     背景图片
    /// </summary>
    public string BackgroundUrl { get; set; }
}