using System.Collections.Generic;
using System.Threading.Tasks;
using Common.Dto.VisualDev;
using Common.Models;
using Engine.Entity.Model;
using Extras.DatabaseAccessor.SqlSugar.Internal;
using Systems.Entity;
using VisualDev.Entity;
using VisualDev.Entity.Dto.VisualDevModelData;

namespace VisualDev.Interface;

/// <summary>
/// 在线开发运行服务接口.
/// </summary>
public interface IRunService
{
    /// <summary>
    /// 创建在线开发功能.
    /// </summary>
    /// <param name="templateEntity">功能模板实体.</param>
    /// <param name="dataInput">数据输入.</param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    Task<string> Create(VisualDevEntity templateEntity, VisualDevModelDataCrInput dataInput, string? tenantId = null);

    /// <summary>
    /// 创建在线开发有表SQL.
    /// </summary>
    /// <param name="templateEntity"></param>
    /// <param name="dataInput"></param>
    /// <param name="mainId"></param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    Task<Dictionary<string, List<Dictionary<string, object>>>> CreateHaveTableSql(VisualDevEntity templateEntity, VisualDevModelDataCrInput dataInput, string mainId, string? tenantId = null);

    /// <summary>
    /// 修改在线开发功能.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="templateEntity"></param>
    /// <param name="visualdevModelDataUpForm"></param>
    /// <returns></returns>
    Task Update(string id, VisualDevEntity templateEntity, VisualDevModelDataUpInput visualdevModelDataUpForm);

    /// <summary>
    /// 批量修改在线开发功能（集成助手用）.
    /// </summary>
    /// <param name="ids"></param>
    /// <param name="templateEntity"></param>
    /// <param name="visualdevModelDataUpForm"></param>
    /// <returns></returns>
    Task BatchUpdate(List<string>? ids, VisualDevEntity templateEntity, VisualDevModelDataUpInput visualdevModelDataUpForm);

    /// <summary>
    /// 修改在线开发有表sql.
    /// </summary>
    /// <param name="templateEntity"></param>
    /// <param name="dataInput"></param>
    /// <param name="mainId"></param>
    /// <returns></returns>
    Task<List<string>> UpdateHaveTableSql(VisualDevEntity templateEntity, VisualDevModelDataUpInput dataInput, string mainId);

    /// <summary>
    /// 删除有表信息.
    /// </summary>
    /// <param name="id">主键.</param>
    /// <param name="templateEntity">模板实体.</param>
    /// <returns></returns>
    Task DelHaveTableInfo(string id, VisualDevEntity templateEntity);

    /// <summary>
    /// 删除集成助手数据.
    /// </summary>
    /// <param name="templateEntity">模板实体.</param>
    /// <returns></returns>
    Task DelInteAssistant(VisualDevEntity templateEntity);

    /// <summary>
    /// 批量删除有表数据.
    /// </summary>
    /// <param name="ids">id数组.</param>
    /// <param name="templateEntity">模板实体.</param>
    /// <param name="visualdevModelDataDeForm"></param>
    /// <returns></returns>
    Task BatchDelHaveTableData(List<string> ids, VisualDevEntity templateEntity, VisualDevModelDataBatchDelInput? visualdevModelDataDeForm = null);

    /// <summary>
    /// 列表数据处理.
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="input"></param>
    /// <param name="actionType"></param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    Task<SqlSugarPagedList<Dictionary<string, object>>> GetListResult(VisualDevEntity entity, VisualDevModelListQueryInput input, string actionType = "List", string? tenantId = null);

    /// <summary>
    /// 关联表单列表数据处理.
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="input"></param>
    /// <param name="actionType"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<Dictionary<string, object>>> GetRelationFormList(VisualDevEntity entity, VisualDevModelListQueryInput input, string actionType = "List",DataFilteringModel ? dataFilteringModel = null);

    /// <summary>
    /// 获取有表详情.
    /// </summary>
    /// <param name="id">主键.</param>
    /// <param name="templateEntity">模板实体.</param>
    /// <param name="isInteAssis">是否为集成助手.</param>
    /// <returns></returns>
    Task<Dictionary<string, object>> GetHaveTableInfo(string id, VisualDevEntity templateEntity, bool isInteAssis = false);

    /// <summary>
    /// 获取有表详情转换.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="templateEntity"></param>
    /// <param name="isFlowTask"></param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    Task<string> GetHaveTableInfoDetails(string id, VisualDevEntity templateEntity, bool isFlowTask = false, string? tenantId = null);

    /// <summary>
    /// 生成系统自动生成字段.
    /// </summary>
    /// <param name="fieldsModelListJson">模板数据.</param>
    /// <param name="allDataMap">真实数据.</param>
    /// <param name="IsCreate">创建与修改标识 true创建 false 修改.</param>
    /// <param name="systemControlList">不赋值的系统控件Key.</param>
    /// <returns></returns>
    Task<Dictionary<string, object>> GenerateFeilds(string fieldsModelListJson, Dictionary<string, object> allDataMap, bool IsCreate, List<string>? systemControlList = null);

    /// <summary>
    /// 获取数据库连接,根据linkId.
    /// </summary>
    /// <param name="linkId">数据库连接Id.</param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    Task<DbLink> GetDbLink(long linkId, string? tenantId = null);

}
