using VisualDev.Entity.Dto.Portal;
using DateTime = System.DateTime;

namespace VisualDev;

/// <summary>
///     业务实现：门户设计.
/// </summary>
[ApiDescriptionSettings("功能设计", Tag = "Portal", Order = 173)]
[Route("/portal/")]
public class PortalService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<PortalEntity> _repository;

    /// <summary>
    ///     用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    ///     文件服务.
    /// </summary>
    private readonly IFileManager _fileManager;

    /// <summary>
    ///     初始化一个<see cref="PortalService" />类型的新实例.
    /// </summary>
    public PortalService(
        ISqlSugarRepository<PortalEntity> repository,
        IUserManager userManager,
        IFileManager fileManager)
    {
        _repository = repository;
        _userManager = userManager;
        _fileManager = fileManager;
    }

    #region Get

    /// <summary>
    ///     列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns>返回列表.</returns>
    [HttpGet("list")]
    public async Task<dynamic> GetList([FromQuery] PortalListQueryInput input)
    {
        var systemAppList = await _repository.AsSugarClient().Queryable<SysApp>().ToListAsync();

        SqlSugarPagedList<PortalListOutput> data = await _repository.AsQueryable()
            .WhereIF(input.keyword.IsNotEmptyOrNull(), p => p.FullName.Contains(input.keyword) || p.EnCode.Contains(input.keyword))
            .WhereIF(input.category > 0, p => p.Category == input.category)
            .WhereIF(input.enabledLock.IsNotEmptyOrNull(), p => p.EnabledLock == input.enabledLock)
            .WhereIF(input.isRelease.IsNotEmptyOrNull(), p => p.State == input.isRelease)
            .OrderBy(p => p.SortCode)
            .OrderBy(p => p.CreatedTime, OrderByType.Desc)
            .OrderByIF(!input.keyword.IsNullOrEmpty(), p => p.UpdatedTime, OrderByType.Desc)
            .Select(p => new PortalListOutput
            {
                id = p.Id,
                fullName = p.FullName,
                enCode = p.EnCode,
                // category = SqlFunc.Subqueryable<DictionaryDataEntity>().EnableTableFilter().Where(it => it.Id.Equals(p.Category)).Select(it => it.FullName),
                creatorTime = p.CreatedTime,
                creatorUser = SqlFunc.Subqueryable<SysUser>().EnableTableFilter().Where(it => it.Id == p.CreatedUserId).Select(it => it.Name),
                lastModifyTime = p.UpdatedTime,
                state = p.State,
                isRelease = p.State,
                enabledLock = p.EnabledLock,
                sortCode = p.SortCode,
                platformRelease = p.PlatformRelease,
                pcPortalIsRelease = SqlFunc.Subqueryable<PortalManageEntity>().EnableTableFilter()
                    .Where(it => systemAppList.Select(s => s.AppId).Contains(it.SystemId) && it.Platform.Equals("Web") && it.PortalId.Equals(p.Id)).Any()
                    ? 1
                    : 0
            })
            .ToPagedListAsync(input.currentPage, input.pageSize);

        List<PortalManageEntity>? portalManageList = await _repository.AsSugarClient().Queryable<PortalManageEntity>().ToListAsync();
        foreach (PortalListOutput? item in data.Rows)
        {
            List<string> pcPortalList = new List<string>();
            foreach (PortalManageEntity? pm in portalManageList.Where(it => it.Platform.Equals("Web") && it.PortalId.Equals(item.id)))
            {
                var sys = systemAppList.Find(it => it.Id.Equals(pm.SystemId));
                if (sys.IsNotEmptyOrNull())
                {
                    pcPortalList.Add(sys.MultiName.Zh);
                }
            }

            item.pcPortalReleaseName = string.Join("；", pcPortalList);
        }

        return data;
    }

    /// <summary>
    ///     获取门户侧边框列表.
    /// </summary>
    /// <returns></returns>
    [HttpGet("Selector")]
    public async Task<dynamic> GetSelector([FromQuery] PortalSelectInput input)
    {
        List<PortalSelectOutput>? data = new();

        // 侧边栏需要系统id过滤
        if (input.type.IsNotEmptyOrNull() && input.type.Equals(1))
        {
            // var sysId = string.Empty;
            // if (_userManager.UserOrigin.Equals("pc"))
            // {
            //     var userOnline = allUserOnlineList.Find(it => it.token.Equals(_userManager.ToKen));
            //     sysId = userOnline.IsNotEmptyOrNull() ? userOnline.systemId : _userManager.User.SystemId;
            // }
            // else
            // {
            //     sysId = _userManager.User.AppSystemId;
            // }

            // if (!_userManager.IsAdministrator)
            // {
            //     var permissionGroupIds = await _repository.AsSugarClient().Queryable<PermissionGroupEntity>()
            //         .In(it => it.Id, _userManager.PermissionGroup).Where(it => it.EnabledMark == 1 && it.DeleteMark == null).Select(it => it.Id).ToListAsync();
            //     var items = await _repository.AsSugarClient().Queryable<AuthorizeEntity>().Where(a => permissionGroupIds.Contains(a.ObjectId) && a.ItemType == "portalManage").GroupBy(it => it.ItemId).Select(it => it.ItemId).ToListAsync();
            //     if (items.Any())
            //     {
            //         data = await _repository.AsSugarClient().Queryable<PortalEntity, PortalManageEntity>((p, pm) => new JoinQueryInfos(JoinType.Left, p.Id == pm.PortalId))
            //             .In((p, pm) => pm.Id, items.ToArray())
            //             .Where((p, pm) => p.EnabledMark == 1 && pm.EnabledMark == 1 )
            //             .Where((p, pm) => pm.Platform.Equals(input.platform))
            //             // .Where((p, pm) => pm.SystemId.Equals(sysId))
            //             .OrderBy((p, pm) => pm.CreatedTime, OrderByType.Desc)
            //             .OrderBy((p, pm) => pm.UpdatedTime, OrderByType.Desc)
            //             .Select(p => new PortalSelectOutput
            //             {
            //                 id = p.Id.ToString(),
            //                 fullName = p.FullName,
            //                 parentId = p.Category
            //             }).ToListAsync();
            //     }
            // }
            // else
            // {
            data = await _repository.AsSugarClient().Queryable<PortalEntity, PortalManageEntity>((p, pm) => new JoinQueryInfos(JoinType.Left, p.Id == pm.PortalId))
                .Where((p, pm) => p.EnabledMark == 1 && pm.EnabledMark == 1)
                .Where((p, pm) => pm.Platform.Equals(input.platform))
                // .Where((p, pm) => pm.SystemId.Equals(sysId))
                .OrderBy((p, pm) => pm.CreatedTime, OrderByType.Desc)
                .OrderBy((p, pm) => pm.UpdatedTime, OrderByType.Desc)
                .Select(p => new PortalSelectOutput
                {
                    id = p.Id.ToString(),
                    fullName = p.FullName,
                    parentId = p.Category.ToString()
                }).ToListAsync();
            // }
        }
        else
        {
            data = await _repository.AsQueryable()
                .Where(it => it.EnabledMark == 1)
                .OrderBy(it => it.SortCode)
                .OrderBy(it => it.CreatedTime, OrderByType.Desc)
                .OrderBy(it => it.UpdatedTime, OrderByType.Desc)
                .Select(it => new PortalSelectOutput
                {
                    id = it.Id.ToString(),
                    fullName = it.FullName,
                    parentId = it.Category.ToString()
                })
                .ToListAsync();
        }

        List<string>? parentIds = data.Select(it => it.parentId).Distinct().ToList();
        List<PortalSelectOutput>? treeList = new();
        if (parentIds.Any())
        {
            treeList = await _repository.AsSugarClient().Queryable<PortalCategory>().In(it => it.Id, parentIds.ToArray())
                .OrderBy(o => o.CreatedTime, OrderByType.Desc)
                .Select(d => new PortalSelectOutput
                {
                    id = d.Id.ToString(),
                    parentId = "0",
                    fullName = d.Name
                }).ToListAsync();
        }

        return new { list = treeList.Union(data).ToList().ToTree() };
    }

    // /// <summary>
    // /// 门户获取系统下拉.
    // /// </summary>
    // /// <param name="id"></param>
    // /// <param name="category"></param>
    // /// <returns></returns>
    // [HttpGet("systemFilter/{id}")]
    // public async Task<dynamic> GetSystemFilter(string id, string category)
    // {
    //     var pmSysemIdList = await _repository.AsSugarClient().Queryable<PortalManageEntity>().Where(it =>  it.Platform.Equals(category) && it.PortalId.Equals(id)).Select(it => it.SystemId).ToListAsync();
    //     var systemList = (await _repository.AsSugarClient().Queryable<SystemEntity>()
    //         .Where(it => it.DeleteMark == null && !it.EnCode.Equals("mainSystem"))
    //         .OrderBy(it => it.SortCode)
    //         .OrderByDescending(it => it.CreatedTime)
    //         .ToListAsync())
    //         .Adapt<List<PortalSystemFilterOutput>>();
    //
    //     foreach (var item in systemList)
    //     {
    //         if (pmSysemIdList.Contains(item.id))
    //             item.disabled = true;
    //     }
    //
    //     return new { list = systemList };
    // }

    /// <summary>
    ///     获取门户信息.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpGet("{id}/detail")]
    public async Task<dynamic> GetInfo(long id)
    {
        List<SysApp>? systemList = await _repository.AsSugarClient().Queryable<SysApp>().ToListAsync();

        PortalInfoOutput? data = await _repository.AsQueryable()
            .Where(it => it.Id == id)
            .Select(it => new PortalInfoOutput
            {
                id = it.Id,
                category = it.Category,
                description = it.Description,
                enCode = it.EnCode,
                enabledLock = it.EnabledLock,
                fullName = it.FullName,
                sortCode = it.SortCode,
                formData = SqlFunc.Subqueryable<PortalDataEntity>().EnableTableFilter().Where(x => x.Type.Equals("model") && x.PortalId.Equals(id)).Select(x => x.FormData),
                platformRelease = it.PlatformRelease,
                pcPortalIsRelease = SqlFunc.Subqueryable<PortalManageEntity>().EnableTableFilter()
                    .Where(x => systemList.Select(s => s.AppId).Contains(x.SystemId) && x.Platform.Equals("Web") && x.PortalId.Equals(it.Id)).Any()
                    ? 1
                    : 0
            })
            .FirstAsync();

        List<PortalManageEntity>? portalManageList = await _repository.AsSugarClient().Queryable<PortalManageEntity>().ToListAsync();
        List<string> pcPortalList = new List<string>();
        foreach (PortalManageEntity? pm in portalManageList.Where(it => it.Platform.Equals("Web") && it.PortalId.Equals(data.id)))
        {
            SysApp? sys = systemList.Find(it => it.Id.Equals(pm.SystemId));
            if (sys.IsNotEmptyOrNull())
            {
                pcPortalList.Add(sys.MultiName.Zh);
            }
        }

        data.pcPortalReleaseName = string.Join("；", pcPortalList);

        return data;
    }

    // /// <summary>
    // /// 信息.
    // /// </summary>
    // /// <param name="id"></param>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [HttpGet("{id}/auth")]
    // public async Task<dynamic> GetInfoAuth(string id, [FromQuery] PortalAuthInput input)
    // {
    //     // var systemId = input.platform.Equals("App") ? _userManager.User.AppSystemId : _userManager.User.SystemId;
    //
    //     if (!await _repository.AsQueryable().AnyAsync(it => it.Id.Equals(id)))
    //         throw Oops.Oh(ErrorCode.D1919);
    //
    //     var entity = new PortalInfoAuthOutput();
    //     if (_userManager.Roles != null && !_userManager.IsAdministrator)
    //     {
    //         var permissionGroupIds = await _repository.AsSugarClient().Queryable<PermissionGroupEntity>()
    //             .Where(it => _userManager.PermissionGroup.Contains(it.Id))
    //             .Where(it => it.EnabledMark == 1 && it.DeleteMark == null)
    //             .Select(it => it.Id)
    //             .ToListAsync();
    //         var items = await _repository.AsSugarClient().Queryable<AuthorizeEntity>()
    //             .Where(a => permissionGroupIds.Contains(a.ObjectId))
    //             .Where(a => a.ItemType == "portalManage")
    //             .GroupBy(it => it.ItemId)
    //             .Select(it => it.ItemId)
    //             .ToListAsync();
    //         if (items.Count == 0) return null;
    //
    //         var portalIdList = await _repository.AsSugarClient().Queryable<PortalManageEntity>()
    //             .Where(it => it.DeleteMark == null && it.EnabledMark == 1 && items.Contains(it.Id) && it.SystemId.Equals(systemId))
    //             .Select(it => it.PortalId)
    //             .ToListAsync();
    //         if (portalIdList.Contains(id))
    //         {
    //             // 判断子门户是否存在
    //             if (!await _repository.AsSugarClient().Queryable<PortalDataEntity>().AnyAsync(it => it.DeleteMark == null && it.Platform.Equals(input.platform) && it.Type.Equals("custom") && it.SystemId.Equals(systemId) && it.PortalId.Equals(id) && it.CreatedUserId.Equals(_userManager.UserId)))
    //             {
    //                 var data = await _repository.AsSugarClient().Queryable<PortalDataEntity>()
    //                     .Where(it => it.DeleteMark == null && it.Type.Equals("release") && it.PortalId.Equals(id) && it.SystemId == null)
    //                     .FirstAsync();
    //                 if (data.IsNotEmptyOrNull())
    //                 {
    //                     data.Id = YitIdHelper.NextId();
    //                     data.Type = "custom";
    //                     data.Platform = input.platform;
    //                     data.SystemId = systemId;
    //                     data.CreatedTime = DateTime.Now;
    //                     data.CreatedUserId = _userManager.UserId;
    //                     await _repository.AsSugarClient().Insertable(data).ExecuteCommandAsync();
    //                 }
    //             }
    //
    //             entity = await _repository.AsQueryable()
    //                 .Where(it => it.DeleteMark == null && it.EnabledMark == 1 && it.Id.Equals(id))
    //                 .Select(it => new PortalInfoAuthOutput
    //                 {
    //                     type = it.Type,
    //                     customUrl = it.CustomUrl,
    //                     linkType = it.LinkType,
    //                     enabledLock = it.EnabledLock,
    //                     formData = SqlFunc.Subqueryable<PortalDataEntity>().EnableTableFilter().Where(x => x.DeleteMark == null && x.CreatedUserId.Equals(_userManager.UserId) && x.Platform.Equals(input.platform) && x.Type.Equals("custom") && x.PortalId.Equals(id) && x.SystemId.Equals(systemId)).Select(x => x.FormData)
    //                 })
    //                 .FirstAsync();
    //         }
    //     }
    //     else if (_userManager.IsAdministrator)
    //     {
    //         // 判断子门户是否存在
    //         if (!await _repository.AsSugarClient().Queryable<PortalDataEntity>().AnyAsync(it => it.DeleteMark == null && it.Platform.Equals(input.platform) && it.Type.Equals("custom") && it.SystemId.Equals(systemId) && it.PortalId.Equals(id) && it.CreatedUserId.Equals(_userManager.UserId)))
    //         {
    //             var data = await _repository.AsSugarClient().Queryable<PortalDataEntity>()
    //                 .Where(it => it.DeleteMark == null && it.Type.Equals("release") && it.PortalId.Equals(id) && it.SystemId == null)
    //                 .FirstAsync();
    //             if (data.IsNotEmptyOrNull())
    //             {
    //                 data.Id = YitIdHelper.NextId();
    //                 data.Type = "custom";
    //                 data.Platform = input.platform;
    //                 data.SystemId = systemId;
    //                 data.CreatedTime = DateTime.Now;
    //                 data.CreatedUserId = _userManager.UserId;
    //                 await _repository.AsSugarClient().Insertable(data).ExecuteCommandAsync();
    //             }
    //         }
    //
    //         entity = await _repository.AsQueryable()
    //             .Where(it => it.DeleteMark == null && it.EnabledMark == 1 && it.Id.Equals(id))
    //             .Select(it => new PortalInfoAuthOutput
    //             {
    //                 type = it.Type,
    //                 customUrl = it.CustomUrl,
    //                 linkType = it.LinkType,
    //                 enabledLock = it.EnabledLock,
    //                 formData = SqlFunc.Subqueryable<PortalDataEntity>().EnableTableFilter().Where(x => x.DeleteMark == null && x.CreatedUserId.Equals(_userManager.UserId) && x.Platform.Equals(input.platform) && x.Type.Equals("custom") && x.PortalId.Equals(id) && x.SystemId.Equals(systemId)).Select(x => x.FormData)
    //             })
    //             .FirstAsync();
    //     }
    //     return entity;
    // }

    /// <summary>
    ///     门户选择.
    /// </summary>
    /// <param name="systemId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("manage/Selector/{systemId}")]
    public async Task<dynamic> GetManageSelector(string systemId, [FromQuery] PortalManageInput input)
    {
        // 系统已添加的门户
        List<long>? portalManageIdList = await _repository.AsSugarClient().Queryable<PortalManageEntity>()
            .Where(it => it.SystemId.Equals(systemId) && it.Platform.Equals(input.platform))
            .Select(it => it.PortalId)
            .ToListAsync();

        SqlSugarPagedList<PortalManageOutput> portalList = await _repository.AsSugarClient().Queryable<PortalEntity, PortalCategory>((p, d) => new JoinQueryInfos(JoinType.Left, p.Category == d.Id))
            .Where(p => p.EnabledMark == 1 && !portalManageIdList.Contains(p.Id))
            .WhereIF(!string.IsNullOrEmpty(input.keyword), p => p.FullName.Contains(input.keyword) || p.EnCode.Contains(input.keyword))
            .OrderBy(p => p.SortCode)
            .OrderBy(p => p.CreatedTime, OrderByType.Desc)
            .OrderBy(p => p.UpdatedTime, OrderByType.Desc)
            .Select((p, d) => new PortalManageOutput
            {
                id = p.Id,
                fullName = p.FullName,
                enCode = p.EnCode,
                sortCode = p.SortCode,
                categoryName = d.Name,
                category = p.Category
            })
            .ToPagedListAsync(input.currentPage, input.pageSize);

        return portalList;
    }

    #endregion

    #region Post

    /// <summary>
    ///     门户导出.
    /// </summary>
    /// <param name="modelId"></param>
    /// <returns></returns>
    [HttpPost("{modelId}/Actions/Export")]
    public async Task<dynamic> ActionsExportData(long modelId)
    {
        // 模板实体
        PortalExportOutput? templateEntity = await _repository.AsQueryable()
            .Where(it => it.Id == modelId)
            .Select<PortalExportOutput>()
            .FirstAsync();
        templateEntity.formData = await _repository.AsSugarClient().Queryable<PortalDataEntity>()
            .Where(it => it.PortalId.Equals(modelId) && it.Type.Equals("model"))
            .Select(it => it.FormData)
            .FirstAsync();

        string? jsonStr = templateEntity.ToJson();
        return jsonStr;
    }

    /// <summary>
    ///     门户导入.
    /// </summary>
    /// <param name="file"></param>
    /// <param name="type">识别重复（0：跳过，1：追加）.</param>
    /// <returns></returns>
    [HttpPost("Actions/Import")]
    public async Task ActionsImportData(IFormFile file, int type)
    {
        string? fileType = Path.GetExtension(file.FileName).Replace(".", string.Empty);
        if (!fileType.ToLower().Equals(ExportFileType.json.ToString()))
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        string? josn = _fileManager.Import(file);
        PortalExportOutput? templateEntity;
        try
        {
            templateEntity = josn.ToObjectOld<PortalExportOutput>();
        }
        catch
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        if (templateEntity == null)
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        if (templateEntity != null && templateEntity.formData.IsNotEmptyOrNull() && templateEntity.formData.IndexOf("layoutId") <= 0)
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        List<string> errorMsgList = new List<string>();
        List<string> errorList = new List<string>();
        if (await _repository.AsQueryable().AnyAsync(it => it.Id.Equals(templateEntity.id)))
        {
            errorList.Add("ID");
        }

        if (await _repository.AsQueryable().AnyAsync(it => it.EnCode.Equals(templateEntity.enCode)))
        {
            errorList.Add("编码");
        }

        if (await _repository.AsQueryable().AnyAsync(it => it.FullName.Equals(templateEntity.fullName)))
        {
            errorList.Add("名称");
        }

        if (errorList.Any())
        {
            if (type.Equals(0))
            {
                string error = string.Join("、", errorList);
                errorMsgList.Add(string.Format("{0}重复", error));
            }
            else
            {
                string? random = new Random().NextLetterAndNumberString(5);
                templateEntity.id = YitIdHelper.NextId();
                templateEntity.fullName = string.Format("{0}.副本{1}", templateEntity.fullName, random);
                templateEntity.enCode += random;
            }
        }

        if (errorMsgList.Any() && type.Equals(0))
        {
            throw Oops.Oh(ErrorCode.COM1018, string.Join(";", errorMsgList));
        }

        PortalEntity portalEntity = templateEntity.Adapt<PortalEntity>();
        portalEntity.State = 0;
        portalEntity.EnabledMark = 0;
        portalEntity.CreatedTime = DateTime.Now;
        portalEntity.CreatedUserId = _userManager.UserId;
        try
        {
            StorageableResult<PortalEntity>? storModuleModel = _repository.AsSugarClient().Storageable(portalEntity).WhereColumns(it => it.Id).Saveable().ToStorage(); // 存在更新不存在插入 根据主键
            await storModuleModel.AsInsertable.ExecuteCommandAsync(); // 执行插入
            await storModuleModel.AsUpdateable.ExecuteCommandAsync(); // 执行更新
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ErrorCode.COM1020, ex.Message);
        }

        // 门户数据
        PortalDataEntity dataEntity = new PortalDataEntity
        {
            Id = YitIdHelper.NextId(),
            PortalId = portalEntity.Id,
            FormData = templateEntity.formData,
            Type = "model",
            CreatedTime = DateTime.Now,
            CreatedUserId = _userManager.UserId
        };
        try
        {
            await _repository.AsSugarClient().Insertable(dataEntity).ExecuteCommandAsync();
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ErrorCode.COM1020, ex.Message);
        }
    }

    /// <summary>
    ///     新建门户信息.
    /// </summary>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("add")]
    public async Task<long> Create([FromBody] PortalCrInput input)
    {
        PortalEntity entity = input.Adapt<PortalEntity>();
        entity.Id = YitIdHelper.NextId();
        entity.State = 0;
        entity.EnabledMark = 0;
        entity.CreatedTime = DateTime.Now;
        entity.CreatedUserId = _userManager.UserId;

        if (entity.Category <= 0)
        {
            throw Oops.Oh(ErrorCode.D1901);
        }

        if (string.IsNullOrEmpty(entity.FullName))
        {
            throw Oops.Oh(ErrorCode.D1902);
        }

        if (string.IsNullOrEmpty(entity.EnCode))
        {
            throw Oops.Oh(ErrorCode.D1903);
        }

        if (await _repository.AsQueryable().Where(it => it.FullName.Equals(input.fullName)).AnyAsync())
        {
            throw Oops.Oh(ErrorCode.D1915);
        }

        if (await _repository.AsQueryable().Where(it => it.EnCode.Equals(input.enCode)).AnyAsync())
        {
            throw Oops.Oh(ErrorCode.D1916);
        }

        await _repository.AsInsertable(entity).ExecuteCommandAsync();

        PortalDataEntity dataEntity = new PortalDataEntity
        {
            Id = YitIdHelper.NextId(),
            PortalId = entity.Id,
            FormData = input.formData,
            CreatedTime = DateTime.Now,
            CreatedUserId = _userManager.UserId,
            Type = "model"
        };
        int isOk = await _repository.AsSugarClient().Insertable(dataEntity).ExecuteCommandAsync();
        if (!(isOk > 0))
        {
            throw Oops.Oh(ErrorCode.COM1000);
        }

        // 确定并设计
        return entity.Id;
    }

    /// <summary>
    ///     修改门户信息.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPut("{id}/update")]
    public async Task Update(string id, [FromBody] PortalUpInput input)
    {
        PortalEntity entity = input.Adapt<PortalEntity>();
        if (await _repository.AsQueryable().Where(it => !it.Id.Equals(id) && it.FullName.Equals(input.fullName)).AnyAsync())
        {
            throw Oops.Oh(ErrorCode.D1915);
        }

        if (await _repository.AsQueryable().Where(it => !it.Id.Equals(id) && it.EnCode.Equals(input.enCode)).AnyAsync())
        {
            throw Oops.Oh(ErrorCode.D1916);
        }

        int? state = await _repository.AsQueryable()
            .Where(it => it.Id.Equals(id))
            .Select(it => it.State)
            .FirstAsync();
        if (state.Equals(1))
        {
            entity.State = 2;
        }

        entity.UpdatedTime = DateTime.Now;
        entity.UpdatedUserId = _userManager.UserId;

        int isOk = await _repository.AsSugarClient().Updateable(entity)
            .IgnoreColumns(true).ExecuteCommandAsync();

        if (!(isOk > 0))
        {
            throw Oops.Oh(ErrorCode.COM1001);
        }

        if (input.formData.IsNotEmptyOrNull())
        {
            await _repository.AsSugarClient().Updateable<PortalDataEntity>()
                .Where(it => it.PortalId.Equals(id) && it.Type.Equals("model"))
                .SetColumns(it => new PortalDataEntity
                {
                    FormData = input.formData,
                    UpdatedTime = SqlFunc.GetDate(),
                    UpdatedUserId = _userManager.UserId
                })
                .ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     删除门户信息.
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpDelete("{id}/delete")]
    public async Task Delete(long id)
    {
        PortalEntity? entity = await _repository.AsQueryable()
            .Where(it => it.Id == id)
            .FirstAsync();
        _ = entity ?? throw Oops.Oh(ErrorCode.COM1005);

        var entityLinkSystemId = await _repository.AsSugarClient().Queryable<PortalManageEntity>()
            .Where(it => it.PortalId == id)
            .Select(it => it.SystemId)
            .FirstAsync();
        string? systemName = await _repository.AsSugarClient().Queryable<SysService>()
            .Where(it => it.Id.Equals(entityLinkSystemId))
            .Select(it => it.Name)
            .FirstAsync();
        if (entityLinkSystemId.IsNotEmptyOrNull() && systemName.IsNotEmptyOrNull())
        {
            throw Oops.Oh(ErrorCode.D1917, systemName);
        }

        await _repository.AsSugarClient().Deleteable(entity).ExecuteCommandAsync();

        int isOk = await _repository.AsSugarClient().Deleteable<PortalDataEntity>()
            .Where(it => it.PortalId == id)
            .ExecuteCommandAsync();
        if (!(isOk > 0))
        {
            throw Oops.Oh(ErrorCode.COM1002);
        }
    }

    /// <summary>
    ///     复制.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpPost("{id}/Actions/Copy")]
    public async Task ActionsCopy(long id)
    {
        string? random = new Random().NextLetterAndNumberString(5);
        PortalEntity? entity = await _repository.AsQueryable()
            .Where(it => it.Id == id)
            .FirstAsync();
        PortalEntity newEntity = new PortalEntity
        {
            Id = YitIdHelper.NextId(),
            CreatedTime = DateTime.Now,
            CreatedUserId = _userManager.UserId,
            FullName = entity.FullName + ".副本" + random,
            EnCode = entity.EnCode + random,
            Category = entity.Category,
            Description = entity.Description,
            EnabledMark = 0,
            EnabledLock = entity.EnabledLock,
            State = 0,
            SortCode = entity.SortCode
        };

        PortalDataEntity? dataEntity = await _repository.AsSugarClient().Queryable<PortalDataEntity>()
            .Where(it => it.PortalId.Equals(id) && it.Type.Equals("model"))
            .FirstAsync();
        PortalDataEntity newDataEntity = new PortalDataEntity
        {
            Id = YitIdHelper.NextId(),
            CreatedTime = DateTime.Now,
            CreatedUserId = _userManager.UserId,
            PortalId = newEntity.Id,
            FormData = dataEntity.FormData,
            Type = "model"
        };

        try
        {
            await _repository.AsSugarClient().Insertable(newEntity).ExecuteCommandAsync();
            await _repository.AsSugarClient().Insertable(newDataEntity).ExecuteCommandAsync();
        }
        catch
        {
            if (entity.FullName.Length >= 100 || entity.EnCode.Length >= 50)
            {
                throw Oops.Oh(ErrorCode.COM1009); // 数据长度超过 字段设定长度
            }

            throw;
        }
    }

    // /// <summary>
    // /// 设置默认门户.
    // /// </summary>
    // /// <param name="id"></param>
    // /// <param name="platform"></param>
    // /// <returns></returns>
    // [HttpPut("{id}/Actions/SetDefault")]
    // public async Task SetDefault(string id, string platform)
    // {
    //     
    //
    //     var portalId = portalDic.ToJsonString();
    //     var isOk = await _repository.AsSugarClient().Updateable<UserEntity>()
    //         .Where(it => it.Id.Equals(_userManager.UserId))
    //         .SetColumns(it => new UserEntity()
    //         {
    //             PortalId = portalId,
    //             UpdatedTime = SqlFunc.GetDate(),
    //             UpdatedUserId = _userManager.UserId
    //         })
    //         .ExecuteCommandAsync();
    //     if (!(isOk > 0))
    //         throw Oops.Oh(ErrorCode.D5014);
    // }

    /// <summary>
    ///     实时保存门户.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPut("Custom/Save/{id}")]
    public async Task SavePortal(string id, [FromBody] PortalSaveInput input)
    {
        // 修改子门户的数据
        int isOk = await _repository.AsSugarClient().Updateable<PortalDataEntity>()
            .Where(it => it.Platform == "Web" && it.Type.Equals("custom") && it.PortalId.Equals(id) && it.CreatedUserId.Equals(_userManager.UserId))
            .SetColumns(it => new PortalDataEntity
            {
                FormData = input.formData,
                UpdatedTime = SqlFunc.GetDate(),
                UpdatedUserId = _userManager.UserId
            })
            .ExecuteCommandAsync();
        if (!(isOk > 0))
        {
            throw Oops.Oh(ErrorCode.D1906);
        }
    }

    /// <summary>
    /// 同步门户.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPut("Actions/release/{id}")]
    [UnitOfWork]
    public async Task SyncPortal(long id, [FromBody] PortalSyncInput input)
    {
        var entity = await _repository.AsQueryable().FirstAsync(it =>  it.Id.Equals(id));
        var sysIdList = await _repository.AsSugarClient().Queryable<SysApp>().Select(it => it.AppId).ToListAsync();
        List<PortalDataEntity> portalDataList = new List<PortalDataEntity>();
        List<PortalManageEntity> portalManageList = new List<PortalManageEntity>();
    
        // 主门户数据
        var modelData = await _repository.AsSugarClient().Queryable<PortalDataEntity>()
            .Where(it => it.Type.Equals("model") && it.PortalId.Equals(id))
            .FirstAsync();
    
        // 发布门户数据
        var releaseData = await _repository.AsSugarClient().Queryable<PortalDataEntity>()
            .Where(it => it.Type.Equals("release") && it.PortalId.Equals(id))
            .FirstAsync();
        if (releaseData.IsNotEmptyOrNull())
        {
            releaseData.FormData = modelData.FormData;
            releaseData.UpdatedTime = DateTime.Now;
            releaseData.UpdatedUserId = _userManager.UserId;
            await _repository.AsSugarClient().Updateable(releaseData)
                .UpdateColumns(it => new { it.FormData, it.UpdatedTime, it.UpdatedUserId })
                .ExecuteCommandAsync();
        }
        else
        {
            var newReleaseData = modelData.Adapt<PortalDataEntity>();
            newReleaseData.Id = YitIdHelper.NextId();
            newReleaseData.Type = "release";
            await _repository.AsSugarClient().Insertable(newReleaseData).ExecuteCommandAsync();
        }
    
        #region 主页门户
    
        if (input.pcPortal == 1)
        {
            if (input.pcPortalSystemId.Any())
            {
                foreach (var item in input.pcPortalSystemId)
                {
                    if (!sysIdList.Contains(item)) throw Oops.Oh(ErrorCode.D4022);
    
                    portalManageList.Add(new PortalManageEntity()
                    {
                        Id = YitIdHelper.NextId(),
                        PortalId = id,
                        SystemId = item,
                        EnabledMark = 1,
                        Platform = "Web",
                        CreatedTime = DateTime.Now,
                        CreatedUserId = _userManager.UserId,
                    });
                }
            }
            else
            {
                if (!await _repository.AsSugarClient().Queryable<PortalManageEntity>().AnyAsync(it => sysIdList.Contains(it.SystemId) && it.PortalId.Equals(id) && it.Platform.Equals("Web")))
                    throw Oops.Oh(ErrorCode.D4023);
            }
        }
    
        // 添加门户管理
        await _repository.AsSugarClient().Insertable(portalManageList).ExecuteCommandAsync();
    
        #endregion
    
        var updateList = new List<string>();
        if (input.pc.Equals(1) || input.pcPortal.Equals(1))
            updateList.Add("Web");
    
        foreach (var item in updateList)
        {
            var dataList = await _repository.AsSugarClient().Queryable<PortalDataEntity>()
                .Where(it => it.PortalId.Equals(id) && it.Platform.Equals(item) && it.Type.Equals("custom"))
                .ToListAsync();
    
            foreach (var upData in dataList)
            {
                upData.FormData = modelData.FormData;
                upData.UpdatedTime = DateTime.Now;
                upData.UpdatedUserId = _userManager.UserId;
    
                portalDataList.Add(upData);
            }
        }
    
        // 更新门户数据
        await _repository.AsSugarClient().Updateable(portalDataList)
            .UpdateColumns(it => new {
                it.FormData,
                it.UpdatedTime,
                it.UpdatedUserId
            }).ExecuteCommandAsync();
    
        // 更新门户发布状态
        await _repository.AsUpdateable()
            .SetColumns(it => new PortalEntity
            {
                State = 1,
                EnabledMark = 1,
                PlatformRelease = input.platformRelease
            })
            .Where(it => it.Id.Equals(id)).ExecuteCommandAsync();
    }

    #endregion
}