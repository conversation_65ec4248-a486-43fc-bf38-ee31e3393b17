using VisualDev.Entity.Dto.Portal;

namespace VisualDev;

/// <summary>
///     门户分类
/// </summary>
[ApiDescriptionSettings("门户")]
public class PortalCategoryService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<PortalCategory> _repository;
    private readonly IUserManager _userManager;

    /// <summary>
    ///     初始化一个<see cref="BillRuleService" />类型的新实例.
    /// </summary>
    public PortalCategoryService(ISqlSugarRepository<PortalCategory> repository, IUserManager userManager)
    {
        _repository = repository;
        _userManager = userManager;
    }

    /// <summary>
    ///     获取接口列表树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/portalCategory/tree")]
    public async Task<dynamic> GetSelector()
    {
        return await _repository.AsQueryable()
            .ToTreeAsync(u => u.Children, u => u.Pid, 0);
    }

    /// <summary>
    ///     添加接口分类
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/portalCategory/add")]
    public async Task Create([FromBody] PortalCategoryAddInput input)
    {
        if (await _repository.IsAnyAsync(x => x.Name == input.Name && x.TenantId == _userManager.TenantId))
        {
            throw Oops.Oh("分类名称已存在！");
        }

        PortalCategory entity = input.Adapt<PortalCategory>();
        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh("新增数据失败");
        }
    }

    /// <summary>
    ///     修改接口分类
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("/portalCategory/{id}/update")]
    public async Task Update(long id, [FromBody] PortalCategoryUpdateInput input)
    {
        if (await _repository.IsAnyAsync(x => x.Name == input.Name && x.Id == input.Id && x.TenantId == _userManager.TenantId))
        {
            throw Oops.Oh("分类名称已存在！");
        }

        PortalCategory entity = input.Adapt<PortalCategory>();
        bool isOk = await _repository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("修改数据失败");
        }
    }

    /// <summary>
    ///     删除接口分类
    /// </summary>
    /// <param name="id">主键id.</param>
    /// <returns></returns>
    [HttpPost("/portalCategory/{id}/delete")]
    public async Task Delete(long id)
    {
        if (await _repository.AsSugarClient().Queryable<PortalEntity>().AnyAsync(a => a.Category == id))
        {
            throw Oops.Oh("分类正在使用中，无法删除");
        }

        bool isOk = await _repository.AsDeleteable()
            .Where(it => it.Id == id)
            .ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh("删除数据失败");
        }
    }
}