namespace VisualDev.Entity;

/// <summary>
/// 可视化个人配置实体
/// </summary>
[SugarTable("VISUALDEV_PERSONAL")]
public class VisualPersonalEntity : EntityTenant
{
    /// <summary>
    /// 菜单ID
    /// </summary>
    [SugarColumn(ColumnName = "F_MENUID")]
    public long MenuId { get; set; }

    /// <summary>
    /// 视图状态：0-系统，1-其他
    /// </summary>
    [SugarColumn(ColumnName = "F_TYPE")]
    public int Type { get; set; }

    /// <summary>
    /// 视图状态：0-其他，1-默认
    /// </summary>
    [SugarColumn(ColumnName = "F_STATUS")]
    public int Status { get; set; }

    /// <summary>
    /// 配置名称
    /// </summary>
    [SugarColumn(ColumnName = "F_FULLNAME")]
    public string FullName { get; set; }

    /// <summary>
    /// 查询字段
    /// </summary>
    [SugarColumn(ColumnName = "F_SEARCHLIST", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string SearchList { get; set; }

    /// <summary>
    /// 列表字段
    /// </summary>
    [SugarColumn(ColumnName = "F_COLUMNLIST", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string ColumnList { get; set; }
}