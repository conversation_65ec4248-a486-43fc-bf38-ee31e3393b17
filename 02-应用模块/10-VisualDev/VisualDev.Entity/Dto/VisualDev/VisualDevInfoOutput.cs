namespace VisualDev.Entity.Dto.VisualDev;

/// <summary>
/// 功能信息输出.
/// </summary>
[SuppressSniffer]
public class VisualDevInfoOutput
{
    /// <summary>
    /// 功能id.
    /// </summary>
    public long? id { get; set; }

    /// <summary>
    /// 功能名称.
    /// </summary>
    public string? fullName { get; set; }

    /// <summary>
    /// 功能编码.
    /// </summary>
    public string? enCode { get; set; }

    /// <summary>
    /// 分类id.
    /// </summary>
    public string? category { get; set; }

    /// <summary>
    /// 功能类型
    /// 1-Web设计,2-App设计,3-流程表单,4-Web表单,5-App表单.
    /// </summary>
    public string? type { get; set; }

    /// <summary>
    /// 说明.
    /// </summary>
    public string? description { get; set; }

    /// <summary>
    /// 表单JSON包.
    /// </summary>
    public string? formData { get; set; }

    /// <summary>
    /// 列表JSON包.
    /// </summary>
    public string? columnData { get; set; }

    /// <summary>
    /// App列表JSON包.
    /// </summary>
    public string? appColumnData { get; set; }

    /// <summary>
    /// 数据表JSON包.
    /// </summary>
    public string? tables { get; set; }

    /// <summary>
    /// 状态
    /// 0-禁用，1-开启.
    /// </summary>
    public int state { get; set; }

    /// <summary>
    /// 1-纯表单,2-列表表单,3-工作流表单.
    /// </summary>
    public string? webType { get; set; }

    /// <summary>
    /// 数据源id.
    /// </summary>
    public long? dbLinkId { get; set; }

    /// <summary>
    /// 工作流模板Json.
    /// </summary>
    public string? flowTemplateJson { get; set; }

    /// <summary>
    /// 引擎ID.
    /// </summary>
    public string? flowId { get; set; }

    /// <summary>
    /// 排序.
    /// </summary>
    public long sortCode { get; set; }

    /// <summary>
    /// 是否发布.
    /// </summary>
    public int isRelease { get; set; }

    /// <summary>
    /// 是否启用流程.
    /// </summary>
    public int enableFlow { get; set; }

    /// <summary>
    /// 接口id.
    /// </summary>
    public string interfaceId { get; set; }

    /// <summary>
    /// 接口名称.
    /// </summary>
    public string interfaceName { get; set; }

    /// <summary>
    /// 接口参数.
    /// </summary>
    public string interfaceParam { get; set; }
}
