namespace VisualDev.Entity.Dto.Portal;

/// <summary>
///     在线开发门户列表查询输入.
/// </summary>
[SuppressSniffer]
public class PortalListQueryInput : PageInputBase
{
    /// <summary>
    ///     分类.
    /// </summary>
    public long? category { get; set; }

    /// <summary>
    ///     锁定（0-锁定，1-自定义）.
    /// </summary>
    public int? enabledLock { get; set; }

    /// <summary>
    ///     状态(0-未发步，1-已发布，2-已修改).
    /// </summary>
    public int? isRelease { get; set; }
}