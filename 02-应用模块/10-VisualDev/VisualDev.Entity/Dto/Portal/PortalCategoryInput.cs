namespace VisualDev.Entity.Dto.Portal;

public class PortalCategoryAddInput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     上级节点
    /// </summary>
    public long Pid { get; set; }
}

public class PortalCategoryUpdateInput : PortalCategoryAddInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}