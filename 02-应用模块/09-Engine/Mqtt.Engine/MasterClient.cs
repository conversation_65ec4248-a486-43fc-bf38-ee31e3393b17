using Furion.TaskQueue;

namespace Mqtt.Engine;

/// <summary>
///     订阅MQTT
/// </summary>
public class MasterClient : ISingleton
{
    public IMqttClient Client { get; set; }
    private MqttClientOptions _clientOptions;
    private readonly ILogger<MasterClient> _logger;
    public bool IsConnected => Client?.IsConnected ?? false;
    private readonly IEventPublisher _eventPublisher;

    private readonly ITaskQueue _taskQueue;

    /// <summary>
    /// </summary>
    /// <param name="logger">日志</param>
    /// <param name="taskQueue">队列</param>
    /// <param name="eventPublisher">事件</param>
    public MasterClient(ILogger<MasterClient> logger, ITaskQueue taskQueue, IEventPublisher eventPublisher)
    {
        _logger = logger;
        _taskQueue = taskQueue;
        _eventPublisher = eventPublisher;
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        //mqtt连接
        _ = ConnectAsync();
    }

    /// <summary>
    ///     重置Master节点连接
    /// </summary>
    public void ReConnect()
    {
        //1.停止master节点
        StopMaster();
        //2.重新连接master节点
        _ = ConnectAsync();
    }

    /// <summary>
    ///     停止master节点连接
    /// </summary>
    public void StopMaster()
    {
        if (Client == null)
        {
            return;
        }

        Client.DisconnectAsync();
        Client.Dispose();
    }

    /// <summary>
    ///     master的MQTT连接
    /// </summary>
    private async Task ConnectAsync()
    {
        try
        {
            Client = new MqttFactory().CreateMqttClient();
            _clientOptions = new MqttClientOptionsBuilder()
                .WithClientId(Guid.NewGuid().ToString("N"))
                .WithTcpServer(App.Configuration["Mqtt:Ip"], Convert.ToInt32(App.Configuration["Mqtt:Port"]))
                .WithCredentials(App.Configuration["Mqtt:UserName"], App.Configuration["Mqtt:Password"])
                .WithTimeout(TimeSpan.FromSeconds(15))
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(60))
                .Build();
            // 消息回调
            Client.ApplicationMessageReceivedAsync += ApplicationMessageReceived;
            // 连接成功
            Client.ConnectedAsync += _ => OnConnected();
            // 连接断开
            Client.DisconnectedAsync += _ => OnDisconnectedAsync();
            await Client.ConnectAsync(_clientOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError("MQTT 初始化出错," + ex.Message);
        }
    }

    // 最后断开时间
    private DateTime _lastDisConnectTime = DateTime.MinValue;

    /// <summary>
    /// </summary>
    private async Task OnDisconnectedAsync()
    {
        try
        {
            await Client.ConnectAsync(_clientOptions);
        }
        catch (Exception e)
        {
            if ((DateTime.Now - _lastDisConnectTime).TotalSeconds > 15)
            {
                _lastDisConnectTime = DateTime.Now;
                _logger.LogError($"【MQTT连接】 Error:{e.Message}");
            }
        }
    }

    /// <summary>
    /// </summary>
    private async Task OnConnected()
    {
        try
        {
            await Client.SubscribeAsync("+/online/data/pub", MqttQualityOfServiceLevel.AtLeastOnce);
            await Client.SubscribeAsync("+/offline/data/pub", MqttQualityOfServiceLevel.AtLeastOnce);
            await Client.SubscribeAsync("+/meta/pub", MqttQualityOfServiceLevel.AtLeastOnce);
            await Client.SubscribeAsync("+/SystemServices/broker/uptime", MqttQualityOfServiceLevel.AtLeastOnce);
            await Client.SubscribeAsync("+/resp/post/gateway/ota/cmd", MqttQualityOfServiceLevel.AtLeastOnce);
            _logger.LogInformation("MQTT连接成功！");
        }
        catch (Exception e)
        {
            _logger.LogError("【MQTT订阅】:" + e.Message);
        }
    }

    /// <summary>
    ///     消息回调
    /// </summary>
    /// <param name="e"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private async Task ApplicationMessageReceived(MqttApplicationMessageReceivedEventArgs e)
    {
        try
        {
            string payload = Encoding.UTF8.GetString(e.ApplicationMessage.PayloadSegment);
            if (string.IsNullOrEmpty(payload))
            {
                return;
            }


            // 实时数据 or 离线数据
            if (e.ApplicationMessage.Topic.Contains("/online/data/pub") || e.ApplicationMessage.Topic.Contains("/offline/data/pub")) //上报采集数据
            {
                ELinkDevSendEx? eLinkData = JSON.Deserialize<ELinkDevSendEx>(payload);
                // 去掉空格
                eLinkData.DeviceName = eLinkData.DeviceName.Trim();
                // 实时数据
                if (e.ApplicationMessage.Topic.Contains("/online/data/pub"))
                {
                    // _logger.LogWarning($"设备：[{eLinkData.DeviceName}],上报时间:{IotPlatform.Core.Extension.DateTime.ToTime(eLinkData.ParentTime)}, 当前时间:{DateTime.Now}");
                    // if (!_customOnLine.DeviceThreads.ContainsKey(eLinkData.DeviceName))
                    // {
                    //     return;
                    // }

                    eLinkData.Params.TryAdd("online_", new ParamValue
                    {
                        Value = "True",
                        Time = eLinkData.ParentTime
                    });
                    await _eventPublisher.PublishAsync(eLinkData.DeviceName, eLinkData);
                }
                else
                {
                    // // 验证该设备是否是平台配置的物实例
                    // if (!_customOnLine.DeviceThreads.ContainsKey(eLinkData.DeviceName))
                    // {
                    //     return;
                    // }
                    await _eventPublisher.PublishAsync(eLinkData.DeviceName + "_offline", eLinkData);
                }
            }
            // 同步时间
            else if (e.ApplicationMessage.Topic.Contains("SystemServices/broker/uptime"))
            {
                string topic = e.ApplicationMessage.Topic.Split("/")[0] + "/SystemServices/broker/uptime_reply";
                long deviceSendTime = IotPlatform.Core.Extension.DateTime.ToLong();
                if (payload.IsNotEmptyOrNull())
                {
                    SysTimeReq? timeReq = JSON.Deserialize<SysTimeReq>(payload);
                    deviceSendTime = timeReq.DeviceSendTime;
                }

                long nowTime = IotPlatform.Core.Extension.DateTime.ToLong(DateTime.UtcNow);

                MqttApplicationMessage message = new()
                {
                    PayloadSegment = Encoding.UTF8.GetBytes(JSON.Serialize(new
                    {
                        HubSendTime = nowTime,
                        DeviceSendTime = deviceSendTime,
                        HubRecvTime = nowTime
                    })),
                    Retain = false,
                    Topic = topic,
                    QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce
                };
                await Client.PublishAsync(message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"ClientId:{e.ClientId} Topic:{e.ApplicationMessage.Topic},Payload:{e.ApplicationMessage.ConvertPayloadToString()}," + ex);
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="message"></param>
    public async Task<bool> PublishAsync(MqttApplicationMessage message)
    {
        MqttClientPublishResult? result = await Client.PublishAsync(message);
        return result.ReasonCode == MqttClientPublishReasonCode.Success;
    }
}