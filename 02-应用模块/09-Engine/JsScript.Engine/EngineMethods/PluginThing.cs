using Common.Hubs;
using IotPlatform.Core.Enum;
using JsScript.Engine.Attributes;

namespace JsScript.Engine.EngineMethods;

/// <summary>
///     物模型实时数据
/// </summary>
[Engine]
public class PluginThing : ISingleton
{
    private IServiceProvider Services { get; }


    private readonly SysCacheService _sysCacheService;

    /// <summary>
    ///     Socket推送
    /// </summary>
    private readonly SocketSingleton _socketService;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="services">服务提供者</param>
    /// <param name="sysCacheService">系统缓存服务</param>
    /// <param name="socketService"></param>
    public PluginThing(IServiceProvider services, SysCacheService sysCacheService, SocketSingleton socketService)
    {
        Services = services;
        _sysCacheService = sysCacheService;
        _socketService = socketService;
    }

    /// <summary>
    ///     缓存中获取属性值
    /// </summary>
    /// <param name="deviceName"></param>
    /// ///
    /// <param name="key"></param>
    /// <returns></returns>
    [EngineMethod("model.Cache('deviceName', 'attributeKey')", "获取设备属性缓存值", "// 方法:model.Cache()\n// 参数一: 'deviceName' 设备名称\n// 参数二: 'attributeKey' 属性名称(非必填)\n// 返回类型:dynamic\n// 描述:该方法从缓存中获取设备属性值\nvar device = 'device1';\nvar attribute = 'temperature';\nvar value = model.Cache(device, attribute);\nreturn value;\n\n// 示例返回结果\n25.5", "实时数据")]
    public dynamic? Cache(string deviceName, string key = null)
    {
        var deviceNameKey = $"cache_{deviceName}";
        // 传入空就直接返回
        WriteTDengIne? value = _sysCacheService.Get<WriteTDengIne>(deviceNameKey);
        if (value == null)
        {
            return null;
        }

        if (!value.Params.Any())
        {
            return null;
        }

        if (key == null)
        {
            return value.Params;
        }

        return value.Params.TryGetValue(key, out WriteParam? cache) ? cache : null;
    }

    /// <summary>
    ///     内存中获取属性值
    /// </summary>
    /// <param name="paramObjList"></param>
    /// <returns></returns>
    [EngineMethod("model.Find(['device1/temperature', 'device2/humidity'])", "获取多个属性值", "// 方法:model.Find()\n// 参数一: ['device1/temperature', 'device2/humidity'] 属性标识列表\n// 返回类型:dynamic\n// 描述:该方法从内存中获取多个属性值\nvar params = ['device1/temperature', 'device2/humidity'];\nvar values = model.Find(params);\nreturn values;\n\n// 示例返回结果\n[25.5, 60]", "实时数据")]
    public dynamic? Find(object paramObjList)
    {
        List<string> paramList = new();
        if (paramObjList != null)
        {
            paramList = paramObjList.Adapt<List<string>>();
        }
        return paramList.Select(param => DataStorage.Instance.GetData(param)).Cast<dynamic>().ToList();
    }

    /// <summary>
    ///     内存中获取属性值
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [EngineMethod("model.Find('device/attribute')", "获取单个属性值", "// 方法:model.Find()\n// 参数一: 'device/attribute' 属性标识\n// 返回类型:dynamic\n// 描述:该方法从内存中获取单个属性值\nvar param = 'device1/temperature';\nvar value = model.Find(param);\nreturn value;\n\n// 示例返回结果\n25.5", "实时数据")]
    public dynamic? Find(string param)
    {
        //传入空就直接返回
        if (param.IsNullOrEmpty())
        {
            return null;
        }
        return DataStorage.Instance.GetData(param);
    }

    /// <summary>
    ///     实例在线状态
    /// </summary>
    /// <param name="identification">物标识</param>
    /// <returns></returns>
    [EngineMethod("model.Status('deviceId')", "获取设备在线状态", "// 方法:model.Status()\n// 参数一: 'deviceId' 设备标识\n// 返回类型:bool\n// 描述:该方法获取设备的在线状态\nvar deviceId = 'device1';\nvar isOnline = model.Status(deviceId);\nreturn isOnline;\n\n// 示例返回结果\ntrue", "实时数据")]
    public bool Status(string identification)
    {
        if (identification.IsNullOrEmpty())
        {
            return false;
        }

        return DataStorage.Instance.deviceConnectStatus.ContainsKey(identification) && DataStorage.Instance.deviceConnectStatus[identification];
    }

    /// <summary>
    ///     实例在线状态
    /// </summary>
    /// <param name="identificationObjList">物标识集合</param>
    /// <returns></returns>
    [EngineMethod("model.Status(['device1', 'device2'])", "获取多个设备在线状态", "// 方法:model.Status()\n// 参数一: ['device1', 'device2'] 设备标识列表\n// 返回类型:object\n// 描述:该方法获取多个设备的在线状态\nvar devices = ['device1', 'device2'];\nvar statuses = model.Status(devices);\nreturn statuses;\n\n// 示例返回结果\n{\"device1\": true, \"device2\": false}", "实时数据")]
    public dynamic Status(object identificationObjList)
    {
        List<string> identificationList = new();
        if (identificationObjList != null)
        {
            identificationList = identificationObjList.Adapt<List<string>>();
        }

        return identificationList.ToDictionary(identification => identification,
            identification => DataStorage.Instance.deviceConnectStatus.ContainsKey(identification) && DataStorage.Instance.deviceConnectStatus[identification]);
    }
    
    /// <summary>
    ///     内存中修改属性值
    /// </summary>
    /// <param name="param">属性标识，格式为"device/attribute"</param>
    /// <param name="value">要设置的新值</param>
    /// <returns>更新是否成功</returns>
    [EngineMethod("model.Update('device/attribute', value)", "修改单个属性值", "// 方法:model.Update()\n// 参数一: 'device/attribute' 属性标识\n// 参数二: value 要设置的新值\n// 返回类型:bool\n// 描述:该方法修改内存中单个属性的值\nvar param = 'device1/temperature';\nvar newValue = 30.5;\nvar success = model.Update(param, newValue);\nreturn success;\n\n// 示例返回结果\ntrue", "实时数据")]
    public bool Update(string param, dynamic value)
    {
        var paramSplit = param.Split("/");
        // 传入空就直接返回
        if (param.IsNullOrEmpty())
        {
            return false;
        }

        try
        {
            // 使用当前时间戳
            long timestamp = DateTimeOffset.UtcNow.AddHours(-8).ToUnixTimeMilliseconds();
            // 直接传递value作为object
            DataStorage.Instance.UpdateData(param, value, timestamp);

            var deviceName = paramSplit[0];
            var key = paramSplit[1];
            _ = _socketService.Send(JSON.Serialize(new
            {
                DeviceName = deviceName,
                Key = key,
                Value = value,
                Time = timestamp
            }), "online_variable");
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     内存中批量修改属性值
    /// </summary>
    /// <param name="paramValuePairs">属性标识和值的键值对集合</param>
    /// <returns>每个属性的更新状态</returns>
    [EngineMethod("model.Update({'device1/temperature': 30.5, 'device2/humidity': 65})", "批量修改属性值", "// 方法:model.Update()\n// 参数一: {'device1/temperature': 30.5, 'device2/humidity': 65} 属性标识和值的键值对\n// 返回类型:object\n// 描述:该方法批量修改内存中多个属性的值\nvar updates = {'device1/temperature': 30.5, 'device2/humidity': 65};\nvar results = model.Update(updates);\nreturn results;\n\n// 示例返回结果\n{\"device1/temperature\": true, \"device2/humidity\": true}", "实时数据")]
    public Dictionary<string, bool> Update(object paramValuePairs)
    {
        Dictionary<string, bool> results = new Dictionary<string, bool>();

        if (paramValuePairs == null)
        {
            return results;
        }

        try
        {
            // 将传入的对象转换为字典
            Dictionary<string, object> updateDict = paramValuePairs.Adapt<Dictionary<string, object>>();

            // 当前时间戳
            long timestamp = DateTimeOffset.UtcNow.AddHours(-8).ToUnixTimeMilliseconds();

            // 批量更新
            foreach (var pair in updateDict)
            {
                string param = pair.Key;
                object value = pair.Value;

                var paramSplit = param.Split("/");
                try
                {
                    // 直接传递value作为object
                    DataStorage.Instance.UpdateData(param, value, timestamp);
                    results[param] = true;
                    var deviceName = paramSplit[0];
                    var key = paramSplit[1];
                    _ = _socketService.Send(JSON.Serialize(new
                    {
                        DeviceName = deviceName,
                        Key = key,
                        Value = value,
                        Time = timestamp
                    }), "online_variable");
                }
                catch
                {
                    results[param] = false;
                }
            }
        }
        catch
        {
            // 如果转换失败，返回空结果
            return results;
        }

        return results;
    }
}