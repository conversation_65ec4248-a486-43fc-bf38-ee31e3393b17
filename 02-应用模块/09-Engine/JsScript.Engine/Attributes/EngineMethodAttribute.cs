namespace JsScript.Engine.Attributes;

/// <summary>
///     脚本方法类
/// </summary>
[AttributeUsage(AttributeTargets.Method)]
public class EngineMethodAttribute : Attribute
{
    /// <summary>
    /// 是否记录方法调用日志
    /// </summary>
    public bool LogMethodCall { get; set; }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="content">方法</param>
    /// <param name="name">名称</param>
    /// <param name="sampleCode">示例代码</param>
    /// <param name="type">类型</param>
    public EngineMethodAttribute(string content, string name, string sampleCode, string type)
    {
        Content = content;
        Describe = sampleCode;
        ScriptTypeName = type;
        Name = name;
    }

    /// <summary>
    ///     方法
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     方法说明
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     示例代码
    /// </summary>
    public string Describe { get; set; }

    /// <summary>
    /// 分类
    /// </summary>
    public string ScriptTypeName { get; set; }
}