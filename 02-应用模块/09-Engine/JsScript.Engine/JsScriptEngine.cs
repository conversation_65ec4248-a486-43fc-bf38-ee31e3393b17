using IotGateway.Application.Exec;

namespace JsScript.Engine;

/// <summary>
///     执行脚本公共基类
/// </summary>
public class JsScriptEngine2 : ITransient, IDisposable
{
    private readonly JsEnginePool _enginePool;
    private JsEngineWrapper _currentEngineWrapper;

    /// <summary>
    /// 获取当前引擎实例
    /// </summary>
    public Jint.Engine Engine => _currentEngineWrapper?.Engine;

    /// <summary>
    /// 获取当前日志引擎
    /// </summary>
    public LogEngine LogEngine => _currentEngineWrapper?.LogEngine;

    /// <summary>
    /// </summary>
    /// <param name="enginePool">Jint引擎池</param>
    public JsScriptEngine2(JsEnginePool enginePool)
    {
        _enginePool = enginePool;
        // 初始获取一个引擎实例
        _currentEngineWrapper = _enginePool.GetEngine();
    }

    /// <summary>
    ///     重置引擎
    /// </summary>
    public void ResetEngine()
    {
        // 归还当前引擎实例到池中
        if (_currentEngineWrapper != null)
        {
            _enginePool.ReturnEngine(_currentEngineWrapper);
        }

        // 获取新的引擎实例
        _currentEngineWrapper = _enginePool.GetEngine();
    }

    /// <summary>
    /// 执行脚本
    /// </summary>
    /// <param name="jsContent"></param>
    /// <param name="args"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public object CallFunction(string jsContent, object args)
    {
        try
        {
            // 处理DataTable类型    
            if (args.GetType() == typeof(DataTable))
            {
                // 将DataTable转换为List<dynamic>
                List<dynamic>? convertedData = ((DataTable)args).ConvertDataTableToList();
                // 将转换后的数据设置为data变量
                _currentEngineWrapper.Engine.SetValue("data", convertedData);
            }
            else
            {
                // 将其他类型数据设置为data变量
                _currentEngineWrapper.Engine.SetValue("data", args);
            }
            // 执行脚本公共基类
            object? result = _currentEngineWrapper.Engine.Evaluate(jsContent, new ScriptParsingOptions
            {
                // 允许宽松解析
                Tolerant = true,
                // 允许在函数外部返回值
                AllowReturnOutsideFunction = true
            }).ToObject();

            // 执行完后清理
            _currentEngineWrapper.Engine.SetValue("data", string.Empty);
            // 强制垃圾回收 
            GC.Collect();

            return result;
        }
        catch (Exception e)
        {
            throw new Exception($"JS执行出错：{e.Message}");
        }
    }

    public void Dispose()
    {
        // 归还当前引擎实例到池中
        if (_currentEngineWrapper != null)
        {
            _enginePool.ReturnEngine(_currentEngineWrapper);
            _currentEngineWrapper = null;
        }
    }
}

/// <summary>
///     jint初始化封装脚本内容
/// </summary>
public static class JintReadFileContent
{
    /// <summary>
    ///     数据库封装
    /// </summary>
    public static string dbFile { get; set; }

    /// <summary>
    ///     http请求封装
    /// </summary>
    public static string httpFile { get; set; }
}