using System.Reflection;
using Furion.DynamicApiController;
using JsScript.Engine.Attributes;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace JsScript.Engine.Service;

/// <summary>
///     规则引擎-脚本列表
///     版 本:V4.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-11
/// </summary>
[ApiDescriptionSettings("任务定义")]
[Route("")]
[Route("webhook/")]
public class SysScriptService : IDynamicApiController, ITransient
{
    private readonly JsScriptEnginePool _enginePool;
    private readonly ILogger<SysScriptService> _logger;

    /// <summary>
    ///     初始化系统脚本服务
    /// </summary>
    /// <param name="enginePool">脚本引擎池</param>
    /// <param name="logger">日志记录器</param>
    public SysScriptService(
        JsScriptEnginePool enginePool,
        ILogger<SysScriptService> logger)
    {
        _enginePool = enginePool;
        _logger = logger;
    }

    /// <summary>
    ///     系统脚本集合
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sys_script/list")]
    public async Task<dynamic> SysScriptList()
    {
        var output = new List<dynamic>();
        // 程序集
        var assembly = Assembly.GetExecutingAssembly();
        // 获取系统脚本类
        var classesWithAttribute = assembly.GetTypes()
            .Where(type => Attribute.IsDefined(type, typeof(EngineAttribute)));
        // 遍历系统脚本
        foreach (var classType in classesWithAttribute)
        {
            // 开放脚本方法
            var methodsWithAttribute = classType.GetMethods().Where(method => Attribute.IsDefined(method, typeof(EngineMethodAttribute)));
            // 遍历系统开放脚本方法
            foreach (var method in methodsWithAttribute)
            {
                var custom = method.CustomAttributes.LastOrDefault();
                if (custom == null)
                    continue;
                var methodData = new
                {
                    // 方法函数
                    Content = custom.ConstructorArguments[0].Value,
                    // 名称
                    Name = custom.ConstructorArguments[1].Value,
                    // 示例代码
                    Describe = custom.ConstructorArguments[2].Value,
                    // 分类
                    ScriptTypeName = custom.ConstructorArguments[3].Value,
                };
                output.Add(methodData);
            }
        }
        return output;
    }
}