using IotPlatform.Core.Extension;
using Newtonsoft.Json;

namespace VisualDev.Engine.Utility;

/// <summary>
/// 表单数据验证工具类
/// </summary>
public static class FormDataValidator
{
    /// <summary>
    /// 验证JSON字符串是否有效
    /// </summary>
    /// <param name="jsonString">要验证的JSON字符串</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateJson(string jsonString)
    {
        if (string.IsNullOrEmpty(jsonString))
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = "JSON字符串为空或null",
                ErrorType = "Empty"
            };
        }

        try
        {
            // 尝试解析JSON
            JToken.Parse(jsonString);
            return new ValidationResult
            {
                IsValid = true,
                ErrorMessage = string.Empty,
                ErrorType = string.Empty
            };
        }
        catch (JsonReaderException ex)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = ex.Message,
                ErrorType = "JsonReaderException",
                LineNumber = ex.LineNumber,
                LinePosition = ex.LinePosition
            };
        }
        catch (JsonException ex)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = ex.Message,
                ErrorType = "JsonException"
            };
        }
        catch (Exception ex)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = ex.Message,
                ErrorType = ex.GetType().Name
            };
        }
    }

    /// <summary>
    /// 验证FormData是否能正确转换为FormDataModel
    /// </summary>
    /// <param name="formData">FormData字符串</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateFormDataModel(string formData)
    {
        var jsonValidation = ValidateJson(formData);
        if (!jsonValidation.IsValid)
        {
            return jsonValidation;
        }

        try
        {
            // 尝试转换为FormDataModel
            var formModel = formData.ToObjectOld<FormDataModel>();

            if (formModel == null)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    ErrorMessage = "JSON格式正确但无法转换为FormDataModel对象",
                    ErrorType = "ConversionFailed"
                };
            }

            return new ValidationResult
            {
                IsValid = true,
                ErrorMessage = string.Empty,
                ErrorType = string.Empty
            };
        }
        catch (Exception ex)
        {
            return new ValidationResult
            {
                IsValid = false,
                ErrorMessage = ex.Message,
                ErrorType = ex.GetType().Name
            };
        }
    }

    /// <summary>
    /// 获取数据的基本信息用于诊断
    /// </summary>
    /// <param name="data">要分析的数据</param>
    /// <returns>诊断信息</returns>
    public static DataDiagnosticInfo GetDiagnosticInfo(string data)
    {
        if (data == null)
        {
            return new DataDiagnosticInfo
            {
                IsNull = true,
                Length = 0,
                FirstChars = string.Empty,
                LastChars = string.Empty,
                DataType = "Null"
            };
        }

        return new DataDiagnosticInfo
        {
            IsNull = false,
            Length = data.Length,
            FirstChars = data.Length > 20 ? data.Substring(0, 20) : data,
            LastChars = data.Length > 20 ? data.Substring(data.Length - 20) : data,
            DataType = GetDataType(data)
        };
    }

    private static string GetDataType(string data)
    {
        if (string.IsNullOrEmpty(data))
            return "Empty";

        data = data.Trim();

        if (data.StartsWith("{") && data.EndsWith("}"))
            return "JSON Object";

        if (data.StartsWith("[") && data.EndsWith("]"))
            return "JSON Array";

        if (data.StartsWith("L"))
            return "Starts with L";

        return "Other";
    }
}

/// <summary>
/// JSON验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 错误类型
    /// </summary>
    public string ErrorType { get; set; } = string.Empty;

    /// <summary>
    /// 错误行号（如果适用）
    /// </summary>
    public int? LineNumber { get; set; }

    /// <summary>
    /// 错误列号（如果适用）
    /// </summary>
    public int? LinePosition { get; set; }
}

/// <summary>
/// 数据诊断信息
/// </summary>
public class DataDiagnosticInfo
{
    /// <summary>
    /// 是否为null
    /// </summary>
    public bool IsNull { get; set; }

    /// <summary>
    /// 数据长度
    /// </summary>
    public int Length { get; set; }

    /// <summary>
    /// 前面的字符
    /// </summary>
    public string FirstChars { get; set; } = string.Empty;

    /// <summary>
    /// 后面的字符
    /// </summary>
    public string LastChars { get; set; } = string.Empty;

    /// <summary>
    /// 数据类型
    /// </summary>
    public string DataType { get; set; } = string.Empty;
}