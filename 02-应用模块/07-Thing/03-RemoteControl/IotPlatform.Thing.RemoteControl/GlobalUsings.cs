global using Common.Models;
global using Extras.DatabaseAccessor.SqlSugar.Repositories;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using Microsoft.AspNetCore.Mvc;
global using IotPlatform.Core.Extension;
global using Extras.DatabaseAccessor.SqlSugar.Internal;
global using Furion.DatabaseAccessor;
global using IotPlatform.Thing.Entity;
global using Mapster;
global using System.Threading.Tasks;