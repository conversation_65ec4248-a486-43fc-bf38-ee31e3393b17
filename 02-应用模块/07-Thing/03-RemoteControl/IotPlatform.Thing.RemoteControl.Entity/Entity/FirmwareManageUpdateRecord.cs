namespace IotPlatform.Thing.RemoteControl.Entity;

/// <summary>
///     远程控制-固件管理-升级记录
/// </summary>
[SugarTable("business_firmwareManageUpdateRecord", "远程控制-固件管理-升级记录")]
public class FirmwareManageUpdateRecord : EntityTenantId
{
    /// <summary>
    ///     固件管理Id
    /// </summary>
    [SugarColumn(ColumnDescription = "固件管理Id")]
    public long FirmwareManageDetailId { get; set; }

    /// <summary>
    ///     物实例Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物实例Id")]
    public long ModelThingId { get; set; }

    /// <summary>
    ///     开始升级时间
    /// </summary>
    [SugarColumn(ColumnDescription = "开始升级时间", IsOnlyIgnoreUpdate = true)]
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    ///     升级状态
    /// </summary>
    [SugarColumn(ColumnDescription = "升级状态")]
    public string Status { get; set; }

    /// <summary>
    ///     状态更新时间
    /// </summary>
    [SugarColumn(ColumnDescription = "状态更新时间")]
    public DateTime? StatusUpdateTime { get; set; }

    /// <summary>
    /// 升级日志
    /// </summary>
    [SugarColumn(ColumnDescription = "升级日志",IsJson = true)]
    public Dictionary<DateTime,string>? Logs { get; set; }

    #region 关联表

    /// <summary>
    ///     物实例
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelThingId))]
    public ModelThing ModelThing { get; set; }

    /// <summary>
    ///     固件管理
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(FirmwareManageDetailId))]
    public FirmwareManageDetail FirmwareManageDetail { get; set; }

    #endregion
}