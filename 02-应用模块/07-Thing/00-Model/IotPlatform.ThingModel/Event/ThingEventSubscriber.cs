using IotPlatform.Thing.Warning.HostedService;

namespace IotPlatform.ThingModel.Event;

/// <summary>
///     物实例事件
/// </summary>
public class ThingEventSubscriber : IEventSubscriber
{
    private readonly ThingHostedService _customThingService;

    /// <summary>
    /// </summary>
    private readonly ThingWarningHostedService _warning;

    /// <summary>
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// </summary>
    /// <param name="customThingService"></param>
    /// <param name="warning"></param>
    public ThingEventSubscriber(ThingHostedService customThingService, ThingWarningHostedService warning, ISqlSugarClient db)
    {
        _customThingService = customThingService;
        _warning = warning;
        _db = db;
    }

    /// <summary>
    ///     物实例报警抑制解除-小时
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.ThingRestrainClose)]
    public async Task ThingRestrainCloseByHour(EventHandlerExecutingContext context)
    {
        foreach ((string key, CustomThingWarning deviceThread) in _customThingService.WarningThreads)
        {
            deviceThread.Thing.Restrain = false;
            deviceThread.AlarmHourCount.Clear();
            await _db.CopyNew().Updateable(deviceThread.Thing).UpdateColumns(w => w.Restrain).ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     物实例报警抑制解除-天
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.ThingRestrainCloseByDay)]
    public async Task ThingRestrainCloseByDay(EventHandlerExecutingContext context)
    {
        foreach ((string key, CustomThingWarning deviceThread) in _customThingService.WarningThreads)
        {
            deviceThread.Thing.Restrain = false;
            deviceThread.AlarmDayCount.Clear();
            await _db.CopyNew().Updateable(deviceThread.Thing).UpdateColumns(w => w.Restrain).ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     物实例报警-手动解除
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.ThingWarningHandClose)]
    public async Task ThingWarningHandClose(EventHandlerExecutingContext context)
    {
        ModelAlarmRecord? modelAlarmRecord = context.GetPayload<ModelAlarmRecord>();
        if (_customThingService.WarningThreads.TryGetValue(modelAlarmRecord.ThingIdent, out CustomThingWarning? thread))
        {
            await thread.ManualCloseAlarm(modelAlarmRecord.ModelAlarmId);
        }
    }

    /// <summary>
    ///     同步物模型下实例内容
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.ThingSynchronization)]
    public async Task ThingSynchronization(EventHandlerExecutingContext context)
    {
        List<ModelThing>? thingList = context.GetPayload<List<ModelThing>>();
        foreach (ModelThing thing in thingList)
        {
            await _customThingService.AddDevice(thing);
        }
    }

    /// <summary>
    ///     物模型实例报警
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.ThingWarning)]
    public async Task ThingWarning(EventHandlerExecutingContext context)
    {
        if (!_warning.AlarmConditionThreads.Any())
        {
            return;
        }

        ModelAlarmCustom? payload = context.GetPayload<ModelAlarmCustom>();
        await MessageCenter.PublishAsync(string.Format(EventConst.ModelThingWarning, payload.ModelId), context.GetPayload<ModelAlarmCustom>());
    }
}