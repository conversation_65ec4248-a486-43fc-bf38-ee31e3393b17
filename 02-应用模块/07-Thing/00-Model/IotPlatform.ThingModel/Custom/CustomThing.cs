using System.Threading.Channels;
using Microsoft.Extensions.Caching.Memory;
using NewLife;
using Timer = System.Timers.Timer;

namespace IotPlatform.ThingModel.Custom;

/// <summary>
///     物实例类 - 负责处理单个设备的数据处理和状态管理
///     包含以下主要功能：
///     1. 设备状态监控
///     2. 实时数据处理
///     3. 批量数据处理
///     4. 数据过滤和转换
///     5. 历史数据存储
/// </summary>
public class CustomThing : IDisposable
{
    #region 定时任务-监听物实例连接状态

    /// <summary>
    ///     设备关机定时器,每5分钟对设备进行一次时序库的关机状态写入，实时数据会重置计时
    /// </summary>
    private Timer _deviceCloseTimer;

    /// <summary>
    ///     设备关机定时任务
    /// </summary>
    private void ResetTimer()
    {
        // 停止定时器
        if (_deviceCloseTimer != null)
        {
            _deviceCloseTimer.Stop();
            _deviceCloseTimer.Dispose();
        }

        // 重新设置定时器
        _deviceCloseTimer = new Timer(1000 * 60);
        _deviceCloseTimer.Elapsed += TimerElapsed;
        _deviceCloseTimer.AutoReset = true;
        _deviceCloseTimer.Start();
    }

    /// <summary>
    ///     实时监听设备的状态
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void TimerElapsed(object sender, ElapsedEventArgs e)
    {
        try
        {
            // 超过5分钟就将关机状态写到库中
            if (!((DateTime.Now - LastTime).TotalSeconds >= 300) || ExampleStatus != ExampleStatusEnum.OnLine)
            {
                return;
            }

            if (LastTime != DateTime.MinValue)
            {
                Thing.LastTime = LastTime;
                _db.Updateable(Thing).UpdateColumns(w => w.LastTime).ExecuteCommand();
            }

            // 设备状态改变
            DeviceStatusChange(ExampleStatusEnum.OffLine);
            // 离线时将在线状态设置为离线
            if (GlobalDeviceParams.ContainsKey("online_"))
            {
                // 离线时将在线状态设置为离线
                GlobalDeviceParams["online_"] = new WriteParam
                {
                    DataType = WriteParamValueDataTypeEnum.Bool,
                    Value = "False",
                    DateTime = DateTime.Now.ToLong()
                };
                // 更新缓存
                _cacheService.Set(Thing.Identification, GlobalDeviceParams);
            }
        }
        catch
        {
            // ignored
        }
    }

    #endregion

    #region 参数

    /// <summary>
    ///     全部属性
    /// </summary>
    private readonly Dictionary<string, ModelAttributes> _thingAttributesVariable;

    /// <summary>
    ///     实例连接状态:1:在线；2：离线
    /// </summary>
    public ExampleStatusEnum ExampleStatus { get; set; } = ExampleStatusEnum.OffLine;

    /// <summary>
    ///     最后收到数据时间
    /// </summary>
    public DateTime LastTime { get; set; }

    /// <summary>
    ///     首次收到数据时间
    /// </summary>
    public long FirstTime { get; set; }

    /// <summary>
    ///     实时收到的原始数据
    /// </summary>
    private readonly WriteTDengIne Write;

    /// <summary>
    ///     物实例的配置信息
    /// </summary>
    public readonly ModelThing Thing;

    /// <summary>
    ///     实时数据变化件
    /// </summary>
    public event EventHandler<DataChangedEventArgs> DataChanged;

    #endregion

    #region 服务

    /// <summary>
    ///     缓存信息
    /// </summary>
    private readonly SysCacheService _cacheService;

    /// <summary>
    ///     数据库操作
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// </summary>
    private readonly IEventBusFactory _eventBusFactory;

    /// <summary>
    ///     TDengIne的执行命令
    /// </summary>
    private readonly ExecuteService _executeService;

    /// <summary>
    ///     Socket推送
    /// </summary>
    private readonly SocketSingleton _socketService;

    // /// <summary>
    // ///     jint脚本解析器
    // /// </summary>
    // public readonly Jint.Engine Engine;

    /// <summary>
    ///     时序库写入
    /// </summary>
    private readonly WriteService _writeService;

    #endregion

    /// <summary>
    ///     数据批处理队列 - 用于缓存待处理的设备数据
    /// </summary>
    private readonly Channel<ELinkDevSendInput> _dataQueue;

    /// <summary>
    ///     批处理大小 - 达到此数量时触发批量处理
    /// </summary>
    private const int BatchSize = 100;

    /// <summary>
    ///     内存缓存 - 用于存储设备的最新数据状态，提高访问效率
    ///     SizeLimit: 1024 表示最多缓存1024条数据
    /// </summary>
    private readonly MemoryCache _memoryCache = new(
        new MemoryCacheOptions { SizeLimit = 1024 });

    // 添加一个静态的 ConcurrentDictionary 用于全局持久化存储设备的所有属性值
    public readonly ConcurrentDictionary<string, WriteParam> GlobalDeviceParams = new();

    /// <summary>
    /// </summary>
    /// <param name="db"></param>
    /// <param name="thing"></param>
    /// <param name="eventBusFactory"></param>
    /// <param name="cacheService"></param>
    public CustomThing(ISqlSugarClient db, ModelThing thing, IEventBusFactory eventBusFactory, SysCacheService cacheService)
    {
        Thing = thing;
        _eventBusFactory = eventBusFactory;
        _cacheService = cacheService;
        _thingAttributesVariable = Thing.Model.ThingAttributes.ToDictionary(d => d.Identification, d => d);

        _writeService = App.GetService<WriteService>();
        _executeService = App.GetService<ExecuteService>();
        _socketService = App.GetService<SocketSingleton>();
        _db = db;
        GlobalDeviceParams = _cacheService.Get<ConcurrentDictionary<string, WriteParam>>(Thing.Identification) ?? new ConcurrentDictionary<string, WriteParam>();
        // 初始化时候从缓存中获取上次数据
        Write = new WriteTDengIne
        {
            // 从缓存中获取上次数据
            Params = GlobalDeviceParams
        };

        // 更新实时数据
        DataChanged += async (sender, e) =>
        {
            // Log.Warning($"设备：【{e.DeviceId}】,标识:【{e.Property}】,值发改变:{e.Value},发生时间：{e.Time}");
            // 数据推送
            await _socketService.Send(JSON.Serialize(new
            {
                DeviceName = Thing.Identification,
                Key = e.Property,
                e.Value,
                e.Time
            }), "online_variable");
            // 更新数据存储
            DataStorage.Instance.UpdateData(e.ThingIdent, e.Property, e.Value, e.Time, e.DataType);
        };

        // 初始化Channel
        _dataQueue = Channel.CreateUnbounded<ELinkDevSendInput>(new UnboundedChannelOptions
        {
            SingleReader = true,
            SingleWriter = false
        });
    }

    /// <summary>
    ///     启动订阅该设备的数据
    /// </summary>
    public void Start()
    {
        // 重置设备关机定时器
        ResetTimer();
        Write.Uuid = Thing.Model.Uuid;
        Write.DeviceName = Thing.Identification;

        _eventBusFactory.Subscribe(Thing.Identification + "_offline", async context =>
        {
            ELinkDevSendEx? eLinkDevSend = context.GetPayload<ELinkDevSendEx>();
            // 离线数据仅处理后保存
            await OffLine(eLinkDevSend);
        });

        // 订阅物实例在线状态--仅支持网关模型
        _eventBusFactory.Subscribe(Thing.Identification + "_status", async context =>
        {
            string? status = context.Source.Payload.ToString();
            // 设备状态改变
            DeviceStatusChange(status == "online" ? ExampleStatusEnum.OnLine : ExampleStatusEnum.OffLine);
            // 离线时将在线状态设置为离线
            if (GlobalDeviceParams.ContainsKey("online_"))
            {
                // 离线时将在线状态设置为离线
                GlobalDeviceParams["online_"] = new WriteParam
                {
                    DataType = WriteParamValueDataTypeEnum.Bool,
                    Value = "False",
                    DateTime = IotPlatform.Core.Extension.DateTime.ToLong()
                };
                _cacheService.Set(Write.DeviceName, GlobalDeviceParams);
            }
        });

        // 动态订阅消息
        _eventBusFactory.Subscribe(Thing.Identification, async context =>
        {
            ELinkDevSendEx? eLinkDevSend = context.GetPayload<ELinkDevSendEx>();
            // 首次收到数据时间
            if (FirstTime == 0)
            {
                FirstTime = Core.Extension.DateTime.ToLong();
            }

            // 实时上报
            await OnLine(eLinkDevSend);
            // 更新最后收到数据的时间
            LastTime = DateTime.Now;
            if (ExampleStatus == ExampleStatusEnum.OffLine)
            {
                DeviceStatusChange(ExampleStatusEnum.OnLine);
            }
        });

        // 启动批处理
        _ = StartBatchProcessing();
    }

    /// <summary>
    ///     启动批处理任务
    ///     持续监听数据队列，当满足以下条件之一时触发批处理：
    ///     1. 累积数据达到 BatchSize
    ///     2. 等待时间超过 BatchTimeout
    /// </summary>
    private async Task StartBatchProcessing()
    {
        List<ELinkDevSendInput> batch = new();

        while (await _dataQueue.Reader.WaitToReadAsync())
        {
            while (batch.Count < BatchSize &&
                   _dataQueue.Reader.TryRead(out ELinkDevSendInput? item))
            {
                batch.Add(item);
            }

            if (batch.Any())
            {
                await ProcessBatch(batch);
                batch.Clear();
            }
        }
    }

    /// <summary>
    ///     处理数据批次
    ///     使用并行处理提高性能，每个数据项都会经过完整的处理流程
    /// </summary>
    /// <param name="batch">待处理的数据批次</param>
    private async Task ProcessBatch(List<ELinkDevSendInput> batch)
    {
        try
        {
            // 并行处理批次数据
            await Parallel.ForEachAsync(batch, async (data, token) => { await OnLine(data); });
        }
        catch (Exception ex)
        {
            Log.Error($"批处理失败: {ex.Message}");
        }
    }

    #region 处理上报数据

    /// <summary>
    ///     处理实时数据
    /// </summary>
    /// <param name="sendInput"></param>
    /// <returns></returns>
    private async Task OnLine(ELinkDevSendInput sendInput)
    {
        if (_thingAttributesVariable.Count == 0)
        {
            return;
        }

        // 整组时间戳
        Write.ParentTime = sendInput.ParentTime;
        // 物标识
        Write.DeviceName = sendInput.DeviceName;

        // 使用 ConcurrentDictionary 来存储处理结果
        ConcurrentDictionary<string, WriteParam> processedParams = new();

        // 使用 MaxDegreeOfParallelism 控制并行度
        ParallelOptions parallelOptions = new()
        {
            MaxDegreeOfParallelism = Environment.ProcessorCount
        };

        /// <summary>
        ///     处理单个属性数据
        ///     包含以下步骤：
        ///     1. 数据类型转换
        ///     2. 数值计算（倍率、基数）
        ///     3. 触发数据变更事件
        ///     4. 数据过滤（最大最小值）
        ///     5. 历史数据处理
        /// </summary>
        /// <param name="inputParam">输入的属性数据</param>
        /// <param name="processedParams">处理结果存储容器</param>
        /// <returns>处理是否成功</returns>
        await Parallel.ForEachAsync(sendInput.Params
                .Where(w => !string.IsNullOrEmpty(w.Key))
                .AsParallel(),
            parallelOptions,
            async (inputParam, token) =>
            {
                try
                {
                    if (await ProcessAttribute(inputParam, processedParams))
                    {
                        Write.Params = processedParams;
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"【解析实时属性】 实例:{Thing.Identification},属性:{inputParam.Key} Error：{e.Message}");
                }
            });

        // 更新全局存储的参数
        foreach (KeyValuePair<string, WriteParam> param in processedParams)
        {
            GlobalDeviceParams[param.Key] = param.Value;
        }

        // 缓存并分发数据
        await CacheAndDistributeData();
    }

    /// <summary>
    ///     处理单个属性数据
    ///     包含以下步骤：
    ///     1. 数据类型转换
    ///     2. 数值计算（倍率、基数）
    ///     3. 触发数据变更事件
    ///     4. 数据过滤（最大最小值）
    ///     5. 历史数据处理
    /// </summary>
    /// <param name="inputParam">输入的属性数据</param>
    /// <param name="processedParams">处理结果存储容器</param>
    /// <returns>处理是否成功</returns>
    private async Task<bool> ProcessAttribute(KeyValuePair<string, ParamValue> inputParam,
        ConcurrentDictionary<string, WriteParam> processedParams)
    {
        string key = inputParam.Key;
        if (!_thingAttributesVariable.TryGetValue(key, out ModelAttributes? thingAttribute))
        {
            return false;
        }

        if (thingAttribute.AttributesValueSource != AttributesValueSourceEnum.Variable)
        {
            return false;
        }

        // 上报的值
        string value = inputParam.Value.Value;
        // 转换数据类型
        WriteParam writeParam = ConvertDataType(thingAttribute.DataType, value);
        // 更新时间戳
        writeParam.DateTime = inputParam.Value.Time;
        // 只对数值类型进行倍率和基数处理
        if (writeParam.DataType == WriteParamValueDataTypeEnum.Number)
        {
            try
            {
                decimal numberValue = Convert.ToDecimal(writeParam.Value);
                numberValue = ApplyMagnificationAndBase(thingAttribute, numberValue);
                writeParam.Value = RoundAndFormatNumber(numberValue, thingAttribute);
            }
            catch (Exception ex)
            {
                Log.Error($"数值转换失败: 属性[{key}], 值[{value}], 错误[{ex.Message}]");
                return false;
            }
        }

        await Task.Run(() =>
            OnDataChanged(new DataChangedEventArgs(Thing.Identification, key, value,
                inputParam.Value.Time, writeParam.DataType)));

        // 数据过滤
        if (!FilterMinMax(thingAttribute, ref writeParam))
        {
            return false;
        }

        if (!HistoricalData(thingAttribute, inputParam.Value))
        {
            return false;
        }

        processedParams.TryAdd(key, writeParam);
        return true;
    }

    /// <summary>
    ///     缓存并分发处理后的数据
    ///     1. 将数据存入内存缓存，提高后续访问速度
    ///     2. 通过消息中心发布数据，触发后续处理流程
    /// </summary>
    private async Task CacheAndDistributeData()
    {
        // 使用内存缓存优化
        _cacheService.Set(Thing.Identification, GlobalDeviceParams);
        _cacheService.Set("cache_" + Write.DeviceName, Write);
        // 异步发布消息
        await MessageCenter.PublishAsync(EventConst.ThingExampleData, Write);
    }

    /// <summary>
    ///     处理离线数据
    /// </summary>
    /// <param name="sendInput"></param>
    /// <returns></returns>
    private async Task OffLine(ELinkDevSendInput sendInput)
    {
        // 处理属性
        if (_thingAttributesVariable.Count == 0)
        {
            return;
        }

        // 组装数据格式,将数据发送到事件中去
        WriteTDengIne writeTd = new()
        {
            Params = new ConcurrentDictionary<string, WriteParam>(),
            Uuid = Thing.Model.Uuid,
            ParentTime = sendInput.ParentTime,
            DeviceName = sendInput.DeviceName
        };

        // 处理属性
        await Parallel.ForEachAsync(sendInput.Params
            .Where(w => !string.IsNullOrEmpty(w.Key))
            .ToList(), (inputParam, token) =>
        {
            try
            {
                string key = inputParam.Key;

                // 检查键是否存在于 _thingAttributesVariable
                if (!_thingAttributesVariable.ContainsKey(key))
                {
                    return ValueTask.CompletedTask;
                }

                ModelAttributes thingAttribute = _thingAttributesVariable[key];
                if (thingAttribute.AttributesValueSource != AttributesValueSourceEnum.Variable)
                {
                    return ValueTask.CompletedTask;
                }

                // 上报的值
                string value = inputParam.Value.Value;
                // 转换数据类型
                WriteParam writeParam = ConvertDataType(thingAttribute.DataType, value);
                // 设置时间戳
                writeParam.DateTime = inputParam.Value.Time;
                
                if (thingAttribute.DataType == DataTypeEnum.Number)
                {
                    decimal numberValue = Convert.ToDecimal(writeParam.Value);
                    numberValue = ApplyMagnificationAndBase(thingAttribute, numberValue);
                    writeParam.Value = RoundAndFormatNumber(numberValue, thingAttribute);
                }

                // 检查数据是否需要被过滤
                bool isFilter = FilterMinMax(thingAttribute, ref writeParam);
                if (!isFilter) // false 的情况数据丢弃
                {
                    return ValueTask.CompletedTask;
                }

                // 数据保存方式
                bool isSave = HistoricalData(thingAttribute, inputParam.Value);
                if (!isSave)
                {
                    return ValueTask.CompletedTask;
                }

                // 修复：将处理后的参数添加到 writeTd.Params 而不是 Write.Params
                writeTd.Params[key] = writeParam;
            }
            catch (Exception ex)
            {
                Log.Error($"【离线数据处理】 实例:{Thing.Identification},属性:{inputParam.Key} Error：{ex.Message}");
            }

            return ValueTask.CompletedTask;
        });

        // 仅写入时序库
        if (writeTd.Params.Any())
        {
            // 添加调试参数，标记为离线数据
            await _writeService.Write(writeTd);
        }
    }

    /// <summary>
    ///     根据平台配置设备属性转换
    /// </summary>
    /// <param name="dataType"></param>
    /// <param name="value">值</param>
    /// <returns></returns>
    private WriteParam ConvertDataType(DataTypeEnum dataType, string value)
    {
        WriteParam writeParam = new() { Value = value };
        writeParam.DataType = dataType switch
        {
            DataTypeEnum.String or DataTypeEnum.Array or DataTypeEnum.Binary or DataTypeEnum.Json => WriteParamValueDataTypeEnum.String,
            DataTypeEnum.Bool => WriteParamValueDataTypeEnum.Bool,
            DataTypeEnum.Number => WriteParamValueDataTypeEnum.Number,
            DataTypeEnum.Int => WriteParamValueDataTypeEnum.Number,
            _ => writeParam.DataType
        };
        return writeParam;
    }

    /// <summary>
    ///     处理倍率和基数
    /// </summary>
    /// <param name="modelAttribute"></param>
    /// <param name="numberValue"></param>
    /// <returns></returns>
    private decimal ApplyMagnificationAndBase(ModelAttributes modelAttribute, decimal numberValue)
    {
        // 连接变量 -处理倍率问题
        if (modelAttribute is { AttributesValueSource: AttributesValueSourceEnum.Variable, AttributesValueModel: not null })
        {
            // 倍率
            decimal magnification = modelAttribute.AttributesValueModel.Magnification == 0 ? 1 : modelAttribute.AttributesValueModel.Magnification;
            // 属性 = 倍率 * 连接变量 + 基数
            numberValue = (numberValue * magnification) + modelAttribute.AttributesValueModel.Base;
        }

        return numberValue;
    }

    /// <summary>
    ///     处理值保留位数
    /// </summary>
    /// <param name="numberValue"></param>
    /// <param name="modelAttribute"></param>
    /// <returns></returns>
    private string RoundAndFormatNumber(decimal numberValue, ModelAttributes modelAttribute)
    {
        decimal roundedValue = Math.Round(numberValue, (int)modelAttribute.RetentionMethod);
        return roundedValue.FormatDecimal();
    }

    #region 最大值/最小值过滤

    /// <summary>
    ///     过滤最大值/最小值
    /// </summary>
    /// <param name="modelAttribute"></param>
    /// <param name="writeParam"></param>
    /// <returns></returns>
    private bool FilterMinMax(ModelAttributes modelAttribute, ref WriteParam writeParam)
    {
        // 如果不是数值类型，直接返回 true
        if (writeParam.DataType != WriteParamValueDataTypeEnum.Number)
        {
            return true;
        }

        try
        {
            decimal doubleValue = Convert.ToDecimal(writeParam.Value);
            decimal min = modelAttribute.ThingAttributeFilter.Min;
            decimal max = modelAttribute.ThingAttributeFilter.Max;

            if (min == 0 && max == 0)
            {
                return true;
            }

            if (doubleValue < min)
            {
                return ApplyFilter(modelAttribute, min, modelAttribute.ThingAttributeFilter.MinFilterType, ref writeParam);
            }

            return doubleValue <= max || ApplyFilter(modelAttribute, max, modelAttribute.ThingAttributeFilter.MaxFilterType, ref writeParam);
        }
        catch (Exception ex)
        {
            Log.Error($"过滤值转换失败: 属性[{modelAttribute.Identification}], 值[{writeParam.Value}], 错误[{ex.Message}]");
            return false;
        }
    }

    /// <summary>
    /// </summary>
    /// <param name="modelAttribute"></param>
    /// <param name="limit"></param>
    /// <param name="filterType"></param>
    /// <param name="writeParam"></param>
    /// <returns></returns>
    private bool ApplyFilter(ModelAttributes modelAttribute, decimal limit, DeviceVariableFilterTypeEnum filterType, ref WriteParam writeParam)
    {
        switch (filterType)
        {
            case DeviceVariableFilterTypeEnum.Min:
            case DeviceVariableFilterTypeEnum.Max:
                writeParam.Value = limit.FormatDecimal();
                break;
            case DeviceVariableFilterTypeEnum.Set:
                writeParam.Value = modelAttribute.ThingAttributeFilter.SetMin.ToString();
                break;
            case DeviceVariableFilterTypeEnum.Cookie:
                DataStorage.PropertyStorage? data = DataStorage.Instance.GetData(modelAttribute.Model.Uuid, modelAttribute.Identification);
                if (data != null)
                {
                    writeParam.Value = data.Value;
                }

                break;
            case DeviceVariableFilterTypeEnum.Clear:
                return false;
        }

        return true;
    }

    #endregion

    /// <summary>
    ///     历史数据处理策略
    ///     根据不同的历史数据配置决定是否需要保存数据：
    ///     - Save/All: 保存所有数据
    ///     - Period: 按周期保存数据
    ///     - Change: 数据变化时保存
    ///     - No: 不保存历史数据
    /// </summary>
    /// <param name="modelAttribute">属性模型配置</param>
    /// <param name="paramValue">属性值</param>
    /// <returns>是否需要保存数据</returns>
    private bool HistoricalData(ModelAttributes modelAttribute, ParamValue paramValue)
    {
        string cacheKey = $"{Thing.Identification}_{modelAttribute.Identification}";

        switch (modelAttribute.HistoricalData)
        {
            case HistoricalDataEnum.Save:
            case HistoricalDataEnum.All:
                return true;

            case HistoricalDataEnum.Period:
            case HistoricalDataEnum.Change:
                if (!_memoryCache.TryGetValue(cacheKey, out DataStorage.PropertyStorage data))
                {
                    return true;
                }

                if (modelAttribute.HistoricalData == HistoricalDataEnum.Period &&
                    paramValue.Time - data.Time < modelAttribute.Period)
                {
                    return false;
                }

                if (modelAttribute.HistoricalData == HistoricalDataEnum.Change &&
                    paramValue.Value == data.Value)
                {
                    return false;
                }

                // 设置缓存选项，包括大小
                MemoryCacheEntryOptions cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSize(1) // 设置缓存项大小
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(5));
                // 更新缓存
                _memoryCache.Set(cacheKey, new DataStorage.PropertyStorage
                {
                    Value = paramValue.Value,
                    Time = paramValue.Time
                }, cacheEntryOptions);

                return true;

            case HistoricalDataEnum.No:
                return false;

            default:
                return true;
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    ///     设备状态改变
    /// </summary>
    /// <param name="status">当前状态</param>
    private void DeviceStatusChange(ExampleStatusEnum status)
    {
        switch (status)
        {
            // 上报状态是关机，内部状态在线
            case ExampleStatusEnum.OffLine when ExampleStatus == ExampleStatusEnum.OnLine:
                ExampleStatus = ExampleStatusEnum.OffLine; // 设置为关机状态
                DataStorage.Instance.deviceConnectStatus[Thing.Identification] = false;
                // 插入到时序库中
                _executeService.Insert(
                    $"INSERT INTO {Thing.Identification} USING {Thing.Model.Uuid} TAGS('{Thing.Identification}') (ts,`online_`) VALUES({Core.Extension.DateTime.ToLong(DateTime.UtcNow)},{false})");
                return;
            case ExampleStatusEnum.OnLine:
                ExampleStatus = ExampleStatusEnum.OnLine; // 设置为开机
                DataStorage.Instance.deviceConnectStatus[Thing.Identification] = true;
                break;
        }
    }

    /// <summary>
    ///     实时数据事件
    /// </summary>
    /// <param name="e"></param>
    protected virtual void OnDataChanged(DataChangedEventArgs e)
    {
        DataChanged?.Invoke(this, e);
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        _eventBusFactory.Unsubscribe(Thing.Identification);
        _deviceCloseTimer?.Dispose();
    }

    #endregion
}

/// <summary>
///     设备数据变化事件
/// </summary>
public class DataChangedEventArgs(string thingIdentId, string property, string value, long time, WriteParamValueDataTypeEnum dataType)
    : EventArgs
{
    /// <summary>
    ///     物实例标识
    /// </summary>
    public string ThingIdent { get; } = thingIdentId;

    /// <summary>
    ///     属性标识
    /// </summary>
    public string Property { get; } = property;

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; } = value;

    /// <summary>
    ///     读取时间戳
    /// </summary>
    public long Time { get; set; } = time;

    /// <summary>
    ///     数据类型
    /// </summary>
    public WriteParamValueDataTypeEnum DataType { get; set; } = dataType;
}