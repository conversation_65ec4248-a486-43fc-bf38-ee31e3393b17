namespace IotPlatform.ThingModel.Services;

/// <summary>
///     物模型-模板
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("物模型")]
public class ModelTemplateService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<Model> _model;

    /// <summary>
    ///     物模型-模板
    /// </summary>
    /// <param name="model"></param>
    public ModelTemplateService(ISqlSugarRepository<Model> model)
    {
        _model = model;
    }

    /// <summary>
    ///     物模型-模板列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/modelTemplate/list")]
    public async Task<List<ThingTemplateListOutput>> ThingTemplateList([FromQuery] ThingTemplateAddInput input)
    {
        List<Model>? thingPageOutput = await _model.AsQueryable()
            .Where(w => w.IsTemplate == true)
            .Where(w => w.Enable == true)
            .Includes(w => w.ModelClassify)
            .ToListAsync();

        thingPageOutput = thingPageOutput.Where(w => w.ModelClassify.Id == input.ThingClassifyId || w.ModelClassify.Pid == input.ThingClassifyId).ToList();
        return thingPageOutput.Adapt<List<ThingTemplateListOutput>>();
    }

    /// <summary>
    ///     物模型-生成模板
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelTemplate/add")]
    [UnitOfWork]
    public async Task ThingTemplateAdd(ThingTemplateAddModel input)
    {
        Model? model = await _model.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.ThingAttributes)
            .Includes(w => w.ThingLinkVariable)
            .Includes(w => w.ThingAlarm)
            .FirstAsync();
        if (model == null)
        {
            throw Oops.Oh("物模型已经被删除!");
        }

        // 物模型分类
        ModelClassify? thingClassify = await _model.AsSugarClient().Queryable<ModelClassify>().FirstAsync(f => f.Id == input.ThingClassifyId);
        if (thingClassify == null)
        {
            throw Oops.Oh("选中物模型分类已经被删除！");
        }

        // 检查该分类是否已经存在自定义中
        if (await _model.AsSugarClient().Queryable<ModelClassify>().AnyAsync(a => a.Name == thingClassify.Name && a.OriginType == OriginTypeEnum.Custom))
        {
            thingClassify = await _model.AsSugarClient().Queryable<ModelClassify>().FirstAsync(a => a.Name == thingClassify.Name && a.OriginType == OriginTypeEnum.Custom);
        }
        else
        {
            // 重置Id和类型，复制一份新的分类
            thingClassify.Id = YitIdHelper.NextId();
            thingClassify.OriginType = OriginTypeEnum.Custom;
            if (thingClassify.Pid > 0)
            {
                ModelClassify? pidThingClassify = await _model.AsSugarClient().Queryable<ModelClassify>().FirstAsync(f => f.Id == thingClassify.Pid);
                if (pidThingClassify != null)
                {
                    // 重置Id和类型，复制一份新的分类
                    pidThingClassify.Id = YitIdHelper.NextId();
                    pidThingClassify.OriginType = OriginTypeEnum.Custom;
                    thingClassify.Pid = pidThingClassify.Id;
                    // 添加自定义分类
                    await _model.AsSugarClient().Insertable(pidThingClassify).ExecuteCommandAsync();
                }
            }

            // 添加自定义分类
            await _model.AsSugarClient().Insertable(thingClassify).ExecuteCommandAsync();
        }

        // 生成一个新的物模型模板
        model.Uuid = RandomExtensions.GetRandomString(10, false);
        model.Id = YitIdHelper.NextId();
        model.IsTemplate = true;
        model.Name = input.Name;
        model.ThingClassifyId = thingClassify.Id;
        model.ModelClassify = thingClassify;
        model.Remark = input.Remark;

        foreach (ModelLinkVariable thingLinkVariable in model.ThingLinkVariable)
        {
            thingLinkVariable.Id = YitIdHelper.NextId();
            thingLinkVariable.ModelId = model.Id;
        }

        foreach (ModelAttributes thingAttributes in model.ThingAttributes)
        {
            thingAttributes.Id = YitIdHelper.NextId();
            thingAttributes.ModelId = model.Id;
        }


        await _model.AsSugarClient().InsertNav(model)
            .Include(w => w.ThingAttributes)
            .Include(w => w.ThingLinkVariable)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     物模型-模板删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelTemplate/delete")]
    public async Task ThingTemplateDelete(BaseId input)
    {
        Model? model = await _model.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        if (!model.IsTemplate)
        {
            throw Oops.Oh("暂不支持！");
        }

        await _model.AsSugarClient().DeleteNav(model)
            .Include(w => w.ThingAlarm)
            .Include(w => w.ThingAttributes)
            .ExecuteCommandAsync();
    }
}