using Microsoft.Extensions.Hosting;
using Systems.Core;

namespace IotPlatform.ThingModel.Services;

/// <summary>
///     物模型-属性
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("物模型")]
public class ThingAttributesService : IDynamicApiController, ITransient
{
    /// <summary>
    /// 自定义在线服务  
    /// </summary>
    private readonly ThingHostedService _customOnLine;
    /// <summary>
    /// 执行服务
    /// </summary>
    private readonly ExecuteService _executeService;
    /// <summary>
    /// 物模型-属性
    /// </summary>
    private readonly ISqlSugarRepository<ModelAttributes> _thingAttributes;
    /// <summary>
    /// 主机环境
    /// </summary>
    private readonly IHostEnvironment _hostEnvironment;
    /// <summary>
    /// 系统缓存服务
    /// </summary>
    private readonly SysCacheService _sysCacheService;
    /// <summary>
    /// 缓存键
    /// </summary>
    private const string CACHE_KEY = "authorize:";

    /// <summary>
    ///   构造函数
    /// </summary>
    /// <param name="thingAttributes"> 物模型-属性 </param>
    /// <param name="executeService"> 执行服务 </param>
    /// <param name="customOnLine"> 自定义在线服务 </param>
    /// <param name="hostEnvironment"> 主机环境 </param>
    /// <param name="sysCacheService"> 系统缓存服务 </param>
    public ThingAttributesService(ISqlSugarRepository<ModelAttributes> thingAttributes, ExecuteService executeService, ThingHostedService customOnLine,
     IHostEnvironment hostEnvironment, SysCacheService sysCacheService)
    {
        _thingAttributes = thingAttributes;
        _executeService = executeService;
        _customOnLine = customOnLine;
        _hostEnvironment = hostEnvironment;
        _sysCacheService = sysCacheService;
    }

    /// <summary>
    ///     物模型属性-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/modelAttributes/page")]
    public async Task<SqlSugarPagedList<ThingAttributesPageOutput>> ThingAttributesPage([FromQuery] ThingAttributesInput input)
    {
        Expressionable<ModelAttributes>? expable = Expressionable.Create<ModelAttributes>()
            .And(w => w.ModelId == input.ModelId)
            .AndIF(input.ThingAttributeName.Any(), w => input.ThingAttributeName.Contains(w.Identification))
            .AndIF(input.DataType.Any(), w => input.DataType.Contains((int)w.DataType))
            .AndIF(input.AttributesValueSource.Any(), w => input.AttributesValueSource.Contains((int)w.AttributesValueSource))
            .AndIF(input.SearchValue.IsNotEmptyOrNull() && input.PreciseSearch == false, u => u.Name.Contains(input.SearchValue) || u.Id.ToString().Contains(input.SearchValue)
                                                                                                                                 || u.Identification.ToString().Contains(input.SearchValue))
            .AndIF(input.SearchValue.IsNotEmptyOrNull() && input.PreciseSearch, u => u.Name == input.SearchValue || u.Id.ToString() == input.SearchValue
                                                                                                                 || u.Identification.ToString() == input.SearchValue);
        if (input.Tags.Any())
        {
            bool first = true;
            foreach (string tag in input.Tags)
            {
                if (first)
                {
                    expable.And(c => SqlFunc.JsonArrayAny(c.Tags, tag));
                    first = false;
                }
                else
                {
                    expable.Or(c => SqlFunc.JsonArrayAny(c.Tags, tag));
                }
            }
        }

        Expression<Func<ModelAttributes, bool>>? exp = expable.ToExpression(); //要用变量 var exp=
        SqlSugarPagedList<ThingAttributesPageOutput>? thingPageOutput = await _thingAttributes.AsQueryable().Where(exp)
            .OrderByIF(!string.IsNullOrEmpty(input.Field), input.Field + " " + input.Order)
            .Select<ThingAttributesPageOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return thingPageOutput;
    }

    /// <summary>
    ///     物模型属性-标签下拉框
    /// </summary>
    /// <returns></returns>
    [HttpGet("/modelAttributes/tag/select")]
    public async Task<List<string>> ThingAttributesTagSelect([FromQuery] BaseId input)
    {
        List<string> tags = new();
        List<ModelAttributes>? thingAttributeList = await _thingAttributes.AsQueryable()
            .Where(w => w.ModelId == input.Id)
            .ToListAsync();
        foreach (string tag in thingAttributeList.Where(thingAttribute => thingAttribute.Tags.Any()).SelectMany(thingAttribute => thingAttribute.Tags.Where(tag => !tags.Contains(tag))))
        {
            tags.Add(tag);
        }

        return tags;
    }


    /// <summary>
    ///     物模型属性-下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/modelAttributes/select")]
    public async Task<List<ThingAttributesSelectOutput>> ThingAttributesSelect([FromQuery] ThingAttributesSelectInput input)
    {
        Expressionable<ModelAttributes>? expable = Expressionable.Create<ModelAttributes>()
            .And(w => w.ModelId == input.ModelId);
        if (input.Tags.Any())
        {
            bool first = true;
            foreach (string tag in input.Tags)
            {
                if (first)
                {
                    expable.And(c => SqlFunc.JsonArrayAny(c.Tags, tag));
                    first = false;
                }
                else
                {
                    expable.Or(c => SqlFunc.JsonArrayAny(c.Tags, tag));
                }
            }
        }

        Expression<Func<ModelAttributes, bool>>? exp = expable.ToExpression(); //要用变量 var exp=
        List<ThingAttributesSelectOutput>? thingPageOutput = await _thingAttributes.AsQueryable().Where(exp).Select<ThingAttributesSelectOutput>()
            .ToListAsync();
        return thingPageOutput;
    }

    /// <summary>
    ///     物模型属性-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/modelAttributes/detail")]
    public async Task<ModelAttributes> ThingAttributesDetail([FromQuery] BaseId input)
    {
        // 物模型属性
        ModelAttributes? thingAttribute = await _thingAttributes.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.Model)
            .FirstAsync();
        if (thingAttribute == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        return thingAttribute;
    }

    /// <summary>
    ///     物模型属性-新增
    /// </summary>
    /// <returns></returns>
    [HttpPost("/modelAttributes/add")]
    [UnitOfWork]
    public async Task ThingAttributesAdd(ThingAttributesAddInput input)
    {
        await CheckAuthorization();

        bool isExist = await _thingAttributes.IsAnyAsync(u => (u.Name == input.Name || u.Identification == input.Identification) && u.ModelId == input.ModelId);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        Model? thingModel = await _thingAttributes.AsSugarClient().Queryable<Model>().FirstAsync(f => f.Id == input.ModelId);
        if (thingModel == null)
        {
            throw Oops.Oh("物模型已经被删除！");
        }

        ModelAttributes thingAttribute = input.Adapt<ModelAttributes>();

        thingAttribute.Id = YitIdHelper.NextId();
        await _thingAttributes.InsertAsync(thingAttribute);
        // 连接变量不填则默认属性Id
        if (input is { AttributesValueSource: AttributesValueSourceEnum.Variable, AttributesValueModel: not null } && input.AttributesValueModel.ThingLinkVariableName.IsNullOrEmpty())
        {
            input.AttributesValueModel.ThingLinkVariableName = input.Identification;
        }

        // 双向绑定属性变量连接
        if (input is { AttributesValueSource: AttributesValueSourceEnum.Variable, AttributesValueModel: not null } && input.AttributesValueModel.ThingLinkVariableName.IsNotEmptyOrNull())
        {
            ModelLinkVariable? thingLinkVariable = await _thingAttributes.AsSugarClient().Queryable<ModelLinkVariable>()
                .FirstAsync(f => f.Name == input.AttributesValueModel.ThingLinkVariableName && f.ModelId == input.ModelId);
            if (thingLinkVariable != null)
            {
                if (thingLinkVariable.ModelAttributesId > 0)
                {
                    throw Oops.Oh("连接变量名称重复！");
                }

                thingLinkVariable.ModelAttributesId = thingAttribute.Id;
                await _thingAttributes.AsSugarClient().Updateable(thingLinkVariable).ExecuteCommandAsync();
            }
            else
            {
                await _thingAttributes.AsSugarClient().Insertable(new ModelLinkVariable
                {
                    Id = YitIdHelper.NextId(),
                    Name = input.AttributesValueModel.ThingLinkVariableName,
                    ModelId = input.ModelId,
                    ModelAttributesId = thingAttribute.Id
                }).ExecuteCommandAsync();
            }
        }

        // 已经发布过了就修改
        if (thingModel.Enable)
        {
            // 操作修改时序库
            string sql = $"ALTER STABLE {thingModel.Uuid} ADD COLUMN {thingAttribute.Identification} ";
            switch (thingAttribute.DataType)
            {
                case DataTypeEnum.Number:
                case DataTypeEnum.Int:
                    sql += "float;";
                    break;
                case DataTypeEnum.Bool:
                case DataTypeEnum.Binary:
                case DataTypeEnum.Json:
                case DataTypeEnum.Array:
                case DataTypeEnum.String:
                default:
                    sql += $"nchar({(thingAttribute.Length == 0 ? 64 : thingAttribute.Length)});";
                    break;
            }

            // 执行SQL信息
            _executeService.ExecuteCommand(sql);

            // 重启实例
            await RestartCustom(input.ModelId);
        }
    }

    /// <summary>
    ///     根据模型Id-重启已经发布过的物实例
    /// </summary>
    /// <param name="modelId"></param>
    private async Task RestartCustom(long modelId)
    {
        // 操作修改
        List<ModelThing>? thingList = await _thingAttributes.AsSugarClient().Queryable<ModelThing>()
            .Where(f => f.ModelId == modelId)
            .Includes(w => w.Model, w => w.ThingAlarm)
            .Includes(w => w.Model, w => w.ThingAttributes)
            .ToListAsync();
        foreach (ModelThing? thing in thingList)
        {
            await _customOnLine.AddDevice(thing);
        }
    }

    /// <summary>
    ///     物模型属性-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelAttributes/update")]
    [UnitOfWork]
    public async Task ThingAttributesUpdate(ThingAttributesUpdateInput input)
    {
        bool isExist = await _thingAttributes.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id && u.ModelId == input.ModelId);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        ModelAttributes? thingAttribute = await _thingAttributes.GetFirstAsync(f => f.Id == input.Id);
        if (thingAttribute == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        var release = thingAttribute.Release;
        if (thingAttribute.DataType != input.DataType || thingAttribute.Identification != input.Identification || thingAttribute.Length != input.Length)
            release = false;

        thingAttribute = input.Adapt<ModelAttributes>();
        thingAttribute.Release = release;
        string thingLinkVariableNameInput = input.AttributesValueModel?.ThingLinkVariableName ?? "";
        if (input is { AttributesValueSource: AttributesValueSourceEnum.Variable, AttributesValueModel: not null } && thingLinkVariableNameInput.IsNotEmptyOrNull() &&
            thingAttribute.AttributesValueModel != null && thingLinkVariableNameInput != thingAttribute.AttributesValueModel.ThingLinkVariableName)
        {
            ModelLinkVariable? thingLinkVariable = await _thingAttributes.AsSugarClient().Queryable<ModelLinkVariable>()
                .FirstAsync(f => f.Name == thingLinkVariableNameInput && f.ModelId == input.ModelId);
            if (thingLinkVariable != null)
            {
                thingLinkVariable.ModelAttributesId = thingAttribute.Id;
                await _thingAttributes.AsSugarClient().Updateable(thingLinkVariable).ExecuteCommandAsync();
            }

            ModelLinkVariable? oldThingLinkVariable = await _thingAttributes.AsSugarClient().Queryable<ModelLinkVariable>()
                .FirstAsync(f => f.Name == thingAttribute.AttributesValueModel.ThingLinkVariableName && f.ModelId == input.ModelId);
            if (oldThingLinkVariable != null)
            {
                oldThingLinkVariable.ModelAttributesId = 0;
                await _thingAttributes.AsSugarClient().Updateable(oldThingLinkVariable).ExecuteCommandAsync();
            }
        }

        await _thingAttributes.UpdateAsync(thingAttribute);
        // 重启实例
        await RestartCustom(input.ModelId);
    }

    /// <summary>
    ///     物模型属性-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelAttributes/delete")]
    public async Task ThingAttributesDelete(BaseId<List<long>> input)
    {
        // 物模型-属性集合
        List<ModelAttributes>? thingAttributeList = await _thingAttributes.AsQueryable().Where(f => input.Id.Contains(f.Id)).ToListAsync();
        foreach (ModelAttributes? thingAttribute in thingAttributeList)
        {
            await _thingAttributes.DeleteAsync(thingAttribute);
            if (thingAttribute.AttributesValueSource == AttributesValueSourceEnum.Variable && thingAttribute.AttributesValueModel != null &&
                thingAttribute.AttributesValueModel.ThingLinkVariableName.IsNotEmptyOrNull())
            {
                ModelLinkVariable? thingLinkVariable = await _thingAttributes.AsSugarClient().Queryable<ModelLinkVariable>()
                    .FirstAsync(f => f.Name == thingAttribute.AttributesValueModel.ThingLinkVariableName && f.ModelId == thingAttribute.ModelId);
                if (thingLinkVariable != null)
                {
                    await _thingAttributes.AsSugarClient().Deleteable(thingLinkVariable).ExecuteCommandAsync();
                }
            }

            Model? thingModel = await _thingAttributes.AsSugarClient().Queryable<Model>()
                .Where(w => w.Id == thingAttribute.ModelId)
                .Includes(w => w.ThingAttributes)
                .FirstAsync();
            if (thingModel is not { Enable: true })
            {
                continue;
            }

            // 属性就只删除列
            if (thingModel.ThingAttributes.Count > 1)
            {
                string sql = $"ALTER STABLE {thingModel.Uuid} DROP COLUMN {thingAttribute.Identification};";
                _executeService.ExecuteCommand(sql);
            }
            else
            {
                // 最后一个属性就把整张表删掉
                string sql = $"DROP STABLE IF EXISTS  {thingModel.Uuid};";
                _executeService.ExecuteCommand(sql);
            }

            // 重启实例
            await RestartCustom(thingModel.Id);
        }
    }

    /// <summary>
    ///     导入设备属性
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelAttributes/edge/inPort/")]
    [UnitOfWork]
    public async Task<ExportOutput> EdgeInPort([FromForm] EdgeInPortInput input)
    {
        // 检查授权状态
        await CheckAuthorization();

        // 物模型
        Model? model = await _thingAttributes.AsSugarClient().Queryable<Model>()
            .Where(w => w.Id == input.ModelId)
            .Includes(w => w.ThingAttributes)
            .FirstAsync();
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 获取机器码和授权信息
        string machineCodePath = Path.Combine(_hostEnvironment.ContentRootPath, "machine.code");
        string fingerprint = await System.IO.File.ReadAllTextAsync(machineCodePath);
        var authorizeInfo = _sysCacheService.Get<SetAuthorizeOutput>(CACHE_KEY + fingerprint);

        // 计算导入后的总变量数量
        int currentVariableCount = await _thingAttributes.AsSugarClient()
            .Queryable<ModelAttributes>()
            .CountAsync();

        MemoryStream stream = new();
        await input.File.CopyToAsync(stream);
        var importItems = await stream.QueryAsync<DeviceVariableInPortDto>(startCell: "A2");

        // 检查导入后是否超出授权限制
        if (currentVariableCount + importItems.Count() > authorizeInfo.VariableNumber)
        {
            throw Oops.Oh($"导入后变量总数({currentVariableCount + importItems.Count()})将超出授权上限({authorizeInfo.VariableNumber})");
        }

        // 返回结果
        ExportOutput result = new() { ErrorColumn = new List<ErrorColumn>() };
        // 属性
        List<ModelAttributes> thingAttributeList = new();
        List<ModelLinkVariable> modelLinkVariableList = new();
        // 当前所在行
        int line = 2;
        foreach (DeviceVariableInPortDto? inPort in await stream.QueryAsync<DeviceVariableInPortDto>(startCell: "A2"))
        {
            try
            {
                ModelAttributes thingAttribute = new()
                {
                    Id = YitIdHelper.NextId(),
                    ModelId = model.Id,
                    Tags = inPort.Tag.IsNotEmptyOrNull() ? JSON.Deserialize<List<string>>(inPort.Tag) : new List<string>(),
                    Name = inPort.Name,
                    AttributesValueSource = AttributesValueSourceEnum.Variable,
                    AttributesValueModel = new AttributesValueModel
                    {
                        ThingLinkVariableName = inPort.Identifier,
                        Base = 0,
                        Magnification = 1
                    },
                    TriggerMethod = TriggerMethodEnum.All,
                    Identification = inPort.Identifier,
                    Length = (int)(!string.IsNullOrEmpty(inPort.LengthEx) ? Convert.ToUInt32(inPort.LengthEx) : 0),
                    DataType = inPort.TransitionType.ToLower() == "int" ? DataTypeEnum.Int :
                        inPort.TransitionType.ToLower() == "bool" ? DataTypeEnum.Bool :
                        inPort.TransitionType.ToLower() == "double" ? DataTypeEnum.Number :
                        DataTypeEnum.String,
                    HistoricalData = HistoricalDataEnum.Save,
                    RetentionMethod = RetentionMethodEnum.Three,
                    Desc = inPort.Description,
                    ReadType = ReadTypeEnum.Read,
                    Custom = inPort.Custom,
                    ThingAttributeFilter = JSON.Deserialize<ThingAttributeFilter>(inPort.DeviceVariableFilterExtend),
                    Internal = false,
                    Period = 0,
                    AttributesScriptModel = null,
                    AttributesWriteModel = null,
                    Unit = inPort.Unit
                };

                modelLinkVariableList.Add(new ModelLinkVariable
                {
                    Id = YitIdHelper.NextId(),
                    Name = inPort.Identifier,
                    Desc = inPort.Description,
                    ModelAttributesId = thingAttribute.Id,
                    ModelId = model.Id
                });
                thingAttributeList.Add(thingAttribute);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 Error:【{e.Message}】 " });
            }
        }

        // 同设备相同的标识符
        List<ModelAttributes> thingAttributeAny = model.ThingAttributes
            .Where(w => thingAttributeList.Select(s => s.Identification).Contains(w.Identification) && w.ModelId == input.ModelId)
            .ToList();

        foreach (ModelAttributes thingAttribute in thingAttributeAny)
        {
            ModelAttributes? thingAttributeVal = thingAttributeList.FirstOrDefault(f => f.Identification == thingAttribute.Identification);
            if (thingAttributeVal == null)
            {
                continue;
            }

            if (input.InPortType == DeviceVariableInPortTypeEnum.OverLook)
            {
                thingAttributeList.Remove(thingAttributeVal);
            }
            else
            {
                // 覆盖原始数据
                // 1.覆盖属性
                // 1.1 移除对象,修改Id后重新加入
                thingAttributeList.Remove(thingAttributeVal);
                thingAttributeVal.Id = thingAttribute.Id;
                thingAttributeList.Add(thingAttributeVal);
            }
        }

        // 属性
        if (thingAttributeList.Any())
        {
            await _thingAttributes.AsSugarClient().Storageable(thingAttributeList).ExecuteCommandAsync();
        }

        // 连接变量
        if (modelLinkVariableList.Any())
        {
            await _thingAttributes.AsSugarClient().Insertable(modelLinkVariableList).ExecuteCommandAsync();
        }

        result.SuccessCount = line - 1 - result.ErrorCount < 0 ? 0 : line - 2 - result.ErrorCount;
        // 重启实例
        await RestartCustom(model.Id);
        return result;
    }

    /// <summary>
    /// 检查授权状态
    /// </summary>
    private async Task CheckAuthorization()
    {
        // 获取机器码
        string machineCodePath = Path.Combine(_hostEnvironment.ContentRootPath, "machine.code");
        if (!File.Exists(machineCodePath))
        {
            throw Oops.Oh("系统未授权");
        }
        string fingerprint = await System.IO.File.ReadAllTextAsync(machineCodePath);

        // 获取授权信息
        var authorizeInfo = _sysCacheService.Get<SetAuthorizeOutput>(CACHE_KEY + fingerprint);
        if (authorizeInfo == null)
        {
            throw Oops.Oh("系统未授权");
        }

        // 检查授权是否过期
        if (!string.IsNullOrEmpty(authorizeInfo.EndTime) &&
            DateTime.Now >= Convert.ToDateTime(authorizeInfo.EndTime))
        {
            throw Oops.Oh("授权已过期");
        }

        // 检查变量数量
        int currentVariableCount = await _thingAttributes.AsSugarClient()
            .Queryable<ModelAttributes>()
            .CountAsync();

        if (currentVariableCount >= authorizeInfo.VariableNumber)
        {
            throw Oops.Oh($"变量数量已达到授权上限({authorizeInfo.VariableNumber})");
        }
    }
}