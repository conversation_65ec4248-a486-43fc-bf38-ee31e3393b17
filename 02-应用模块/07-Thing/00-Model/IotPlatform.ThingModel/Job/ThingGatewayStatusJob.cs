using IotPlatform.ThingModel.Services;

namespace IotPlatform.ThingModel.Job;

/// <summary>
///     同步网关实例在线状态
/// </summary>
[JobDetail("ThingGatewayStatus", "同步网关实例在线状态", GroupName = "物实例同步")]
[Cron("0/30 * * ? * *", CronStringFormat.WithSeconds, TriggerId = "ThingGatewayStatus")]
public class ThingGatewayStatusJob : IJob
{
    private readonly ILogger<ThingGatewayStatusJob> _logger;
    private readonly IServiceScopeFactory _scopeFactory;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="scopeFactory"></param>
    public ThingGatewayStatusJob(ILogger<ThingGatewayStatusJob> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="stoppingToken"></param>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        var taskFactory = new TaskFactory(TaskScheduler.Current);
        await taskFactory.StartNew(async () =>
        {
            try
            {
                using var scope = _scopeFactory.CreateScope();
                var services = scope.ServiceProvider;
                await services.GetService<ThingService>()!.ThingExampleGatewayStatus();
            }
            catch (Exception ex)
            {
                _logger.LogError($"【物实例同步】 网关在线状态 Error:【{ex.Message}】");
            }
        }, stoppingToken);
    }
}