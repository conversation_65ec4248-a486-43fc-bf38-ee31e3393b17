namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型属性列表返回
/// </summary>
public class ThingAttributesPageOutput
{
    /// <summary>
    ///     Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     属性ID
    /// </summary>
    public string Identification { get; set; }

    /// <summary>
    ///     数据类型：1：String;2:Number;3:Bool;4:Int;5:Binary;6:Json;7:Array
    /// </summary>
    [JsonIgnore]
    public DataTypeEnum DataType { get; set; }

    /// <summary>
    ///     数据类型：1：String;2:Number;3:Bool;4:Int;5:Binary;6:Json;7:Array
    /// </summary>
    public string DataTypeName => DataType.GetDescription();

    /// <summary>
    ///     属性值来源:1:连接变量；2：规则指定；3：手动写值
    /// </summary>
    [JsonIgnore]
    public AttributesValueSourceEnum AttributesValueSource { get; set; }

    /// <summary>
    ///     属性值来源:1:连接变量；2：规则指定；3：手动写值
    /// </summary>
    public string AttributesValueSourceName => AttributesValueSource.GetDescription();

    /// <summary>
    ///     读写操作设置：1：读写；2：只读；3：只写
    /// </summary>
    [SugarColumn(ColumnDescription = "读写操作设置：1：读写；2：只读；3：只写")]
    [JsonIgnore]
    public ReadTypeEnum ReadType { get; set; }

    /// <summary>
    ///     读写操作设置：1：读写；2：只读；3：只写
    /// </summary>
    [SugarColumn(ColumnDescription = "读写操作设置：1：读写；2：只读；3：只写")]
    public string ReadTypeName => ReadType.GetDescription();

    /// <summary>
    ///     属性值配置
    /// </summary>
    [SugarColumn(ColumnDescription = "属性值配置", ColumnDataType = "longtext,text,clob")]
    [JsonIgnore]
    public string AttributesValueConfig { get; set; }

    /// <summary>
    ///     属性值配置
    /// </summary>
    public AttributesValueModel AttributesValueModel { get; set; }

    /// <summary>
    ///     属性标签
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<string> Tags { get; set; }

    /// <summary>
    ///     为属性值添加自定义
    /// </summary>
    public string Custom { get; set; }
}

/// <summary>
///     物模型-属性下拉返回
/// </summary>
public class ThingAttributesSelectOutput
{
    /// <summary>
    ///     Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     属性ID
    /// </summary>
    public string Identification { get; set; }

    /// <summary>
    ///     数据类型：1：String;2:Number;3:Bool;4:Int;5:Binary;6:Json;7:Array
    /// </summary>
    public DataTypeEnum DataType { get; set; }
}