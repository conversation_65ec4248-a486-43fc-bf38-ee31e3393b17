namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-属性-连接变量--新增
/// </summary>
public class ThingLinkVariableInput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Desc { get; set; }
}

/// <summary>
///     物模型-属性-连接变量-列表请求
/// </summary>
public class ThingLinkVariablePageInput : BasePageInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }
}

/// <summary>
///     物模型-属性-连接变量-下拉请求
/// </summary>
public class ThingLinkVariableSelectInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }
}

/// <summary>
///     物模型-属性-连接变量--修改
/// </summary>
public class ThingLinkVariableUpdateInput : ThingLinkVariableInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}