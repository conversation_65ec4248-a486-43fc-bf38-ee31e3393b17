namespace IotPlatform.Thing.Entity;

/// <summary>
///     趋势分析表
/// </summary>
[SugarTable("business_trendAnalysis", "趋势分析表")]
public class TrendAnalysis : EntityTenant
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     元数据配置
    /// </summary>
    [SugarColumn(ColumnDescription = "元数据配置", ColumnDataType = "longtext,text,clob")]
    public string Metadata { get; set; }
}