namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-分组--新增
/// </summary>
public class ThingGroupInput
{
    /// <summary>
    ///     分组名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     父级
    /// </summary>
    [Required]
    public long ParentId { get; set; }

    /// <summary>
    ///     1.物模型；2物实例
    /// </summary>
    [Required]
    public ThingGroupTypeEnum ThingGroupType { get; set; }
}

/// <summary>
///     物模型-分组--修改
/// </summary>
public class ThingGroupUpdateInput : ThingGroupInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}

/// <summary>
/// </summary>
public class ThingGroupTreeInput
{
    /// <summary>
    ///     1.物模型；2物实例
    /// </summary>
    [Required]
    public ThingGroupTypeEnum ThingGroupType { get; set; }
}