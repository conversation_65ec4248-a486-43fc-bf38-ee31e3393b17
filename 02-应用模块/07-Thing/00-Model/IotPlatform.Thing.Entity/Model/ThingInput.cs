using IotPlatform.Thing.Entity;

namespace IotPlatform.ThingModel.Entity.Dto;

/// <summary>
///     物实例-新增
/// </summary>
public class ThingAddInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     实例名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     物标识
    /// </summary>
    [Required]
    public string Identification { get; set; }

    /// <summary>
    ///     设备类型配置
    /// </summary>
    public DeviceExampleModelAdd? DeviceExampleModel { get; set; }

    /// <summary>
    ///     网关类型配置
    /// </summary>
    public GatewayExampleModel? GatewayExampleModel { get; set; }

    /// <summary>
    ///     关联网关Id
    /// </summary>
    public long ThingId { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public List<string> Tags { get; set; }
}

/// <summary>
///     设备类型配置新增
/// </summary>
public class DeviceExampleModelAdd
{
    /// <summary>
    ///     连网方式:1:直接连接：2；通过网关连接
    /// </summary>
    public ExampleConnectTypeEnum ExampleConnectType { get; set; }

    /// <summary>
    ///     直接连接配置
    /// </summary>
    public DeviceExampleConnectModel? DeviceExampleConnectModel { get; set; }

    /// <summary>
    ///     通过网关连接配置
    /// </summary>
    public DeviceExampleGatewayModel? DeviceExampleGatewayModel { get; set; }
}

/// <summary>
///     物实例-列表请求
/// </summary>
public class ThingExamplePageInput : BasePageInput
{
    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [Required]
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     实例状态:0 全部；1:在线；2：离线
    /// </summary>
    public int ExampleStatus { get; set; }

    /// <summary>
    ///     精确搜索
    /// </summary>
    public bool PreciseSearch { get; set; }

    /// <summary>
    ///     物模型Id
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    ///     物实例分组Id
    /// </summary>
    public long ThingGroupId { get; set; }

    /// <summary>
    ///     关联网关Id
    /// </summary>
    public long ThingByGatewayId { get; set; }

    /// <summary>
    ///     报警状态 0：全部；1：已禁用；2：已抑制
    /// </summary>
    public int Restrain { get; set; }
}

/// <summary>
///     物实例-修改标签
/// </summary>
public class ThingExampleSetTagsInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public List<string> Tags { get; set; }
}

/// <summary>
///     物实例-分组-修改
/// </summary>
public class ThingExampleUpdateGroupInput
{
    /// <summary>
    ///     分组id
    /// </summary>
    [Required]
    public long ThingGroupId { get; set; }

    /// <summary>
    ///     物实例Id
    /// </summary>
    [Required]
    public List<long> ThingId { get; set; }
}

/// <summary>
///     物实例下拉
/// </summary>
public class ThingSelectInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    ///     物模型类型：0全部；1;设备；2：复合物；3：网关
    /// </summary>
    public int ModelType { get; set; }
}

/// <summary>
///     物实例-位置定位请求参数
/// </summary>
public class ThingLocationInput : BaseId
{
    /// <summary>
    ///     位置信息
    /// </summary>
    [Required]
    public LocationModel Location { get; set; }
}

/// <summary>
///     物实例-修改sn码
/// </summary>
public class ThingUpdateSnInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     sn码
    /// </summary>
    [Required]
    public string Sn { get; set; }
}

/// <summary>
///     物实例-接入信息修改
/// </summary>
public class ThingUpdateConnectInput
{
    /// <summary>
    ///     物实例Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     设备类型配置
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true, ColumnDataType = "longtext,text,clob")]
    public DeviceExampleModel? DeviceExampleModel { get; set; }

    /// <summary>
    ///     网关类型配置
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true, ColumnDataType = "longtext,text,clob")]
    public GatewayExampleModel? GatewayExampleModel { get; set; }
}