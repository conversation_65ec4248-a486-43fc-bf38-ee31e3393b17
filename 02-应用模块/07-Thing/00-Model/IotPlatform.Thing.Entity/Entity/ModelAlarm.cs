using Furion.JsonSerialization;
using Newtonsoft.Json;

namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-报警表
/// </summary>
[SugarTable("business_modelAlarm", "物模型-报警表")]
public class ModelAlarm : EntityTenant
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     报警ID
    /// </summary>
    [SugarColumn(ColumnDescription = "报警ID", Length = 64)]
    public string Identification { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述")]
    public string Desc { get; set; }

    /// <summary>
    ///     报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定
    /// </summary>
    [SugarColumn(ColumnDescription = "报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定")]
    public AlarmLevelEnum AlarmLevel { get; set; }

    /// <summary>
    ///     报警原因
    /// </summary>
    [SugarColumn(ColumnDescription = "报警原因", ColumnDataType = "longtext,text,clob")]
    public string AlarmReason { get; set; }

    /// <summary>
    ///     解决方案
    /// </summary>
    [SugarColumn(ColumnDescription = "解决方案", ColumnDataType = "longtext,text,clob")]
    public string Solution { get; set; }

    /// <summary>
    ///     与报警同时上报的属性值
    /// </summary>
    [SugarColumn(ColumnDescription = "与报警同时上报的属性值", IsJson = true)]
    public List<string> ModelAttributesId { get; set; }

    /// <summary>
    ///     报警标签
    /// </summary>
    [SugarColumn(ColumnDescription = "报警标签", IsJson = true)]
    public List<string> Tags { get; set; }

    /// <summary>
    ///     自定义AlarmTypeId
    /// </summary>
    [SugarColumn(ColumnDescription = "自定义AlarmTypeId")]
    public bool CustomAlarmTypeIdBool { get; set; }

    /// <summary>
    ///     自定义AlarmTypeId
    /// </summary>
    [SugarColumn(ColumnDescription = "自定义AlarmTypeId")]
    public string CustomAlarmTypeId { get; set; }

    /// <summary>
    ///     报警触发规则:1简单规则；2多条件规则；3自定义规则
    /// </summary>
    [SugarColumn(ColumnDescription = "报警触发规则:1简单规则；2多条件规则；3自定义规则")]
    public AlarmRulesTypeEnum AlarmRuleType { get; set; }

    /// <summary>
    ///     设置报警触发规则内容
    /// </summary>
    [SugarColumn(ColumnDescription = "设置报警触发规则内容", ColumnDataType = "longtext,text,clob")]
    public string Config { get; set; }

    /// <summary>
    ///     报警条件表达式
    /// </summary>
    [SugarColumn(ColumnDescription = "报警条件表达式")]
    public string Expression { get; set; }

    /// <summary>
    ///     报警规则最小执行间隔(毫秒)
    /// </summary>
    [SugarColumn(ColumnDescription = "报警规则最小执行间隔(毫秒)")]
    public bool IntervalTimeBool { get; set; }

    /// <summary>
    ///     报警规则最小执行间隔(毫秒)
    /// </summary>
    [SugarColumn(ColumnDescription = "报警规则最小执行间隔(毫秒)")]
    public int IntervalTime { get; set; }

    /// <summary>
    ///     设置延迟报警时间
    /// </summary>
    [SugarColumn(ColumnDescription = "设置延迟报警时间")]
    public short DelayTime { get; set; }

    /// <summary>
    ///     单位：秒；分钟；小时
    /// </summary>
    [SugarColumn(ColumnDescription = "单位：秒；分钟；小时", IsNullable = true)]
    public string? DelayTimeUnit { get; set; }

    /// <summary>
    ///     报警方式:1:仅报警一次；2：持续报警
    /// </summary>
    [SugarColumn(ColumnDescription = "报警方式:1:仅报警一次；2：持续报警")]
    public AlarmTypeEnum AlarmType { get; set; }

    /// <summary>
    ///     持续报警时间
    /// </summary>
    [SugarColumn(ColumnDescription = "持续报警时间")]
    public short ContinuousTime { get; set; }

    /// <summary>
    ///     持续报警单位
    /// </summary>
    [SugarColumn(ColumnDescription = "持续报警单位", IsNullable = true)]
    public string? ContinuousTimeUnit { get; set; }

    /// <summary>
    ///     报警触发后需手动确认
    /// </summary>
    [SugarColumn(ColumnDescription = "报警触发后需手动确认")]
    public bool AlarmHandDisarmed { get; set; }

    /// <summary>
    ///     报警解除规则:1自动解除；2简单规则；3多条件规则；4自定义规则
    /// </summary>
    [SugarColumn(ColumnDescription = "报警解除规则:1自动解除；2简单规则；3多条件规则；4自定义规则")]
    public AlarmDisarmedEnum AlarmDisarmed { get; set; }

    /// <summary>
    ///     设置报警解除规则配置
    /// </summary>
    [SugarColumn(ColumnDescription = "设置报警解除规则配置", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public string? AlarmDisarmedConfig { get; set; }
   
    /// <summary>
    ///     延迟解除报警时间
    /// </summary>
    [SugarColumn(ColumnDescription = "延迟解除报警时间")]
    public bool DisarmedDelayTimeBool { get; set; }

    /// <summary>
    ///     延迟解除报警时间值
    /// </summary>
    [SugarColumn(ColumnDescription = "延迟解除报警时间值")]
    public short DisarmedDelayTime { get; set; }

    /// <summary>
    ///     延迟解除报警时间单位
    /// </summary>
    [SugarColumn(ColumnDescription = "延迟解除报警时间单位", IsNullable = true)]
    public string DisarmedDelayTimeUnit { get; set; }

    /// <summary>
    ///     解除报警条件表达式
    /// </summary>
    [SugarColumn(ColumnDescription = "解除报警条件表达式", IsNullable = true)]
    public string? DisarmedExpression { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool Enable { get; set; }

    #region 忽略字段

    /// <summary>
    ///     报警表达式实时转义
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [System.Text.Json.Serialization.JsonIgnore]
    [Newtonsoft.Json.JsonIgnore]
    public string ExpressionNew { get; set; }
    
    /// <summary>
    ///     报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string AlarmLevelName => EnumExtensions.GetDescription(AlarmLevel);
    
    /// <summary>
    ///     设置报警触发规则-简单规则
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public AlarmRulesModel? SimpleAlarmRulesModel => AlarmRuleType == AlarmRulesTypeEnum.Simple ? JSON.Deserialize<AlarmRulesModel>(Config) : null;
    
    /// <summary>
    ///     设置报警触发规则-多条件规则
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<AlarmRulesModel>? AlarmRulesModel => AlarmRuleType == AlarmRulesTypeEnum.AlarmRules ? JSON.Deserialize<List<AlarmRulesModel>>(Config) : null;
    
    /// <summary>
    ///     设置报警解除规则-简单规则
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public AlarmRulesModel? SimpleAlarmDisarmedModel => AlarmDisarmed == AlarmDisarmedEnum.Simple ? JSON.Deserialize<AlarmRulesModel>(AlarmDisarmedConfig) : null;

    /// <summary>
    ///     设置报警解除规则-多条件规则
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<AlarmRulesModel>? AlarmDisarmedModel => AlarmDisarmed == AlarmDisarmedEnum.AlarmRules ? JSON.Deserialize<List<AlarmRulesModel>>(AlarmDisarmedConfig) : null;

    #endregion

    #region 关联表

    /// <summary>
    ///     物模型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelId))]
    public Model Model { get; set; }
    
    /// <summary>
    ///     报警记录
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ModelAlarmRecord.ModelAlarmId))]
    [SugarColumn(IsIgnore = true)]
    public List<ModelAlarmRecord> ThingAlarmRecord { get; set; }
    #endregion
}

/// <summary>
///     设置报警触发规则-非复杂规则
/// </summary>
public class AlarmRulesModel
{
    /// <summary>
    ///     属性标识符
    /// </summary>
    [Required]
    public string ThingAttributeName { get; set; }

    /// <summary>
    ///     符号：1：大于；2：小于；3：大于等于；4：小于等于 5：等于；6：不等于
    /// </summary>
    public SymbolEnum Symbol { get; set; }

    /// <summary>
    ///     规则触发：1：值；2：属性
    /// </summary>
    public AlarmRulesValueTypeEnum AlarmRulesValueType { get; set; }

    /// <summary>
    ///     值或者属性名称
    /// </summary>
    public string Value { get; set; }
}

/// <summary>
///     规则触发：1：值；2：属性
/// </summary>
public enum AlarmRulesValueTypeEnum
{
    /// <summary>
    ///     值
    /// </summary>
    [Description("值")] Value = 1,

    /// <summary>
    ///     属性
    /// </summary>
    [Description("属性")] Variable = 2
}

/// <summary>
///     符号：1：大于；2：小于；3：大于等于；4：小于等于 5：等于；6：不等于
/// </summary>
public enum SymbolEnum
{
    /// <summary>
    ///     大于
    /// </summary>
    [Description("大于")] Greater = 1,

    /// <summary>
    ///     小于
    /// </summary>
    [Description("小于")] Less = 2,

    /// <summary>
    ///     大于等于
    /// </summary>
    [Description("大于等于")] GreaterOrEqual = 3,

    /// <summary>
    ///     小于等于
    /// </summary>
    [Description("小于等于")] LessOrEqual = 4,

    /// <summary>
    ///     等于
    /// </summary>
    [Description("等于")] Equal = 5,

    /// <summary>
    ///     不等于
    /// </summary>
    [Description("不等于")] DoesNotEqual = 6
}

/// <summary>
///     报警触发规则:1简单规则；2多条件规则；3自定义规则
/// </summary>
public enum AlarmRulesTypeEnum
{
    /// <summary>
    ///     简单规则
    /// </summary>
    [Description("简单规则")] Simple = 1,

    /// <summary>
    ///     多条件规则
    /// </summary>
    [Description("多条件规则")] AlarmRules = 2,

    /// <summary>
    ///     自定义规则
    /// </summary>
    [Description("自定义规则")] Custom = 3
}

/// <summary>
///     报警解除规则:1自动解除；2简单规则；3多条件规则；4自定义规则
/// </summary>
public enum AlarmDisarmedEnum
{
    /// <summary>
    ///     自动解除
    /// </summary>
    [Description("自动解除")] Auto = 1,

    /// <summary>
    ///     简单规则
    /// </summary>
    [Description("简单规则")] Simple = 2,

    /// <summary>
    ///     多条件规则
    /// </summary>
    [Description("多条件规则")] AlarmRules = 3,

    /// <summary>
    ///     自定义规则
    /// </summary>
    [Description("自定义规则")] Custom = 4
}

/// <summary>
///     报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定
/// </summary>
public enum AlarmLevelEnum
{
    /// <summary>
    ///     紧急
    /// </summary>
    [Description("紧急")] Urgent = 1,

    /// <summary>
    ///     重要
    /// </summary>
    [Description("重要")] Important = 2,

    /// <summary>
    ///     警告
    /// </summary>
    [Description("警告")] Warning = 3,

    /// <summary>
    ///     一般
    /// </summary>
    [Description("一般")] General = 4,

    /// <summary>
    ///     不确定
    /// </summary>
    [Description("不确定")] NotSure = 5
}

/// <summary>
///     状态：1持续中；2自动解除；3手动解除
/// </summary>
public enum ThingAlarmRecordStatusEnum
{
    /// <summary>
    ///     持续中
    /// </summary>
    [Description("持续中")] Alarm = 1,

    /// <summary>
    ///     自动解除
    /// </summary>
    [Description("自动解除")] Auto = 2,

    /// <summary>
    ///     手动解除
    /// </summary>
    [Description("手动解除")] Hand = 3
}

/// <summary>
///     报警方式:1:仅报警一次；2：持续报警
/// </summary>
public enum AlarmTypeEnum
{
    /// <summary>
    ///     仅报警一次
    /// </summary>
    [Description("仅报警一次")] One = 1,

    /// <summary>
    ///     持续报警
    /// </summary>
    [Description("持续报警")] Always = 2
}