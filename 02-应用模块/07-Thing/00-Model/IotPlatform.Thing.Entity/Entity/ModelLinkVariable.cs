using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-属性-连接变量
/// </summary>
[SugarTable("business_modelLinkVariable", "物模型-属性-连接变量")]
public class ModelLinkVariable : EntityTenant
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    [Required]
    [MaxLength(64)]
    public string Name { get; set; }

    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    ///     物模型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelId))]
    public Model Model { get; set; }

    /// <summary>
    ///     关联物模型属性Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelAttributesId { get; set; }

    /// <summary>
    ///     关联物模型属性
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelAttributesId))]
    public ModelAttributes ModelAttributes { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", IsNullable = true)]
    public string? Desc { get; set; }
}