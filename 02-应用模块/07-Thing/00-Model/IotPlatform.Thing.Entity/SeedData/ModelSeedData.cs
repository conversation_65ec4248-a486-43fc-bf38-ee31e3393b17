namespace IotPlatform.Thing.Entity.SeedData;

/// <summary>
///     物模型种子数据
/// </summary>
public class ModelSeedData : ISqlSugarEntitySeedData<Model>
{
    public IEnumerable<Model> HasData()
    {
        Func<PropertyInfo, object, object?> valueProvider = (property, oldValue) =>
        {
            return property.Name switch
            {
                "Device" when oldValue != null => JsonSerializer.Deserialize<Device>(oldValue.ToString()),
                "Compound" when oldValue != null => JsonSerializer.Deserialize<Compound>(oldValue.ToString()),
                "Gateway" when oldValue != null => JsonSerializer.Deserialize<Gateway>(oldValue.ToString()),
                _ => oldValue
            };
        };
        IEnumerable<Model> model = SeedDataUtil.GetSeedData<Model>("business_model.json", valueProvider);
        return model;
    }
}