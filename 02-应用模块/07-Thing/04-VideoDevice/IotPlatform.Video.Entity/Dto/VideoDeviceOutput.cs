namespace IotPlatform.Video.Entity.Dto;

public class VideoDeviceOutput
{
    /// <summary>
    ///     通道Id
    /// </summary>
    public string channelId { get; set; }

    /// <summary>
    ///     通道名称
    /// </summary>
    public string channelName { get; set; }

    /// <summary>
    ///     视频地址
    /// </summary>
    public string videoUrl { get; set; }

    /// <summary>
    ///     用户名
    /// </summary>
    public string? userName { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    public string? password { get; set; }

    /// <summary>
    ///     安装地址
    /// </summary>
    public string? installAddr { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? description { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public long id { get; set; }
}