namespace IotPlatform.Video.Entity;

/// <summary>
///     视频设备
/// </summary>
[SugarTable("base_video_device")]
public class VideoDevice : EntityTenant
{
    /// <summary>
    ///     通道Id
    /// </summary>
    [SugarColumn(ColumnDescription = "通道Id")]
    public string ChannelId { get; set; }

    /// <summary>
    ///     通道名称
    /// </summary>
    [SugarColumn(ColumnDescription = "通道名称")]
    public string ChannelName { get; set; }

    /// <summary>
    ///     视频地址
    /// </summary>
    [SugarColumn(ColumnDescription = "视频地址", Length = 2048)]
    public string VideoUrl { get; set; }

    /// <summary>
    ///     用户名
    /// </summary>
    [SugarColumn(ColumnDescription = "用户名")]
    public string? UserName { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    [SugarColumn(ColumnDescription = "密码")]
    public string? Password { get; set; }

    /// <summary>
    ///     安装地址
    /// </summary>
    [SugarColumn(ColumnDescription = "安装地址")]
    public string? InstallAddr { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? Description { get; set; }
}