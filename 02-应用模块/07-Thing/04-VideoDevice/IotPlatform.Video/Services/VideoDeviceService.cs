using Common.Models;
using Extras.DatabaseAccessor.SqlSugar.Internal;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.FriendlyException;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using IotPlatform.Video.Entity;
using IotPlatform.Video.Entity.Dto;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SharpRTSPtoWebRTC.Model;
using SharpRTSPtoWebRTC.WebRTCProxy;
using SIPSorcery.Net;
using Yitter.IdGenerator;

namespace IotPlatform.Video.Services;

/// <summary>
///     视频设备
/// </summary>
[ApiDescriptionSettings("视频设备")]
public class VideoDeviceService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<VideoDevice> _videoDevice;

    private readonly RTSPtoWebRTCProxyService _webRTCServer;

    /// <summary>
    /// </summary>
    /// <param name="videoDevice"></param>
    public VideoDeviceService(ISqlSugarRepository<VideoDevice> videoDevice, RTSPtoWebRTCProxyService webRtcServer)
    {
        _videoDevice = videoDevice;
        _webRTCServer = webRtcServer;
    }

    /// <summary>
    ///     视频设备-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/videoDevice/page")]
    public async Task<dynamic> VideoDevicePage([FromQuery] PageInputBase input)
    {
        SqlSugarPagedList<VideoDeviceOutput> videoDevicePage = await _videoDevice.AsQueryable()
            .WhereIF(input.keyword.IsNotEmptyOrNull(), u => u.ChannelName == input.keyword || u.ChannelId.ToString() == input.keyword)
            .Select<VideoDeviceOutput>()
            .ToPagedListAsync(input.currentPage, input.pageSize);
        return videoDevicePage;
    }

    /// <summary>
    ///     视频设备-下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/videoDevice/select")]
    [AllowAnonymous]
    public async Task<dynamic> VideoDeviceSelect()
    {
        List<VideoDeviceOutput>? videoDevicePage = await _videoDevice.AsQueryable()
            .Select<VideoDeviceOutput>()
            .ToListAsync();
        return videoDevicePage;
    }

    /// <summary>
    ///     视频设备-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/videoDevice/{id}/detail")]
    public async Task<VideoDeviceOutput> VideoDeviceDetail(long id)
    {
        VideoDeviceOutput? videoDevice = await _videoDevice.AsQueryable().Where(w => w.Id == id).Select<VideoDeviceOutput>().FirstAsync();
        if (videoDevice == null)
        {
            throw Oops.Oh("数据已经被删除!");
        }

        return videoDevice;
    }

    /// <summary>
    ///     视频设备-添加
    /// </summary>
    /// <returns></returns>
    [HttpPost("/videoDevice/add")]
    public async Task VideoDeviceAdd(VideoDeviceAddInput input)
    {
        if (input.channelId.IsNullOrEmpty())
        {
            input.channelId = YitIdHelper.NextId().ToString();
        }

        bool isExist = await _videoDevice.IsAnyAsync(u => u.ChannelName == input.channelName);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        VideoDevice videoDevice = input.Adapt<VideoDevice>();
        await _videoDevice.InsertAsync(videoDevice);
    }

    /// <summary>
    ///     视频设备-修改
    /// </summary>
    /// <returns></returns>
    [HttpPost("/videoDevice/update")]
    public async Task VideoDeviceUpdate(VideoDeviceUpdateInput input)
    {
        // 检查标识唯一性
        bool isExist = await _videoDevice.IsAnyAsync(u => u.ChannelName == input.channelName && u.Id != input.id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        VideoDevice? videoDevice = await _videoDevice.GetFirstAsync(f => f.Id == input.id);
        if (videoDevice == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        videoDevice = input.Adapt<VideoDevice>();
        await _videoDevice.AsSugarClient().Updateable(videoDevice).IgnoreColumns(true).ExecuteCommandAsync();
    }

    /// <summary>
    ///     视频设备-删除
    /// </summary>
    /// <returns></returns>
    [HttpPost("/videoDevice/{id}/delete")]
    public async Task VideoDeviceDelete(long id)
    {
        VideoDevice? videoDevice = await _videoDevice.AsQueryable()
            .Where(f => f.Id == id).FirstAsync();
        if (videoDevice == null)
        {
            return;
        }

        await _videoDevice.AsSugarClient().Deleteable(videoDevice).ExecuteCommandAsync();
    }

    /// <summary>
    /// </summary>
    /// <param name="channelId"></param>
    /// <param name="uuid"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpGet("/videoDevice/{channelId}/getoffer")]
    [AllowAnonymous]
    public async Task<RTCSessionDescriptionInitEx> GetOffer(string channelId, string uuid)
    {
        VideoDevice? videoDevice = await _videoDevice.GetFirstAsync(f => f.ChannelId == channelId);
        if (videoDevice == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        return await _webRTCServer.GetOfferAsync(uuid, videoDevice.VideoUrl, videoDevice.UserName, videoDevice.Password);
    }

    /// <summary>
    /// </summary>
    /// <param name="answer"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/videoDevice/{uuid}/setanswer")]
    [AllowAnonymous]
    public async Task SetAnswer(string uuid, [FromBody] RTCSessionDescriptionInit answer)
    {
        if (string.IsNullOrWhiteSpace(answer?.sdp))
        {
            throw Oops.Oh("The SDP answer cannot be empty in SetAnswer.");
        }

        _webRTCServer.SetAnswer(uuid, answer);
    }

    /// <summary>
    /// </summary>
    /// <param name="uuid"></param>
    /// <param name="iceCandidate"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/videoDevice/{uuid}/addicecandidate")]
    [AllowAnonymous]
    public async Task AddIceCandidate(string uuid, [FromBody] RTCIceCandidateInit iceCandidate)
    {
        if (string.IsNullOrWhiteSpace(iceCandidate?.candidate))
        {
            throw Oops.Oh("The candidate field cannot be empty in AddIceCandidate.");
        }

        _webRTCServer.AddIceCandidate(uuid, iceCandidate);
    }
}