<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Concentus" Version="2.2.2" />
        <PackageReference Include="SharpJaad.AAC" Version="0.0.4" />
        <PackageReference Include="SharpRTSPClient" Version="0.0.5" />
        <PackageReference Include="SIPSorcery" Version="8.0.6" />
        <PackageReference Include="System.Net.Http" Version="4.3.4" />
        <PackageReference Include="System.Net.Security" Version="4.3.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj" />
    </ItemGroup>
</Project>
