<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\..\01-架构核心\Extras.DatabaseAccessor.SqlSugar\Extras.DatabaseAccessor.SqlSugar.csproj" />
      <ProjectReference Include="..\..\..\02-System\Systems.Entity\Systems.Entity.csproj" />
      <ProjectReference Include="..\..\00-Model\IotPlatform.Thing.Entity\IotPlatform.Thing.Entity.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="IotPlatform.Thing.Warning.Entity.csproj.DotSettings" />
    </ItemGroup>

</Project>
