namespace IotPlatform.Thing.Warning.Entity;

/// <summary>
///     报警消息
/// </summary>
public class ThingAlarmRecordPageInput : BasePageInput
{
    /// <summary>
    ///     类型；1：待处理报警；2报警历史
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    ///     物实例标识
    /// </summary>
    public string ThingIdent { get; set; }

    /// <summary>
    ///     报警状态：1持续中；2自动解除；3手动解除
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    ///     模型Id
    /// </summary>
    public long ModelId { get; set; }

    /// <summary>
    ///     实例Id
    /// </summary>
    public long ThingId { get; set; }

    /// <summary>
    ///     报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定
    /// </summary>
    public List<int> AlarmLevel { get; set; } = new();
}

/// <summary>
///     物模型-报警记录-详情-关联属性请求参数
/// </summary>
public class RelPropertiesInput
{
    /// <summary>
    ///     报警Id
    /// </summary>
    [Required]
    public long ModelAlarmId { get; set; }

    /// <summary>
    ///     报警记录Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}