namespace IotPlatform.Thing.Warning.Entity;

/// <summary>
///     物模型-报警设置表
/// </summary>
[SugarTable("business_alarmConf", "物模型-报警设置表")]
public class AlarmConf : EntityTenantId
{
    /// <summary>
    ///     单个实例单小时报警数量上限
    /// </summary>
    [SugarColumn(ColumnDescription = "单个实例单小时报警数量上限")]
    public int HourMax { get; set; }

    /// <summary>
    ///     解除抑制方式 1次日零点自动解除+手动解除;2次小时自动解除+手动解除
    /// </summary>
    [SugarColumn(ColumnDescription = "解除抑制方式 1次日零点自动解除+手动解除;2次小时自动解除+手动解除")]
    public CloseTypeEnum HourCloseType { get; set; }

    /// <summary>
    ///     单个实例当天报警数量上限
    /// </summary>
    [SugarColumn(ColumnDescription = "单个实例当天报警数量上限")]
    public int DayMax { get; set; }

    /// <summary>
    ///     解除抑制方式 1次日零点自动解除+手动解除;
    /// </summary>
    [SugarColumn(ColumnDescription = "解除抑制方式 1次日零点自动解除+手动解除;")]
    public CloseTypeEnum CloseType { get; set; }
}

/// <summary>
///     解除抑制方式 1次日零点自动解除+手动解除;2次小时自动解除+手动解除
/// </summary>
public enum CloseTypeEnum
{
    /// <summary>
    ///     次日零点自动解除+手动解除
    /// </summary>
    [Description("次日零点自动解除+手动解除")] Day = 1,

    /// <summary>
    ///     次小时自动解除+手动解除
    /// </summary>
    [Description("次小时自动解除+手动解除")] Hour = 2
}