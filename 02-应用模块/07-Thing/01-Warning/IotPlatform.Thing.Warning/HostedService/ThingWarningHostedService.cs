using Microsoft.Extensions.Hosting;
using Timer = System.Threading.Timer;

namespace IotPlatform.Thing.Warning.HostedService;

/// <summary>
///     报警初始化
/// </summary>
public class ThingWarningHostedService : IHostedService, IDisposable
{
    /// <summary>
    /// </summary>
    private readonly ISqlSugarClient _db;

    private readonly IEventBusFactory _eventBusFactory;

    /// <summary>
    ///     报警条件订阅
    /// </summary>
    public readonly ConcurrentDictionary<long, WarningCenterCustom> AlarmConditionThreads = new();

    /// <summary>
    ///     定时任务
    /// </summary>
    private Timer _timer;


    /// <summary>
    /// </summary>
    /// <param name="db"></param>
    /// <param name="eventBusFactory"></param>
    public ThingWarningHostedService(ISqlSugarClient db, IEventBusFactory eventBusFactory)
    {
        _db = db;
        _eventBusFactory = eventBusFactory;
    }

    /// <summary>
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _timer = new Timer(DoWork, null, TimeSpan.Zero, TimeSpan.FromMinutes(15)); // 每15分钟执行一次
        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    /// <param name="state"></param>
    private void DoWork(object? state)
    {
        try
        {
            // 初始化报警类型-报警条件
            List<AlarmCondition>? alarmConditionList = _db.Queryable<AlarmCondition>()
                .Includes(w => w.AlarmType, w => w.AlarmStrategy)
                .ToList();
            // 报警通知中心
            foreach (AlarmCondition alarmCondition in alarmConditionList.Where(alarmCondition => !AlarmConditionThreads.ContainsKey(alarmCondition.Id)))
            {
                try
                {
                    WarningCenterCustom alarmConditionThread = new(_db, _eventBusFactory)
                    {
                        AlarmCondition = alarmCondition
                    };
                    AlarmConditionThreads.TryAdd(alarmCondition.Id, alarmConditionThread);
                    Console.WriteLine($"··························[物实例-报警]启动报警:{alarmCondition.Id}································");
                    ThreadPool.QueueUserWorkItem(_ => alarmConditionThread.Start());
                }
                catch (Exception e)
                {
                    Log.Error($"【物实例-报警】 启动失败:{e.Message}");
                }
            }
        }
        catch (Exception e)
        {
            Log.Error($"[物实例-报警]:严重错误：{e.Message}");
        }
    }

    /// <summary>
    ///     新增物报警条件
    /// </summary>
    public Task AddWarningCenterCustom(AlarmCondition alarmCondition)
    {
        try
        {
            WarningCenterCustom warningCenterCustom = new(_db, _eventBusFactory)
            {
                AlarmCondition = alarmCondition
            };
            AlarmConditionThreads.TryAdd(alarmCondition.Id, warningCenterCustom);
            ThreadPool.QueueUserWorkItem(_ => warningCenterCustom.Start());
        }
        catch (Exception e)
        {
            Log.Error($"【报警中心】 Error:{e.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    ///     停止线程
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public Task Stop(long id)
    {
        if (!AlarmConditionThreads.TryGetValue(id, out WarningCenterCustom? thread))
        {
            return Task.CompletedTask;
        }

        thread.Stop();
        AlarmConditionThreads[id].Dispose();
        AlarmConditionThreads.TryRemove(id, out _);

        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        _timer?.Dispose();
    }
}