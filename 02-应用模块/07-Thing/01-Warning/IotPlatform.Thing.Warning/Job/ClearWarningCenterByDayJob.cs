namespace IotPlatform.Thing.Warning.Job;

/// <summary>
///     报警抑制-次日零点自动解除
/// </summary>
[JobDetail("ClearWarningCenterByDay", "次日零点自动解除", GroupName = "报警中心-报警抑制")]
[<PERSON>ron("00 00 00 ? * *", CronStringFormat.WithSeconds, TriggerId = "ClearWarningCenterByDay")]
public class ClearWarningCenterByDayJob : IJob
{
    private readonly ILogger<ClearWarningCenterByDayJob> _logger;
    private readonly IServiceScopeFactory _scopeFactory;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="scopeFactory"></param>
    public ClearWarningCenterByDayJob(ILogger<ClearWarningCenterByDayJob> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="stoppingToken"></param>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        TaskFactory taskFactory = new(TaskScheduler.Current);
        await taskFactory.StartNew(async () =>
        {
            try
            {
                using IServiceScope scope = _scopeFactory.CreateScope();
                IServiceProvider services = scope.ServiceProvider;
                await services.GetService<ThingAlarmConfService>()!.DayClear();
            }
            catch (Exception ex)
            {
                _logger.LogError($"【报警中心-报警抑制】 次日零点自动解除 Error:【{ex.Message}】");
            }
        }, stoppingToken);
    }
}