using Furion;
using IotPlatform.Thing.Warning.HostedService;

namespace IotPlatform.Thing.Warning;

/// <summary>
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        services.AddSingleton<ThingWarningHostedService>(); // 注册为单例服务
        services.AddHostedService(provider => provider.GetRequiredService<ThingWarningHostedService>());
    }
}