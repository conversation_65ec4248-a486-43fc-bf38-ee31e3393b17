using IotPlatform.Core;
using IotPlatform.Core.Const;
using IotPlatform.Thing.Entity;
using NewLife.Reflection;
using Yitter.IdGenerator;

namespace IotPlatform.Thing.Warning.Services;

/// <summary>
///     报警管理-报警设置
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-17
/// </summary>
[ApiDescriptionSettings("报警管理")]
public class ThingAlarmConfService : IDynamicApiController, ITransient
{
    private readonly SysCacheService _cacheService;
    private readonly ISqlSugarRepository<AlarmConf> _thingAlarmConf;

    /// <summary>
    /// </summary>
    /// <param name="thingAlarmConf"></param>
    /// <param name="cacheService"></param>
    public ThingAlarmConfService(ISqlSugarRepository<AlarmConf> thingAlarmConf, SysCacheService cacheService)
    {
        _thingAlarmConf = thingAlarmConf;
        _cacheService = cacheService;
    }

    /// <summary>
    ///     物模型-报警设置
    /// </summary>
    /// <returns></returns>
    [HttpGet("/alarmConf/get")]
    public async Task<AlarmConf> ThingAlarmConfGet()
    {
        AlarmConf? thingAlarmConf = _cacheService.Get<AlarmConf>("thingAlarmConf");
        if (thingAlarmConf != null)
        {
            return thingAlarmConf;
        }

        thingAlarmConf = await _thingAlarmConf.AsQueryable().FirstAsync();
        if (thingAlarmConf != null)
        {
            _cacheService.Set("thingAlarmConf", thingAlarmConf);
            return thingAlarmConf;
        }

        // 不存在添加一条默认的
        thingAlarmConf = new AlarmConf
        {
            Id = YitIdHelper.NextId(),
            CloseType = CloseTypeEnum.Day,
            DayMax = 500,
            HourMax = 50,
            HourCloseType = CloseTypeEnum.Day
        };
        await _thingAlarmConf.InsertAsync(thingAlarmConf);
        _cacheService.Set("thingAlarmConf", thingAlarmConf);
        return thingAlarmConf;
    }

    /// <summary>
    ///     物模型-报警设置
    /// </summary>
    /// <returns></returns>
    [HttpPost("/alarmConf/set")]
    public async Task ThingAlarmConfSet(AlarmConf input)
    {
        AlarmConf? thingAlarmConf = await _thingAlarmConf.GetFirstAsync(f => f.Id == input.Id);
        if (thingAlarmConf == null)
        {
            return;
        }

        thingAlarmConf = input;
        await _thingAlarmConf.UpdateAsync(thingAlarmConf);
    }

    /// <summary>
    ///     小时清空
    /// </summary>
    public  async Task HourClear()
    {
        // 解除实例抑制状态
        var isSet = await _thingAlarmConf.AsSugarClient().Updateable<ModelThing>()
            .SetColumns(w => w.Restrain == false)
            .Where(w => w.Restrain == true)
            .ExecuteCommandAsync();
        if (isSet > 0)
        {
            await MessageCenter.PublishAsync(EventConst.ThingRestrainClose, new {});
        }
    }

    /// <summary>
    ///     按天清空
    /// </summary>
    public async Task DayClear()
    {
        // 解除实例抑制状态
        var isSet = await _thingAlarmConf.AsSugarClient().Updateable<ModelThing>()
            .SetColumns(w => w.Restrain == false)
            .Where(w => w.Restrain == true).ExecuteCommandAsync();
        if (isSet >0)
        {
            await MessageCenter.PublishAsync(EventConst.ThingRestrainCloseByDay, new {});
        }
    }
}