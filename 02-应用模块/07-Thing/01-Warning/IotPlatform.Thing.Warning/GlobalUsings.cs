global using IotPlatform.Core.Extension;
global using SqlSugar;
global using System.Collections.Concurrent;
global using Furion.EventBus;
global using Furion.Logging;
global using IotPlatform.Thing.Warning.Entity;
global using Furion.Schedule;
global using Furion.TimeCrontab;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;
global using Common.Models;
global using Extras.DatabaseAccessor.SqlSugar.Internal;
global using Extras.DatabaseAccessor.SqlSugar.Repositories;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using IotPlatform.Core.Enum;
global using IotPlatform.Thing.Warning.Custom;
global using Mapster;
global using Microsoft.AspNetCore.Mvc;
global using System;
global using System.Collections.Generic;
global using System.Threading.Tasks;
global using System.Linq;
global using IotPlatform.Core.Const;
global using IotPlatform.Thing.Entity;
global using System.Threading;
global using IotPlatform.Thing.Warning.Services;