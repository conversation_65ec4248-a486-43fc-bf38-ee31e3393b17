using System;
using System.Threading;
using Furion.JsonSerialization;
using DateTime = System.DateTime;

namespace IotPlatform.Thing.StatisticalRule.Job;

/// <summary>
///     物模型-统计规则任务
/// </summary>
public class StatisticalRuleJob : IJob
{
    /// <summary>
    ///     TDengIne 封装的方法
    /// </summary>
    private readonly ExecuteService _executeService;

    private readonly ILogger<StatisticalRuleJob> _logger;
    private readonly SqlSugarScope _db;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="executeService"></param>
    /// <param name="sugarClient"></param>
    public StatisticalRuleJob(ILogger<StatisticalRuleJob> logger, ExecuteService executeService, ISqlSugarClient sugarClient)
    {
        _logger = logger;
        _executeService = executeService;
        _db = (SqlSugarScope) sugarClient;
    }

    /// <summary>
    ///     指定方法
    /// </summary>
    /// <param name="context"></param>
    /// <param name="stoppingToken"></param>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        TaskFactory taskFactory = new(TaskScheduler.Current);
        await taskFactory.StartNew(async () =>
        {
            Dictionary<string, object>? properties = context.JobDetail.GetProperties();
            StatisticalRuleConf statisticalRule = null;
            // 通过外部获取数据
            if (properties.TryGetValue("StatisticalRule", out object? statisticalRuleObj))
            {
                statisticalRule = statisticalRuleObj.ToString().ToObjectOld<StatisticalRuleConf>();
            }

            if (statisticalRule == null)
            {
                return;
            }

            try
            {
                // 时间单位转换成分钟
                if (statisticalRule.DateTimeUnit == DateTimeUnitEnum.Hour)
                {
                    statisticalRule.DateTimeNumber *= 60;
                }

                // 查询SQL
                switch (statisticalRule.StatisticalRuleType)
                {
                    case StatisticalRuleTypeEnum.Total:
                    {
                        await StatisticalRuleTypeByTotal(statisticalRule, context);
                        return;
                    }

                    case StatisticalRuleTypeEnum.MaxOrMinOrAvg:
                    {
                        await StatisticalRuleTypeByMaxOrMinOrAvg(statisticalRule, context);
                        return;
                    }
                    case StatisticalRuleTypeEnum.OpenOrClose:
                    {
                        await StatisticalRuleTypeByOpenOrClose(statisticalRule, context, statisticalRule.Propertys);
                        break;
                    }
                    case StatisticalRuleTypeEnum.If:
                    {
                        await StatisticalRuleTypeByIf(statisticalRule, context, statisticalRule.Propertys);
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"【物模型-统计规则Job】 Error:【{ex.Message}】");
            }
        }, stoppingToken);
    }

    /// <summary>
    ///     累计统计
    /// </summary>
    private async Task StatisticalRuleTypeByTotal(StatisticalRuleConf statisticalRule, JobExecutingContext context)
    {
        // 截止时间
        DateTime executingTime = context.ExecutingTime;
        // 开始时间
        DateTime startTime = context.ExecutingTime.AddMinutes(-statisticalRule.DateTimeNumber);
        // 新增实时统计记录
        List<ReportTotal> reportTotalList = new();
        // 新增或修改统计历史
        List<ReportTotalCarryover> reportTotalCarryoverListSet = new();
        foreach (StatisticalProperty property in statisticalRule.Propertys)
        {
            // 查询SQL
            string checkSql = "SELECT ";
            checkSql += $"first(`{property.Identification}`) as _first" + ",";
            checkSql += $"last(`{property.Identification}`) as _last" + ",";
            checkSql += $"last(`{property.Identification}`) - first(`{property.Identification}`) as _diff" + ",";

            checkSql += " deviceName ";
            checkSql += $" from {statisticalRule.Model.Uuid} where ts >= " +
                        $"{Core.Extension.DateTime.ToLong(startTime.AddHours(-8))} and ts <= {Core.Extension.DateTime.ToLong(executingTime.AddHours(-8))} group by deviceName";

            _logger.LogInformation($"【累计统计】 查询SQL：[{checkSql}]");
            List<Dictionary<string, object?>>? executeDataList = _executeService.Select(checkSql);
            _logger.LogInformation($"【累计统计】 查询结果：[{executeDataList.ToJson()}]");

            // 当前时间范围有数据
            if (!executeDataList.Any())
            {
                continue;
            }

            // 设备名称集合
            List<string?> deviceNameList = executeDataList.Where(dict => dict.ContainsKey("devicename")).Select(dict => dict["devicename"].ToString()).ToList();

            // 当年/当月/当日 实时统计情况
            List<ReportTotalCarryover>? reportTotalCarryoverList = await _db.Queryable<ReportTotalCarryover>()
                .SplitTable(tabs => tabs.InTableNames($"report_{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover"))
                .Where(w => deviceNameList.Contains(w.DeviceName)
                            && w.TagName == property.Identification
                            && ((w.StartTime.Date == DateTime.Now.Date && w.Type == "day")
                                || (w.StartTime.Date.Year == DateTime.Now.Year && w.Type == "year")
                                || (w.StartTime.Date.Month == DateTime.Now.Month && w.Type == "month")))
                .ToListAsync();

            // 遍历数据-根据设备名称
            foreach (Dictionary<string, object?>? executeData in executeDataList)
            {
                if (!executeData.ContainsKey("devicename"))
                {
                    continue;
                }

                // 取最新值
                double lastValue = executeData.ContainsKey("_last") ? executeData["_last"]?.ToString()?.ToLower() == "null" ? 0 :
                    Math.Round(Convert.ToDouble(executeData["_last"]), 3) : 0;
                // 最早值
                double firstValue = executeData.ContainsKey("_first") ? executeData["_first"]?.ToString()?.ToLower() == "null" ? 0 : 
                    Math.Round(Convert.ToDouble(executeData["_first"]), 3) : 0;
                // 累计值
                double sumValue = executeData.ContainsKey("_diff") ? executeData["_diff"]?.ToString()?.ToLower() == "null" ? 0 :
                    Math.Round(Convert.ToDouble(executeData["_diff"]), 3) : 0;
//      【累计统计】 查询结果：[[{"_first":11365,"_last":11545,"_diff":180.0,"devicename":"Screwm"}]]
                // 拼接设备名称
                string deviceName = executeData["devicename"] + "";
                ReportTotal reportTotal = new()
                {
                    DeviceName = deviceName,
                    Id = YitIdHelper.NextId(),
                    Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_{DateTime.Now:yyyy-MM}",
                    StartTime = startTime,
                    EndTime = executingTime,
                    TagName = property.Identification,
                    CurrentVal = lastValue,
                    TenantId = statisticalRule.TenantId
                };

                List<string> types = new() {"day", "month", "year"};
                foreach (string type in types)
                {
                    // 当年，当月，当日历史值
                    ReportTotalCarryover reportTotalCarryover = reportTotalCarryoverList.FirstOrDefault(f => f.Type == type && f.DeviceName == deviceName) ?? new ReportTotalCarryover
                    {
                        Id = YitIdHelper.NextId(),
                        Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover",
                        Type = type,
                        CurrentVal = lastValue,
                        DeviceName = deviceName,
                        StartTime = startTime.Date,
                        TagName = property.Identification,
                        PrevVal = firstValue,
                        DifferenceVal = sumValue,
                        TenantId = statisticalRule.TenantId
                    };

                    if (type == "day")
                    {
                        // 上一个值
                        reportTotal.PrevVal = Math.Round(firstValue,3);
                        // 差值
                        reportTotal.DifferenceVal = Math.Round(reportTotal.CurrentVal - reportTotal.PrevVal,3);
                    }

                    // 改变历史值
                    reportTotalCarryover.CurrentVal = Math.Round(lastValue,3);
                    reportTotalCarryover.DifferenceVal = Math.Round(lastValue - reportTotalCarryover.PrevVal,3);
                    reportTotalCarryover.Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover";
                    reportTotalCarryoverListSet.Add(reportTotalCarryover);
                }

                // 修改完毕后添加
                reportTotalList.Add(reportTotal);
            }
        }

        // 添加实时统计记录
        if (reportTotalList.Any())
        {
            await _db.Insertable(reportTotalList).SplitTable().ExecuteCommandAsync();
        }

        // 更新统计记录
        if (reportTotalCarryoverListSet.Any())
        {
            await _db.Storageable(reportTotalCarryoverListSet).SplitTable().ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     MaxOrMinOrAvg 统计
    /// </summary>
    private async Task StatisticalRuleTypeByMaxOrMinOrAvg(StatisticalRuleConf statisticalRule, JobExecutingContext context)
    {
        // 截止时间
        DateTime executingTime = context.ExecutingTime;
        // 开始时间
        DateTime startTime = context.ExecutingTime.AddMinutes(-statisticalRule.DateTimeNumber);
        // 新增实时统计记录
        List<ReportMax> reportMaxList = new();
        // 新增或修改统计历史
        List<ReportMaxCarryover> reportMaxCarryoverListSet = new();

        foreach (StatisticalProperty property in statisticalRule.Propertys)
        {
            // 查询SQL
            string checkSql = "SELECT ";
            checkSql += $"max(`{property.Identification}`) as _max" + ",";
            checkSql += $"min(`{property.Identification}`) as _min" + ",";
            checkSql += $"avg(`{property.Identification}`) as _avg" + ",";
            checkSql += $"first(`{property.Identification}`) as _first" + ",";
            checkSql += $"last(`{property.Identification}`) as _last" + ",";

            checkSql += " deviceName ";
            checkSql += $" from {statisticalRule.Model.Uuid} where ts >= " +
                        $"{Core.Extension.DateTime.ToLong(startTime.AddHours(-8))} and ts <= {Core.Extension.DateTime.ToLong(executingTime.AddHours(-8))} group by deviceName";

            _logger.LogInformation($"【Max/Min/Avg统计】 查询SQL：[{checkSql}]");
            List<Dictionary<string, object?>>? executeDataList = _executeService.Select(checkSql);
            _logger.LogInformation($"【Max/Min/Avg统计】 查询结果：[{executeDataList.ToJson()}]");

            // 当前时间范围没有数据
            if (!executeDataList.Any())
            {
                continue;
            }

            // 设备名称集合
            List<string?> deviceNameList = executeDataList.Where(dict => dict.ContainsKey("devicename")).Select(dict => dict["devicename"].ToString()).ToList();
            // 当年/当月/当日 实时统计情况
            List<ReportMaxCarryover>? reportMaxCarryoverList = await _db.Queryable<ReportMaxCarryover>()
                .SplitTable(tabs => tabs.InTableNames($"report_{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover"))
                .Where(w => deviceNameList.Contains(w.DeviceName)
                            && w.TagName == property.Identification
                            && ((w.StartTime.Date == DateTime.Now.Date && w.Type == "day")
                                || (w.StartTime.Date.Year == DateTime.Now.Year && w.Type == "year")
                                || (w.StartTime.Date.Month == DateTime.Now.Month && w.Type == "month")))
                .ToListAsync();

            // 遍历数据-根据设备名称
            foreach (Dictionary<string, object?>? executeData in executeDataList)
            {
                if (!executeData.ContainsKey("devicename"))
                {
                    continue;
                }

                // 取最新值
                double lastValue = executeData.ContainsKey("_last") ? executeData["_last"]?.ToString()?.ToLower() == "null" ? 0 :
                    Math.Round(Convert.ToDouble(executeData["_last"]), 3) : 0;
                // 取最大值
                double maxValue = executeData.ContainsKey("_max") ? executeData["_max"]?.ToString()?.ToLower() == "null" ? 0 : 
                    Math.Round(Convert.ToDouble(executeData["_max"]),3) : 0;
                // 最小值
                double minValue = executeData.ContainsKey("_min") ? executeData["_min"]?.ToString()?.ToLower() == "null" ? 0 :
                    Math.Round(Convert.ToDouble(executeData["_min"]),3) : 0;
                // 取平均值
                double avgValue = executeData.ContainsKey("_avg") ? executeData["_avg"]?.ToString()?.ToLower() == "null" ? 0 : 
                    Math.Round(Convert.ToDouble(executeData["_avg"]),3) : 0;
                // 最早值
                double firstValue = executeData.ContainsKey("_first") ? executeData["_first"]?.ToString()?.ToLower() == "null" ? 0 : 
                    Math.Round(Convert.ToDouble(executeData["_first"]),3) : 0;
                // 拼接设备名称
                string deviceName = executeData["devicename"] + "";

                reportMaxList.Add(new ReportMax
                {
                    DeviceName = deviceName,
                    Id = YitIdHelper.NextId(),
                    Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_{DateTime.Now:yyyy-MM}",
                    StartTime = startTime,
                    EndTime = executingTime,
                    TagName = property.Identification,
                    AverageVal = avgValue,
                    MaxVal = maxValue,
                    MinVal = minValue,
                    SInstantaneouVal = firstValue,
                    EInstantaneouVal = lastValue,
                    TenantId = statisticalRule.TenantId
                });

                List<string> types = new() {"day", "month", "year"};
                // 遍历 当年，当月，当日的累计数据
                foreach (ReportMaxCarryover reportMaxCarryover in types.Select(type => reportMaxCarryoverList.FirstOrDefault(f => f.Type == type && f.DeviceName == deviceName) ??
                                                                                       new ReportMaxCarryover
                                                                                       {
                                                                                           Id = YitIdHelper.NextId(),
                                                                                           Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover",
                                                                                           Type = type,
                                                                                           DeviceName = deviceName,
                                                                                           StartTime = startTime.Date,
                                                                                           TagName = property.Identification,
                                                                                           SInstantaneouVal = firstValue,
                                                                                           TenantId = statisticalRule.TenantId
                                                                                       }))
                {
                    reportMaxCarryover.EInstantaneouVal = lastValue;
                    reportMaxCarryover.MaxVal = Math.Round(maxValue > reportMaxCarryover.MaxVal ? maxValue : reportMaxCarryover.MaxVal,3);
                    reportMaxCarryover.MinVal = Math.Round(minValue < reportMaxCarryover.MinVal ? minValue : reportMaxCarryover.MinVal,3);
                    reportMaxCarryover.AverageVal = Math.Round((reportMaxCarryover.MaxVal + reportMaxCarryover.MinVal) / 2,3);
                    reportMaxCarryover.Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover";
                    reportMaxCarryoverListSet.Add(reportMaxCarryover);
                }
            }
        }

        if (reportMaxList.Any())
        {
            // 添加实时统计记录
            await _db.Insertable(reportMaxList).SplitTable().ExecuteCommandAsync();
        }

        if (reportMaxCarryoverListSet.Any())
        {
            // 更新统计记录
            await _db.Storageable(reportMaxCarryoverListSet).SplitTable().ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     开关统计
    /// </summary>
    private async Task StatisticalRuleTypeByOpenOrClose(StatisticalRuleConf statisticalRule, JobExecutingContext context, List<StatisticalProperty> propertyList)
    {
        // 截止时间
        DateTime executingTime = context.ExecutingTime;
        // 开始时间
        DateTime startTime = context.ExecutingTime.AddMinutes(-statisticalRule.DateTimeNumber);
        // 新增实时统计记录
        List<ReportOpen> reportOpenList = new();
        // 新增或修改统计历史
        List<ReportOpenCarryover> reportOpenCarryoverListSet = new();
        foreach (StatisticalProperty property in propertyList)
        {
            // 查询SQL
            string checkSql = "SELECT ";

            checkSql += $" * FROM (SELECT _wstart as fst,_WEND as ent,_WDURATION as totalTime, `{property.Identification}`,deviceName" +
                        $" FROM {statisticalRule.Model.Uuid} where ts >= " +
                        $"{Core.Extension.DateTime.ToLong(startTime.AddHours(-8))} and ts <= {Core.Extension.DateTime.ToLong(executingTime.AddHours(-8))} partition by deviceName STATE_WINDOW(`{property.Identification}`)) t ;";

            _logger.LogInformation($"【开关统计】 查询SQL：[{checkSql}]");
            List<Dictionary<string, object?>>? executeDataList = _executeService.Select(checkSql);
            _logger.LogInformation($"【开关统计】 查询结果：[{executeDataList.ToJson()}]");

            // 按照设备名称分组
            List<IGrouping<object?, Dictionary<string, object?>>> executeDataGroupList = executeDataList.GroupBy(d => d["devicename"]).ToList();

            foreach (IGrouping<object?, Dictionary<string, object?>> executeDataGroup in executeDataGroupList)
            {
                // 当前设备名称
                string deviceName = executeDataGroup.Key + "";
                // 开启次数
                int openCount = 0;
                // 开机时长
                long openTimeTotal = 0;
                // 关闭次数
                int closeCount = 0;
                ReportOpen reportOpen = new()
                {
                    DeviceName = deviceName,
                    Id = YitIdHelper.NextId(),
                    Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_{DateTime.Now:yyyy-MM}",
                    StartTime = startTime,
                    EndTime = executingTime,
                    TagName = property.Identification,
                    TenantId = statisticalRule.TenantId
                };
                // 当前时间范围没有数据
                if (!executeDataList.Any())
                {
                    // 查询SQL
                    checkSql = "SELECT ";
                    checkSql += $"last(`{property.Identification}`) as _last" + ",";

                    checkSql += " deviceName ";
                    checkSql += $" from {statisticalRule.Model.Uuid} " + " group by deviceName";

                    // Console.WriteLine($"【开关统计-无数据】 查询SQL：[{checkSql}]");
                    executeDataList = _executeService.Select(checkSql);
                    // Console.WriteLine($"【开关统计-无数据】 查询结果：[{executeDataList.ToJson()}]");
                    if (!executeDataList.Any())
                    {
                        continue;
                    }
                }

                // 设备名称集合
                List<string?> deviceNameList = executeDataList.Where(dict => dict.ContainsKey("devicename")).Select(dict => dict["devicename"].ToString()).ToList();
                // 当年/当月/当日 实时统计情况
                List<ReportOpenCarryover>? reportOpenCarryoverList = await _db.Queryable<ReportOpenCarryover>()
                    .SplitTable(tabs => tabs.InTableNames($"report_{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover"))
                    .Where(w => deviceNameList.Contains(w.DeviceName)
                                && w.TagName == property.Identification
                                && ((w.StartTime.Date == DateTime.Now.Date && w.Type == "day")
                                    || (w.StartTime.Date.Year == DateTime.Now.Year && w.Type == "year")
                                    || (w.StartTime.Date.Month == DateTime.Now.Month && w.Type == "month")))
                    .ToListAsync();

                foreach (Dictionary<string, object?>? executeData in executeDataGroup)
                {
                    if (!executeData.ContainsKey("devicename"))
                    {
                        continue;
                    }

                    try
                    {
                        // 统计开关数
                        bool boolValue = executeData[property.Identification]?.ToString()?.ToLower() != "null" && Convert.ToBoolean(executeData[property.Identification]);
                        if (boolValue)
                        {
                            openCount += 1;
                            openTimeTotal += Convert.ToInt64(executeData["totaltime"]);
                        }
                        else
                        {
                            closeCount += 1;
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError($"【数据统计】:{e.Message}");
                    }
                }

                reportOpen.OpenNum = openCount;
                reportOpen.ShutNum = closeCount;
                reportOpen.OpenTimeLong = openTimeTotal;

                List<string> types = new() {"day", "month", "year"};
                // 遍历 当年，当月，当日的开关数据
                foreach (ReportOpenCarryover reportOpenCarryover in types.Select(type => reportOpenCarryoverList.FirstOrDefault(f => f.Type == type && f.DeviceName == deviceName) ??
                                                                                         new ReportOpenCarryover
                                                                                         {
                                                                                             Id = YitIdHelper.NextId(),
                                                                                             Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover",
                                                                                             Type = type,
                                                                                             DeviceName = deviceName,
                                                                                             StartTime = startTime.Date,
                                                                                             TagName = property.Identification,
                                                                                             TenantId = statisticalRule.TenantId
                                                                                         }))
                {
                    reportOpenCarryover.OpenNum += openCount;
                    reportOpenCarryover.ShutNum += closeCount;
                    reportOpenCarryover.OpenTimeLong += openTimeTotal;
                    reportOpenCarryover.Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover";
                    reportOpenCarryoverListSet.Add(reportOpenCarryover);
                }

                reportOpenList.Add(reportOpen);
            }
        }

        if (reportOpenList.Any())
            // 添加实时统计记录
        {
            await _db.Insertable(reportOpenList).SplitTable().ExecuteCommandAsync();
        }

        if (reportOpenCarryoverListSet.Any())
            // 更新统计记录
        {
            await _db.Storageable(reportOpenCarryoverListSet).SplitTable().ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     条件统计
    /// </summary>
    private async Task StatisticalRuleTypeByIf(StatisticalRuleConf statisticalRule, JobExecutingContext context, List<StatisticalProperty> propertyList)
    {
        // 截止时间
        DateTime executingTime = context.ExecutingTime;
        // 开始时间
        DateTime startTime = context.ExecutingTime.AddMinutes(-statisticalRule.DateTimeNumber);
        // 新增实时统计记录
        List<ReportIf> reportIfList = new();
        // 新增或修改统计历史
        List<ReportIfCarryover> reportIfCarryoverListSet = new();

        foreach (StatisticalProperty property in propertyList)
        {
            // 查询SQL
            string checkSql = "SELECT ";

            checkSql += $" * FROM (SELECT _wstart as fst,_WEND as ent,_WDURATION as totalTime, `{property.Identification}`,deviceName" +
                        $" FROM {statisticalRule.Model.Uuid} where ts >= " +
                        $"{Core.Extension.DateTime.ToLong(startTime.AddHours(-8))} and ts <= {Core.Extension.DateTime.ToLong(executingTime.AddHours(-8))} partition by deviceName STATE_WINDOW(`{property.Identification}`)) t ;";

            List<Dictionary<string, object?>>? executeDataList = _executeService.Select(checkSql);

            _logger.LogInformation($"【条件统计】: 执行SQL：{checkSql}");
            // 符合匹配条件的字典
            List<Dictionary<string, object>> meetTheConditions = new();
            _logger.LogInformation($"【条件统计】:{JSON.Serialize(executeDataList)}");
            // 按照设备名称分组
            List<IGrouping<object?, Dictionary<string, object?>>> executeDataGroupList = executeDataList.GroupBy(d => d["devicename"]).ToList();

            // 设备名称集合
            List<string?> deviceNameList = executeDataList.Where(dict => dict.ContainsKey("devicename")).Select(dict => dict["devicename"].ToString()).ToList();

            // 当年/当月/当日 实时统计情况
            List<ReportIfCarryover>? reportIfCarryoverList = await _db.Queryable<ReportIfCarryover>()
                .SplitTable(tabs => tabs.InTableNames($"report_{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover"))
                .Where(w => deviceNameList.Contains(w.DeviceName)
                            && w.TagName == property.Identification
                            && ((w.StartTime.Date == DateTime.Now.Date && w.Type == "day")
                                || (w.StartTime.Date.Year == DateTime.Now.Year && w.Type == "year")
                                || (w.StartTime.Date.Month == DateTime.Now.Month && w.Type == "month")))
                .ToListAsync();

            foreach (IGrouping<object?, Dictionary<string, object?>> executeDataGroup in executeDataGroupList)
            {
                Dictionary<string, ReportIfParam> paramDic = new();
                // 遍历统计参数
                foreach (StatisticalParameter parameter in statisticalRule.Parameter)
                {
                    Interpreter interpreter = new();
                    // 满足次数
                    int count = 0;
                    // 累计持续时间
                    long timeTotal = 0;
                    // 记录最开始的时间
                    long lastTime = 0;

                    foreach (Dictionary<string, object?>? executeData in executeDataGroup)
                    {
                        if (!executeData.ContainsKey("devicename"))
                        {
                            continue;
                        }

                        try
                        {
                            // value
                            object? value = executeData[property.Identification.ToLower()];
                            switch (value)
                            {
                                case int or long or short when value.ToString() != "NULL":
                                    interpreter.SetVariable("value", Convert.ToInt64(value));
                                    interpreter.SetVariable(parameter.Identification, Convert.ToInt64(value));
                                    break;
                                case double or decimal when value.ToString() != "NULL":
                                    interpreter.SetVariable("value", Convert.ToDouble(value));
                                    interpreter.SetVariable(parameter.Identification, Convert.ToDouble(value));
                                    break;
                                default:
                                    interpreter.SetVariable("value", value.ToString());
                                    interpreter.SetVariable(parameter.Identification, value.ToString());
                                    break;
                            }
                            
                            if (lastTime == 0)
                            {
                                lastTime = Core.Extension.DateTime.ToLong(executeData["fst"].ToString());
                            }

                            if (parameter.Expression != "以上条件均不匹配")
                            {
                                if (value.ToString() != "NULL")
                                {
                                    // 是否满足条件
                                    bool isEval = (bool) interpreter.Eval(parameter.Expression);
                                    if (isEval)
                                    {
                                        count += 1;
                                        timeTotal += Core.Extension.DateTime.ToLong(executeData["ent"].ToString()) - lastTime;
                                        // 满足条件时，标记到符合条件的字典中
                                        meetTheConditions.Add(executeData);
                                    }
                                }
                            }
                            else
                            {
                                if (value.ToString() != "NULL")
                                {
                                    // 过滤掉在列表 b 中存在的数据
                                    List<Dictionary<string, object?>> filteredDataList = executeDataGroup.Where(d => !meetTheConditions.Any(e => e.DictionaryEqual(d))).ToList();
                                    foreach (Dictionary<string, object?>? filteredData in filteredDataList)
                                    {
                                        count += 1;
                                        timeTotal += Core.Extension.DateTime.ToLong(executeData["ent"].ToString()) - lastTime;
                                    }
                                }

                                break;
                            }

                            lastTime = Core.Extension.DateTime.ToLong(executeData["fst"].ToString());
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(e.Message);
                        }
                    }

                    paramDic.TryAdd(parameter.Name, new ReportIfParam
                    {
                        OpenNum = count,
                        OpenTimeLong = timeTotal == 0 ? 0 : timeTotal / 1000
                    });
                }

                // 拼接设备名称
                string deviceName = executeDataGroup.Key + "";
                ReportIf reportIf = new()
                {
                    DeviceName = deviceName,
                    Id = YitIdHelper.NextId(),
                    Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_{DateTime.Now:yyyy-MM}",
                    StartTime = startTime,
                    EndTime = executingTime,
                    TagName = property.Identification,
                    Params = paramDic,
                    TenantId = statisticalRule.TenantId
                };

                List<string> types = new() {"day", "month", "year"};
                foreach (string type in types)
                {
                    // 当年，当月，当日历史值
                    ReportIfCarryover reportIfCarryover = reportIfCarryoverList.FirstOrDefault(f => f.Type == type && f.DeviceName == deviceName) ?? new ReportIfCarryover
                    {
                        Id = YitIdHelper.NextId(),
                        Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover",
                        Type = type,
                        DeviceName = deviceName,
                        StartTime = startTime.Date,
                        TagName = property.Identification,
                        Params = paramDic,
                        TenantId = statisticalRule.TenantId
                    };
                    foreach ((string key, ReportIfParam param) in paramDic)
                    {
                        if (reportIfCarryover.Params.ContainsKey(key))
                        {
                            reportIfCarryover.Params[key].OpenNum += param.OpenNum;
                            reportIfCarryover.Params[key].OpenTimeLong += param.OpenTimeLong;
                        }
                        else
                        {
                            reportIfCarryover.Params.TryAdd(key, param);
                        }
                    }

                    // 改变历史值
                    reportIfCarryover.Key = $"{statisticalRule.RuleCode}_{statisticalRule.Model.Uuid}_carryover";
                    reportIfCarryoverListSet.Add(reportIfCarryover);
                }

                // 修改完毕后添加
                reportIfList.Add(reportIf);
            }
        }

        if (reportIfList.Any())
            // 添加实时统计记录
        {
            await _db.Insertable(reportIfList).SplitTable().ExecuteCommandAsync();
        }

        if (reportIfCarryoverListSet.Any())
            // 更新统计记录
        {
            await _db.Storageable(reportIfCarryoverListSet).SplitTable().ExecuteCommandAsync();
        }
    }
}