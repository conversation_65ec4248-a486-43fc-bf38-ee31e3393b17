using IotPlatform.Core.Extension;
using IotPlatform.Thing.Entity;

namespace IotPlatform.Thing.StatisticalRule.Entity;

/// <summary>
///     数据统计-树 返回结果
/// </summary>
public class StatisticalRuleDataTreeOutput
{
    /// <summary>
    ///     统计类型：1：累计统计；2：MAX/MIN/AVG ；3:开关统计；4：条件统计
    /// </summary>
    public StatisticalRuleTypeEnum RuleType { get; set; }

    /// <summary>
    ///     统计类型名称
    /// </summary>
    public string RuleTypeName => RuleType.GetDescription();

    /// <summary>
    ///     数据统计-树-模型
    /// </summary>
    public List<StatisticalRuleDataTreeModel> Model { get; set; }
}

/// <summary>
///     数据统计-树-模型
/// </summary>
public class StatisticalRuleDataTreeModel
{
    /// <summary>
    ///     模型Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     模型名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     uuid
    /// </summary>
    public string Uuid { get; set; }

    /// <summary>
    ///     规则编码
    /// </summary>
    public string RuleCode { get; set; }

    /// <summary>
    ///     统计属性
    /// </summary>
    public List<StatisticalProperty> Propertys { get; set; }
}