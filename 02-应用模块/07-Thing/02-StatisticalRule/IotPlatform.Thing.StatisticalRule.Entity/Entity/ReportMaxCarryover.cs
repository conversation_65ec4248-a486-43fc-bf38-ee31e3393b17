using System;

namespace IotPlatform.Thing.StatisticalRule.Entity;

[SplitTable(SplitType._Custom02, typeof(ReportCarryoverSplitService))]
[SugarTable("report", "Max/Min/Avg统计-历史数据")]
public class ReportMaxCarryover : EntityTenantId
{
    /// <summary>
    ///     存储的时间
    /// </summary>
    [SugarColumn(ColumnDescription = "存储的时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     结转类型 day:日,month:月,year:年
    /// </summary>
    [SugarColumn(ColumnDescription = "结转类型 day:日,month:月,year:年")]
    public string Type { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    [SugarColumn(ColumnDescription = "设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性标识
    /// </summary>
    [SugarColumn(ColumnDescription = "属性标识")]
    public string TagName { get; set; }

    /// <summary>
    ///     最大值
    /// </summary>
    [SugarColumn(ColumnDescription = "最大值", IsNullable = true)]
    public double MaxVal { set; get; }

    /// <summary>
    ///     最小值
    /// </summary>
    [SugarColumn(ColumnDescription = "最小值", IsNullable = true)]
    public double MinVal { get; set; }

    /// <summary>
    ///     平均值
    /// </summary>
    [SugarColumn(ColumnDescription = "平均值", IsNullable = true)]
    public double AverageVal { get; set; }

    /// <summary>
    ///     开始瞬时值
    /// </summary>
    [SugarColumn(ColumnDescription = "开始瞬时值", IsNullable = true)]
    public double SInstantaneouVal { get; set; }

    /// <summary>
    ///     结束瞬时值
    /// </summary>
    [SugarColumn(ColumnDescription = "结束瞬时值", IsNullable = true)]
    public double EInstantaneouVal { get; set; }

    /// <summary>
    ///     分表标签
    /// </summary>
    [SplitField]
    [SugarColumn(IsIgnore = true)]
    [JsonIgnore]
    public string Key { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DateTime FTime => StartTime;
}