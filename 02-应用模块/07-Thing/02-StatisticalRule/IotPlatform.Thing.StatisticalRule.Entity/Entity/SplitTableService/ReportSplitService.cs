using System.Text.RegularExpressions;

namespace IotPlatform.Thing.StatisticalRule.Entity.SplitTableService;

/// <summary>
///     统计分表规则
/// </summary>
public class ReportSplitService : ISplitTableService
{
    /// <summary>
    ///     返回数据库中所有分表
    /// </summary>
    /// <param name="db"></param>
    /// <param name="entityInfo"></param>
    /// <param name="tableInfos"></param>
    /// <returns></returns>
    public List<SplitTableInfo> GetAllTables(ISqlSugarClient db, EntityInfo entityInfo, List<DbTableInfo> tableInfos)
    {
        List<SplitTableInfo> result = new List<SplitTableInfo>();
        foreach (DbTableInfo item in tableInfos)
        {
            // 通过正则判断是否是_yyyy-MM结尾的表
            string pattern = @"_\d{4}-\d{2}$";
            bool isMatch = Regex.IsMatch(item.Name, pattern);
            if (isMatch) //区分标识如果不用正则符复杂一些，防止找错表
            {
                SplitTableInfo data = new SplitTableInfo
                {
                    TableName = item.Name //要用item.name不要写错了
                };
                result.Add(data);
            }
        }

        return result.OrderBy(it => it.TableName).ToList(); //打断点看一下有没有查出所有分表
    }

    /// <summary>
    ///     获取分表字段的值
    /// </summary>
    /// <param name="db"></param>
    /// <param name="entityInfo"></param>
    /// <param name="splitType"></param>
    /// <param name="entityValue"></param>
    /// <returns></returns>
    public object GetFieldValue(ISqlSugarClient db, EntityInfo entityInfo, SplitType splitType, object entityValue)
    {
        EntityColumnInfo? splitColumn = entityInfo.Columns.FirstOrDefault(it => it.PropertyInfo.GetCustomAttribute<SplitFieldAttribute>() != null);
        object? value = splitColumn.PropertyInfo.GetValue(entityValue, null);
        return value;
    }

    /// <summary>
    /// </summary>
    /// <param name="db"></param>
    /// <param name="entityInfo"></param>
    /// <returns></returns>
    public string GetTableName(ISqlSugarClient db, EntityInfo entityInfo)
    {
        return entityInfo.DbTableName;
    }

    public string GetTableName(ISqlSugarClient db, EntityInfo entityInfo, SplitType type)
    {
        return entityInfo.DbTableName;
    }

    /// <summary>
    ///     确定生成数据库表的时候，表的名称
    /// </summary>
    /// <param name="db"></param>
    /// <param name="entityInfo"></param>
    /// <param name="splitType"></param>
    /// <param name="fieldValue"></param>
    /// <returns></returns>
    public string GetTableName(ISqlSugarClient db, EntityInfo entityInfo, SplitType splitType, object fieldValue)
    {
        return entityInfo.DbTableName + "_" + fieldValue; //根据值按首字母
    }
}