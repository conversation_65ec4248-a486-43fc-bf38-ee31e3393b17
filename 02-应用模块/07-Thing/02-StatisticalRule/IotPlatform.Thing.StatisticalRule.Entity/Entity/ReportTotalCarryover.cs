using System;

namespace IotPlatform.Thing.StatisticalRule.Entity;

[SplitTable(SplitType._Custom02, typeof(ReportCarryoverSplitService))]
[SugarTable("report", "累计统计-历史数据")]
public class ReportTotalCarryover : EntityTenantId
{
    /// <summary>
    ///     存储的时间
    /// </summary>
    [SugarColumn(ColumnDescription = "存储的时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     结转类型 day:日,month:月,year:年
    /// </summary>
    [SugarColumn(ColumnDescription = "结转类型 day:日,month:月,year:年")]
    public string Type { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    [SugarColumn(ColumnDescription = "设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性标识
    /// </summary>
    [SugarColumn(ColumnDescription = "属性标识")]
    public string TagName { get; set; }

    /// <summary>
    ///     上一个值
    /// </summary>
    [SugarColumn(ColumnDescription = "上一个值", IsNullable = true)]
    public double PrevVal { set; get; }

    /// <summary>
    ///     当前值
    /// </summary>
    [SugarColumn(ColumnDescription = "当前值", IsNullable = true)]
    public double CurrentVal { get; set; }

    /// <summary>
    ///     差值累计
    /// </summary>
    [SugarColumn(ColumnDescription = "差值累计", IsNullable = true)]
    public double DifferenceVal { get; set; }

    /// <summary>
    ///     录入值
    /// </summary>
    [SugarColumn(ColumnDescription = "录入值", IsNullable = true)]
    public double? InputVal { get; set; }

    /// <summary>
    ///     录入人
    /// </summary>
    [SugarColumn(ColumnDescription = "录入人", IsNullable = true)]
    public string? InputUserId { get; set; }

    /// <summary>
    ///     录入备注
    /// </summary>
    [SugarColumn(ColumnDescription = "录入备注", IsNullable = true)]
    public string? InputRemarks { get; set; }

    /// <summary>
    ///     录入时间
    /// </summary>
    [SugarColumn(ColumnDescription = "录入时间", IsNullable = true)]
    public DateTime? InputTime { get; set; }

    /// <summary>
    ///     分表标签
    /// </summary>
    [SplitField]
    [SugarColumn(IsIgnore = true)]
    [JsonIgnore]
    public string Key { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DateTime FTime => StartTime;
}