namespace IotPlatform.Application.Service.OpenApiServer.Dto;

/// <summary>
/// 获取临时Token
/// </summary>
public class GetTokenInput
{
    /// <summary>
    /// Token
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    /// 签名
    /// </summary>
    public string Sign { get; set; }

    /// <summary>
    /// 当前时间毫秒时间戳
    /// </summary>
    [Required]
    public long Timestamp { get; set; }

    /// <summary>
    /// 应用Id
    /// </summary>
    public string AppId { get; set; }
}