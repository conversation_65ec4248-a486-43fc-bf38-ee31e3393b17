using IotPlatform.Application.Service.BackupServices;

namespace IotPlatform.Application.Service.Job;

/// <summary>
///     备份管理-数据备份
/// </summary>
public class ExportDatabaseJob : IJob
{
    private readonly ILogger<ExportDatabaseJob> _logger;
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="scopeFactory"></param>
    public ExportDatabaseJob(ILogger<ExportDatabaseJob> logger, IServiceScopeFactory scopeFactory, ISqlSugarClient db)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
        _db = db;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="stoppingToken"></param>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        TaskFactory taskFactory = new(TaskScheduler.Current);
        await taskFactory.StartNew(async () =>
        {
            Dictionary<string, object> properties = context.JobDetail.GetProperties();
            DataBaseBackup? dataBaseBackup = null;
            string connectionString = null;
            // 通过外部获取数据
            if (properties.TryGetValue("DataBaseBackup", out object property))
            {
                dataBaseBackup = property.ToString().ToObjectOld<DataBaseBackup>();
            }

            if (properties.TryGetValue("connectionString", out object connection))
            {
                connectionString = connection.ToString();
            }

            if (dataBaseBackup == null || string.IsNullOrEmpty(connectionString))
            {
                return;
            }

            try
            {
                using IServiceScope scope = _scopeFactory.CreateScope();
                IServiceProvider services = scope.ServiceProvider;

                // 获取数据库备份工厂并根据数据库类型创建备份实例
                var backupFactory = services.GetService<DataBaseBackupFactory>()!;
                var dbContext = services.GetService<ISqlSugarClient>()!;
                IDataBaseBackup backup = backupFactory.CreateBackup(dbContext.CurrentConnectionConfig);

                dynamic output = await backup.ExportDatabase(connectionString, dataBaseBackup.Name + "_" + DateTime.Now.ToString("yyyy-MM-dd_HH_mm_ss"));
                long length = output.Length;
                string backupFileName = output.BackupFileName;
                string backupFilePath = output.BackupFilePath;

                BackupRecord backupRecord = new()
                {
                    Size = Math.Round((decimal) length / 1024 / 1024, 2),
                    BackupTime = DateTime.Now,
                    BackupFileName = backupFileName,
                    BackupFilePath = backupFilePath,
                    DataBaseBackupId = dataBaseBackup.Id
                };
                await _db.Insertable(backupRecord).ExecuteCommandAsync(stoppingToken);
                dataBaseBackup.BackupTime = backupRecord.BackupTime;
                dataBaseBackup.BackupStatus = "备份成功";
                await _db.CopyNew().Updateable(dataBaseBackup).ExecuteCommandAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                dataBaseBackup.BackupTime = DateTime.Now;
                dataBaseBackup.BackupStatus = "备份失败,Error:" + ex.Message;
                await _db.Updateable(dataBaseBackup).ExecuteCommandAsync(stoppingToken);
                _logger.LogError($"【备份管理-数据备份】 Error:【{ex.Message}】");
            }
        }, stoppingToken);
    }
}