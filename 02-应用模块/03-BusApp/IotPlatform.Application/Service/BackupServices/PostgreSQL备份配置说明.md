# PostgreSQL备份服务配置说明

## 概述

PostgreSQL备份服务现在支持多种部署场景，能够自动适应不同的环境配置。

## 支持的部署场景

### 1. 宿主机模式 (Host Mode)
- **应用部署**: 宿主机或容器
- **PostgreSQL部署**: 宿主机
- **适用场景**: PostgreSQL作为独立服务运行在宿主机上

### 2. 容器模式 (Container Mode)  
- **应用部署**: 宿主机或容器
- **PostgreSQL部署**: Docker容器
- **适用场景**: PostgreSQL运行在Docker容器中

### 3. 自动检测模式 (Auto Mode)
- **自动检测**: 根据环境自动选择最佳方式
- **适用场景**: 不确定部署方式或需要灵活配置

## 配置参数

在 `App.json` 文件中的 `PostgreSQL` 节点下配置：

```json
{
  "PostgreSQL": {
    // 基础工具路径配置
    "PgDumpPath": "",                    // pg_dump工具路径（可选）
    "PgRestorePath": "",                 // pg_restore工具路径（可选）
    "PsqlPath": "",                      // psql工具路径（可选）
    "ContainerName": "postgresql",       // PostgreSQL容器名称
    
    // 部署模式配置
    "DeploymentMode": "Host",            // 部署模式: "Host", "Container", "Auto"
    "ForceLocalTools": true,             // 是否强制使用本地工具
    
    // 宿主机连接配置
    "HostConnection": {
      "HostAddress": "host.docker.internal"  // 从容器访问宿主机的地址
    }
  }
}
```

## 配置参数详解

### DeploymentMode (部署模式)
- **"Host"**: 明确指定PostgreSQL部署在宿主机上
- **"Container"**: 明确指定PostgreSQL部署在容器中
- **"Auto"**: 自动检测部署方式（默认值）

### ForceLocalTools (强制使用本地工具)
- **true**: 优先使用宿主机上的PostgreSQL客户端工具
- **false**: 允许使用容器内的工具

### HostConnection.HostAddress (宿主机地址)
当应用运行在容器中需要连接宿主机PostgreSQL时使用：
- **"host.docker.internal"**: Docker Desktop默认的宿主机地址
- **"**********"**: Linux Docker默认网关地址
- **具体IP地址**: 宿主机的实际IP地址

## 使用场景配置示例

### 场景1: 应用在容器中，PostgreSQL在宿主机上
```json
{
  "PostgreSQL": {
    "DeploymentMode": "Host",
    "ForceLocalTools": false,
    "HostConnection": {
      "HostAddress": "host.docker.internal"
    }
  }
}
```

### 场景2: 应用在宿主机上，PostgreSQL在宿主机上
```json
{
  "PostgreSQL": {
    "DeploymentMode": "Host",
    "ForceLocalTools": true,
    "PgDumpPath": "/usr/bin/pg_dump",
    "PgRestorePath": "/usr/bin/pg_restore"
  }
}
```

### 场景3: 传统容器模式
```json
{
  "PostgreSQL": {
    "DeploymentMode": "Container",
    "ContainerName": "postgresql"
  }
}
```

## 工具检测优先级

1. **配置文件指定的工具路径**
2. **系统PATH环境变量中的工具**
3. **常见安装路径的工具**
4. **Docker容器内的工具**

## 故障排除

### 1. 备份失败：未找到PostgreSQL工具
**解决方案**:
- 安装PostgreSQL客户端工具
- 在配置文件中指定工具路径
- 确保Docker可用且容器运行正常

### 2. 连接宿主机PostgreSQL失败
**解决方案**:
- 检查 `HostConnection.HostAddress` 配置
- 确保宿主机PostgreSQL允许容器连接
- 检查防火墙和网络配置

### 3. 权限问题
**解决方案**:
- 确保PostgreSQL用户有备份权限
- 检查文件系统权限
- 确保容器有访问宿主机的权限

## 验证配置

可以通过调用 `/postgresql/validate-tools` 接口来验证当前配置是否正确。

该接口会返回：
- 工具可用性状态
- 检测到的工具路径
- Docker环境信息
- 当前部署配置信息
