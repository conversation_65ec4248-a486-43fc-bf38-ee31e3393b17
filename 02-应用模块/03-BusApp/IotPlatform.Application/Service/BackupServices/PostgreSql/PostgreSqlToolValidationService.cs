using Microsoft.Extensions.Configuration;

namespace IotPlatform.Application.Service.BackupServices;

/// <summary>
///     PostgreSQL工具验证服务
/// </summary>
[ApiDescriptionSettings("备份管理")]
public class PostgreSqlToolValidationService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     验证PostgreSQL工具
    /// </summary>
    /// <returns>验证结果</returns>
    [HttpGet("/postgresql/tools/validate")]
    public dynamic ValidatePostgreSqlTools()
    {
        var (isAvailable, message, pgDumpPath, pgRestorePath, useDocker) = PostgreSqlToolDetector.ValidateTools();
        
        return new
        {
            IsAvailable = isAvailable,
            Message = message,
            PgDumpPath = pgDumpPath,
            PgRestorePath = pgRestorePath,
            UseDocker = useDocker,
            DetectedPaths = new
            {
                PgDump = PostgreSqlToolDetector.DetectPgDumpPath(),
                PgRestore = PostgreSqlToolDetector.DetectPgRestorePath()
            },
            DockerInfo = new
            {
                IsDockerAvailable = PostgreSqlToolDetector.IsDockerAvailable(),
                IsContainerRunning = PostgreSqlToolDetector.IsPostgreSqlContainerRunning()
            },
            DeploymentInfo = new
            {
                DeploymentMode = App.Configuration["PostgreSQL:DeploymentMode"] ?? "Auto",
                ForceLocalTools = App.Configuration.GetValue<bool>("PostgreSQL:ForceLocalTools", true),
                IsApplicationInContainer = PostgreSqlToolDetector.IsCurrentApplicationInContainer(),
                HostAddress = App.Configuration["PostgreSQL:HostConnection:HostAddress"] ?? "host.docker.internal"
            }
        };
    }

    /// <summary>
    ///     获取当前数据库类型
    /// </summary>
    /// <returns>数据库类型信息</returns>
    [HttpGet("/database/type/info")]
    public dynamic GetDatabaseTypeInfo()
    {
        var db = App.GetService<ISqlSugarClient>();
        var connectionConfig = db.CurrentConnectionConfig;
        
        return new
        {
            DatabaseType = connectionConfig.DbType.ToString(),
            ConnectionString = connectionConfig.ConnectionString,
            DatabaseName = connectionConfig.ConnectionString.Contains("Database=") 
                ? connectionConfig.ConnectionString.Split("Database=")[1].Split(";")[0]
                : "Unknown",
            IsPostgreSQL = connectionConfig.DbType == SqlSugar.DbType.PostgreSQL,
            IsMySQL = connectionConfig.DbType == SqlSugar.DbType.MySql
        };
    }
}
