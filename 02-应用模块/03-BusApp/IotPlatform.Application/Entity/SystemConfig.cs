namespace IotPlatform.Application.Entity;

/// <summary>
///     系统配置
/// </summary>
[SugarTable("system_systemConfig", "系统配置表")]
public class SystemConfig : EntityBase
{
    /// <summary>
    ///     系统名称
    /// </summary>
    [SugarColumn(ColumnDescription = "系统名称",Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     Icon
    /// </summary>
    [SugarColumn(ColumnDescription = "Icon",IsNullable = true)]
    public string? Icon { get; set; }

    /// <summary>
    ///     菜单图标
    /// </summary>
    [SugarColumn(ColumnDescription = "菜单图标",IsNullable = true)]
    public string? Menu { get; set; }

    /// <summary>
    ///     版权标记
    /// </summary>
    [SugarColumn(ColumnDescription = "版权标记",IsNullable = true)]
    public string? Copyright { get; set; }

    /// <summary>
    ///     背景图片Logo
    /// </summary>
    [SugarColumn(ColumnDescription = "背景图片Logo",IsNullable = true)]
    public string? BackgroundPicture { get; set; }

    /// <summary>
    /// 版本号-忽略
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string Version { get; set; }
}