namespace IotPlatform.Application.Entity;

/// <summary>
///     备份管理-数据备份
/// </summary>
[SugarTable("business_dataBaseBackup", "备份管理-数据备份")]
public class DataBaseBackup : EntityBaseId
{
    /// <summary>
    ///     规则名称
    /// </summary>
    [SugarColumn(ColumnDescription = "规则名称")]
    public string Name { get; set; }

    /// <summary>
    ///     备份应用 1：WebSocket-数据
    /// </summary>
    [SugarColumn(ColumnDescription = "备份应用 1：WebSocket-数据", IsJson = true)]
    public List<DataBaseBackupAppEnum> BackupApp { get; set; }

    /// <summary>
    ///     自动备份 1：禁用； 2：自动备份
    /// </summary>
    [SugarColumn(ColumnDescription = "自动备份 1：禁用； 2：自动备份")]
    public DataBaseAutoBackupEnum AutoBackup { get; set; }

    /// <summary>
    ///     Cron表达式
    /// </summary>
    [SugarColumn(ColumnDescription = "Cron表达式", IsNullable = true)]
    public string Cron { get; set; }

    /// <summary>
    ///     存储管理Id
    /// </summary>
    [SugarColumn(ColumnDescription = "存储管理Id")]
    public long DataBaseStorageId { get; set; }

    /// <summary>
    ///     存储管理
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(DataBaseStorageId))]
    public DataBaseStorage? DataBaseStorage { get; set; }

    /// <summary>
    ///     最后备份时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后备份时间", IsNullable = true)]
    public DateTime BackupTime { get; set; }

    /// <summary>
    ///     最后备份状态
    /// </summary>
    [SugarColumn(ColumnDescription = "最后备份状态", IsNullable = true)]
    public string BackupStatus { get; set; }

    #region 忽略字段

    /// <summary>
    ///     自动备份 1：禁用； 2：自动备份
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string AutoBackupName => AutoBackup.GetDescription();

    #endregion
}

/// <summary>
///     备份应用 1：WebSocket-数据
/// </summary>
public enum DataBaseBackupAppEnum
{
    /// <summary>
    ///     WebSocket-数据
    /// </summary>
    [Description("WebSocket-数据")] Web = 1
}

/// <summary>
///     自动备份 1：禁用； 2：自动备份
/// </summary>
public enum DataBaseAutoBackupEnum
{
    /// <summary>
    ///     禁用
    /// </summary>
    [Description("禁用")] Disabled = 1,

    /// <summary>
    ///     自动备份
    /// </summary>
    [Description("自动备份")] Auto = 2
}