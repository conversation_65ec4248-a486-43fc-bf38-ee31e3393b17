namespace IotPlatform.DataWeaving;

/// <summary>
///     新建模型字段
/// </summary>
public class ManageTableFieldAddInput
{
    /// <summary>
    ///     字段名称
    /// </summary>
    [Required]
    public string FieldName { get; set; }

    /// <summary>
    ///     字段标识
    /// </summary>
    [Required]
    public string Code { get; set; }

    /// <summary>
    ///     字段类型
    /// </summary>
    [Required]
    public TableFieldEnum FieldType { get; set; }

    /// <summary>
    ///     字符串配置
    /// </summary>
    public StringModel StringModel { get; set; }

    /// <summary>
    ///     整型配置
    /// </summary>
    public NumberModel NumberModel { get; set; }

    /// <summary>
    ///     高精度配置
    /// </summary>
    public DecimalModel DecimalModel { get; set; }

    /// <summary>
    ///     时间配置
    /// </summary>
    public DateTimeModel DateTimeModel { get; set; }

    /// <summary>
    ///     维度配置
    /// </summary>
    public DimensionModel DimensionModel { get; set; }

    /// <summary>
    ///     默认值
    /// </summary>
    public string Default { get; set; }

    /// <summary>
    ///     必填
    /// </summary>
    public bool Required { get; set; }

    /// <summary>
    ///     是否显示
    /// </summary>
    public bool Display { get; set; }

    /// <summary>
    ///     是否查看
    /// </summary>
    public bool Check { get; set; }

    /// <summary>
    ///     表Id
    /// </summary>
    [Required]
    public long ManageTableId { get; set; }
}