namespace IotPlatform.DataWeaving;

/// <summary>
/// </summary>
public class ManageTableFieldSetCheckInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     是否查看
    /// </summary>
    public bool Check { get; set; }
}

/// <summary>
/// 模型字段-排序请求参数
/// </summary>
public class ManageTableFieldSortInput
{
    /// <summary>
    /// 数据建模Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    /// 字段排序
    /// </summary>
    [Required]
    public List<string> Field { get; set; }
}