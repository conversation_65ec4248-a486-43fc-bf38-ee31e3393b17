namespace IotPlatform.DataWeaving;

/// <summary>
///     创建模型
/// </summary>
public class ManageTableAddInput
{
    /// <summary>
    ///     模型名称
    /// </summary>
    [Required]
    public string ModelName { get; set; }

    /// <summary>
    ///     模型标识
    /// </summary>
    [Required]
    public string TableName { get; set; }

    /// <summary>
    ///     模型类型
    /// </summary>
    [Required]
    public ManageTableTypeEnum Type { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    ///     数据字典
    /// </summary>
    public DataDicModel DataDic { get; set; }
}