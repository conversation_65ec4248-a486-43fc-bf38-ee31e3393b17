namespace IotPlatform.DataWeaving.Servers;

/// <summary>
///     实体管理-集合
/// </summary>
public class DbEntityManageListInput
{
    /// <summary>
    /// 默认数据库连接标识
    /// </summary>
    public string configId { get; set; }
    
    /// <summary>
    /// 表名称过滤
    /// </summary>
    public string keyword { get; set; }
}

public class DbTableInput
{
    public string TableName { get; set; }

    public string Description { get; set; }

    public List<DbColumnInput> DbColumnInfoList { get; set; }
}

public class UpdateDbTableInput
{
    public string TableName { get; set; }

    public string OldTableName { get; set; }

    public string Description { get; set; }
}

public class DeleteDbTableInput
{
    public string TableName { get; set; }
}

public class DbColumnInput
{
    public string TableName { get; set; }

    public string DbColumnName { get; set; }

    public string DataType { get; set; }

    public int Length { get; set; }

    public string ColumnDescription { get; set; }

    public int IsNullable { get; set; }

    public int IsIdentity { get; set; }

    public int IsPrimarykey { get; set; }

    public int DecimalDigits { get; set; }
}

public class UpdateDbColumnInput
{
    public string TableName { get; set; }

    public string ColumnName { get; set; }

    public string OldColumnName { get; set; }

    public string Description { get; set; }
    
    public bool Primarykey { get; set; }
}

public class DeleteDbColumnInput
{
    public string TableName { get; set; }

    public string DbColumnName { get; set; }
}