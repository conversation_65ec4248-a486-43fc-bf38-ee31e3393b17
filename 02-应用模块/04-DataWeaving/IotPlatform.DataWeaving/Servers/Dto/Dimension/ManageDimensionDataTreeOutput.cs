namespace IotPlatform.DataWeaving;

/// <summary>
/// </summary>
public class ManageDimensionDataTreeOutput
{
    /// <summary>
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     节点名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     节点Id
    /// </summary>
    public string NodeId { get; set; }

    /// <summary>
    /// </summary>
    public List<ManageDimensionData> Children { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    public long Pid { get; set; }
    
    /// <summary>
    ///     所属维度管理
    /// </summary>
    public long ManageDimensionId { get; set; }
}