namespace IotPlatform.DataWeaving;

/// <summary>
///     维度管理数据新增
/// </summary>
public class ManageDimensionDataAddInput
{
    /// <summary>
    ///     节点名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    public long Pid { get; set; }

    /// <summary>
    ///     节点Id
    /// </summary>
    [Required]
    public string NodeId { get; set; }
    
    /// <summary>
    ///     所属维度管理
    /// </summary>
    [Required]
    public long ManageDimensionId { get; set; }
}
