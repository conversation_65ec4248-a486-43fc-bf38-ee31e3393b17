using Extras.DatabaseAccessor.SqlSugar.Internal;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using IotPlatform.Core.Extension;
using DateTime = System.DateTime;

namespace IotPlatform.DataWeaving.Servers;

/// <summary>
///     数据编织-自定义创建表管理
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("数据编织")]
public class ManageTableService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<ManageTable> _repository;

    /// <summary>
    /// </summary>
    /// <param name="dbLinkRepository"></param>
    public ManageTableService(ISqlSugarRepository<ManageTable> dbLinkRepository)
    {
        _repository = dbLinkRepository;
    }

    #region Get

    /// <summary>
    ///     (业务数据,数据字典,设备管理)数据列表
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageTable/type/data/page")]
    public async Task<SqlSugarPagedList<ManageTableDataPageOutput>> ManageTableDataPage([FromQuery] ManageTableDataPageInput input)
    {
        SqlSugarPagedList<TableData>? dataList = await _repository.AsSugarClient().Queryable<TableData>()
            .Where(w => w.ManageTableId == input.Id)
            .OrderByDescending(o => o.CreatedTime)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        // 用来过滤被删除的字段数据
        List<TableField>? fieldList = await _repository.AsSugarClient().Queryable<TableField>()
            .Where(w => w.ManageTableId == input.Id)
            .Where(w => w.Display == true)
            .ToListAsync();

        List<ManageTableDataPageOutput> result = new();
        // 遍历组装结果
        foreach (TableData? row in dataList.Rows)
        {
            ManageTableDataPageOutput manageTableData = new() {Id = row.Id, Data = new Dictionary<string, object>()};
            Dictionary<string, object>? rowData = JSON.Deserialize<Dictionary<string, object>>(row.Data);
            foreach (TableField? field in fieldList)
            {
                // 组装key,value
                manageTableData.Data.Add(field.Code, rowData.TryGetValue(field.Code, out object? value) ? value : "");
                // 维度手动组合其他参数
                if (field.FieldType == TableFieldEnum.Dimensionality)
                {
                    DimensionModel? dimensionModel = field.Config.ToObjectOld<DimensionModel>();
                    if (dimensionModel == null)
                    {
                        continue;
                    }

                    if (dimensionModel.ManageDimensionId <= 0)
                    {
                        continue;
                    }

                    if (field.Default == null)
                    {
                        continue;
                    }

                    // 维度数据Id
                    List<long>? manageDimensionDataIdList = field.Default.ToObjectOld<List<long>>();
                    // 维度数据
                    var manageDimensionDataList = await _repository.AsSugarClient().Queryable<ManageDimensionData>().Where(f => manageDimensionDataIdList.Contains(f.Id)).Select(s => new
                    {
                        s.Id,
                        s.Name
                    }).ToListAsync();
                    // 组合nodeName
                    string nodeName = manageDimensionDataIdList.Select(manageDimensionDataId => manageDimensionDataList.FirstOrDefault(f => f.Id == manageDimensionDataId))
                        .Where(manageDimensionData => manageDimensionData != null).Aggregate("", (current, manageDimensionData) => current + manageDimensionData.Name + ",");
                    nodeName = nodeName.TrimEnd(',');
                    manageTableData.Data.Add(field.Code + "._node_Name", nodeName);
                }
            }

            result.Add(manageTableData);
        }

        SqlSugarPagedList<ManageTableDataPageOutput> outputMap = dataList.Adapt<SqlSugarPagedList<ManageTableDataPageOutput>>();
        return outputMap;
    }

    /// <summary>
    ///     (业务数据,数据字典,设备管理)数据详情
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageTable/type/data/detail")]
    public async Task<ManageTableDataPageOutput> ManageTableDataDetail([FromQuery] BaseId input)
    {
        TableData? data = await _repository.AsSugarClient().Queryable<TableData>().FirstAsync(w => w.Id == input.Id);
        if (data == null)
        {
            throw Oops.Oh("数据不存在！");
        }

        Dictionary<string, object>? rowData = JSON.Deserialize<Dictionary<string, object>>(data.Data);
        ManageTableDataPageOutput resultData = new() {Id = data.Id, Data = rowData};
        return resultData;
    }

    /// <summary>
    ///     数据建模分类列表(业务数据,数据字典,设备管理)
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageTable/type/page")]
    public async Task<List<ManageTablePageOutput>> ManageTableTypePage([FromQuery] ManageTableTypePageInput input)
    {
        List<ManageTablePageOutput>? manageTables = await _repository.AsQueryable()
            .Where(w => w.Type == input.Type && w.Enable)
            .OrderBy(o => o.CreatedTime)
            .Select<ManageTablePageOutput>()
            .ToListAsync();
        return manageTables;
    }

    /// <summary>
    ///     数据建模字段集合(业务数据,数据字典,设备管理)
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageTable/type/field/list")]
    public async Task<List<TableField>> TypeFieldList([FromQuery] BaseId input)
    {
        List<TableField>? tableFieldList = await _repository.AsSugarClient().Queryable<TableField>()
            .Where(w => w.ManageTableId == input.Id)
            .Where(w => w.Display == true)
            .OrderBy(w => w.SortOrder)
            .ToListAsync();
        return tableFieldList;
    }

    /// <summary>
    ///     数据建模列表
    /// </summary>
    /// <param name="input">过滤条件</param>
    /// <returns></returns>
    [HttpGet("/manageTable/page")]
    public async Task<SqlSugarPagedList<ManageTablePageOutput>> Page([FromQuery] BasePageInput input)
    {
        SqlSugarPagedList<ManageTablePageOutput>? manageTables = await _repository.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue?.Trim()), w => w.ModelName.Contains(input.SearchValue)
                                                                            || w.TableName.Contains(input.SearchValue))
            .WhereIF(!string.IsNullOrEmpty(input.SearchBeginTime), u => u.CreatedTime >= Convert.ToDateTime(input.SearchBeginTime))
            .WhereIF(!string.IsNullOrEmpty(input.SearchEndTime), u => u.CreatedTime <= Convert.ToDateTime(input.SearchEndTime))
            .OrderBy(o => o.CreatedTime)
            .Select<ManageTablePageOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);

        foreach (ManageTablePageOutput row in manageTables.Rows.Where(w => w.Type == ManageTableTypeEnum.DataDic))
        {
            row.DataDic = JSON.Deserialize<DataDicModel>(row.Config);
        }

        return manageTables;
    }

    /// <summary>
    ///     数据建模详情
    /// </summary>
    /// <param name="input">过滤条件</param>
    /// <returns></returns>
    [HttpGet("/manageTable/detail")]
    public async Task<ManageTablePageOutput> Detail([FromQuery] BaseId input)
    {
        ManageTable? manageTable = await _repository.AsQueryable()
            .FirstAsync(w => w.Id == input.Id);
        return manageTable.Adapt<ManageTablePageOutput>();
    }

    /// <summary>
    ///     数据建模字段集合
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageTable/field/list")]
    public async Task<List<TableField>> FieldList([FromQuery] BaseId input)
    {
        List<TableField>? tableFieldList = await _repository.AsSugarClient().Queryable<TableField>()
            .Where(w => w.ManageTableId == input.Id)
            .OrderBy(o => o.SortOrder)
            .ToListAsync();
        return tableFieldList;
    }

    #endregion Get

    #region Post

    /// <summary>
    ///     新建模型
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/add")]
    public async Task<long> ManageTableAdd(ManageTableAddInput input)
    {
        if (await _repository.IsAnyAsync(a => a.TableName == input.TableName))
        {
            throw Oops.Oh("模型标识已存在！");
        }

        ManageTable mapTable = input.Adapt<ManageTable>();
        switch (mapTable.Type)
        {
            case ManageTableTypeEnum.Device:
            case ManageTableTypeEnum.BusinessData:
                break;
            case ManageTableTypeEnum.DataDic:
                mapTable.TableFields ??= new List<TableField>();
                mapTable.Config = JSON.Serialize(mapTable.DataDic);
                mapTable.PrimaryKey = "dictValue";
                TableField tableField = new()
                {
                    Code = "dictValue",
                    Default = "",
                    Display = true,
                    Id = YitIdHelper.NextId(),
                    Required = true,
                    Config = "{}",
                    FieldName = "数据字典值",
                    PrimaryKey = true,
                    ManageTable = mapTable
                };
                switch (mapTable.DataDic.DataDicType)
                {
                    case DataDicTypeEnum.Number:
                        tableField.FieldType = TableFieldEnum.Number;
                        break;
                    case DataDicTypeEnum.String:
                        tableField.FieldType = TableFieldEnum.String;
                        tableField.StringModel = new StringModel {Length = 50};
                        tableField.Config = JSON.Serialize(tableField.StringModel);
                        break;
                    case DataDicTypeEnum.Bool:
                        tableField.FieldType = TableFieldEnum.Bool;
                        break;
                }

                mapTable.TableFields.Add(tableField);

                mapTable.TableFields.Add(new TableField
                {
                    Check = true,
                    Code = "dictName",
                    StringModel = new StringModel {Length = 50},
                    Config = JSON.Serialize(tableField.StringModel),
                    Display = true,
                    Default = "",
                    Id = YitIdHelper.NextId(),
                    Required = true,
                    FieldName = "数据字典名称",
                    FieldType = TableFieldEnum.String,
                    ManageTable = mapTable
                });
                break;
        }

        await _repository.AsSugarClient().InsertNav(mapTable).Include(w => w.TableFields).ExecuteCommandAsync();
        return mapTable.Id;
    }

    /// <summary>
    ///     删除模型
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/delete")]
    public async Task ManageTableDelete(BaseId input)
    {
        await _repository.DeleteAsync(a => a.Id == input.Id);
    }

    /// <summary>
    ///     模型主键设置
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/setPrimaryKey")]
    public async Task ManageTableSetPrimaryKey(SetPrimaryKeyInput input)
    {
        ManageTable? table = await _repository.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (table == null)
        {
            throw Oops.Oh("模型不存在！");
        }

        if (table.Type == ManageTableTypeEnum.DataDic)
        {
            throw Oops.Oh("字典模型不允许编辑主键！");
        }

        table.PrimaryKey = input.PrimaryKey;
        TableField? field = await _repository.AsSugarClient().Queryable<TableField>().FirstAsync(f =>
            f.ManageTableId == table.Id && f.Code == input
                .PrimaryKey);
        if (field == null)
        {
            throw Oops.Oh($"字段标识:【{input.PrimaryKey}】不存在！");
        }

        field.PrimaryKey = true;
        field.Required = true;
        //如果之前有主键设置 重置
        List<TableField>? primaryKeyField = await _repository.AsSugarClient().Queryable<TableField>()
            .Where(f => f.ManageTableId == table.Id && f.PrimaryKey == true).ToListAsync();
        primaryKeyField.ForEach(f => f.PrimaryKey = false);
        if (primaryKeyField.Any())
        {
            await _repository.AsSugarClient().Updateable(primaryKeyField).UpdateColumns(w => new {w.PrimaryKey})
                .ExecuteCommandAsync();
        }

        await _repository.AsSugarClient().Updateable(field).UpdateColumns(w => new {w.PrimaryKey, w.Required})
            .ExecuteCommandAsync();
        await _repository.AsSugarClient().Updateable(table).UpdateColumns(w => w.PrimaryKey).ExecuteCommandAsync();
    }

    /// <summary>
    ///     模型描述设置
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/setRemark")]
    public async Task ManageTableSetRemark(SetRemarkInput input)
    {
        ManageTable? table = await _repository.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (table == null)
        {
            throw Oops.Oh("模型不存在！");
        }

        table.Remark = input.Remark;
        await _repository.AsSugarClient().Updateable(table).UpdateColumns(w => w.Remark).ExecuteCommandAsync();
    }

    /// <summary>
    ///     模型重命名
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/setModelName")]
    public async Task ManageTableSetModelName(SetNameInput input)
    {
        ManageTable? table = await _repository.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (table == null)
        {
            throw Oops.Oh("模型不存在！");
        }

        table.ModelName = input.ModelName;
        await _repository.AsSugarClient().Updateable(table).UpdateColumns(w => w.ModelName).ExecuteCommandAsync();
    }

    /// <summary>
    ///     模型启用禁用
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/enable")]
    public async Task ManageTableEnable(EnableInput<long> input)
    {
        ManageTable? table = await _repository.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (table == null)
        {
            throw Oops.Oh("模型不存在！");
        }

        table.Enable = input.Enable;
        await _repository.AsSugarClient().Updateable(table).UpdateColumns(w => w.Enable).ExecuteCommandAsync();
    }

    /// <summary>
    ///     模型字段-排序
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/field/sort")]
    public async Task ManageTableFieldSort(ManageTableFieldSortInput input)
    {
        List<TableField>? fieldList = await _repository.AsSugarClient().Queryable<TableField>().Where(w => w.ManageTableId == input.Id).ToListAsync();
        foreach (TableField? field in fieldList)
        {
            int index = input.Field.FindIndex(x => x == field.Code);
            if (index != -1)
            {
                field.SortOrder = index;
            }
        }

        await _repository.AsSugarClient().Updateable(fieldList).UpdateColumns(w => w.SortOrder).ExecuteCommandAsync();
    }

    /// <summary>
    ///     新建模型字段
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/field/add")]
    public async Task ManageTableFieldAdd(ManageTableFieldAddInput input)
    {
        if (await _repository.AsSugarClient().Queryable<TableField>().AnyAsync(a =>
                a.ManageTableId == input.ManageTableId && a.FieldName == input.FieldName))
        {
            throw Oops.Oh("字段标识已存在！");
        }

        TableField mapTable = input.Adapt<TableField>();
        switch (mapTable.FieldType)
        {
            case TableFieldEnum.Number:
                mapTable.Config = JSON.Serialize(input.NumberModel);
                break;
            case TableFieldEnum.String:
                mapTable.Config = JSON.Serialize(input.StringModel);
                break;
            case TableFieldEnum.Enums:
                mapTable.Config = "{}";
                break;
            case TableFieldEnum.Double:
            case TableFieldEnum.Decimal:
                mapTable.Config = JSON.Serialize(input.DecimalModel);
                break;
            case TableFieldEnum.DateTime:
                mapTable.Config = JSON.Serialize(input.DateTimeModel);
                break;
            case TableFieldEnum.Dimensionality:
                mapTable.Config = JSON.Serialize(input.DimensionModel);
                break;
        }

        await _repository.AsSugarClient().Insertable(mapTable).ExecuteCommandAsync();
    }

    /// <summary>
    ///     修改模型字段
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/field/update")]
    public async Task ManageTableFieldUpdate(TableField input)
    {
        if (await _repository.AsSugarClient().Queryable<TableField>().AnyAsync(a =>
                a.ManageTableId == input.ManageTableId && a.FieldName == input.FieldName && a.Id != input.Id))
        {
            throw Oops.Oh("字段标识已存在！");
        }

        switch (input.FieldType)
        {
            case TableFieldEnum.Number:
                input.Config = JSON.Serialize(input.NumberModel);
                break;
            case TableFieldEnum.String:
                input.Config = JSON.Serialize(input.StringModel);
                break;
            case TableFieldEnum.Enums:
                input.Config = "{}";
                break;
            case TableFieldEnum.Double:
            case TableFieldEnum.Decimal:
                input.Config = JSON.Serialize(input.DecimalModel);
                break;
            case TableFieldEnum.DateTime:
                input.Config = JSON.Serialize(input.DateTimeModel);
                break;
            case TableFieldEnum.Dimensionality:
                input.Config = JSON.Serialize(input.DimensionModel);
                break;
        }

        await _repository.AsSugarClient().Updateable(input).ExecuteCommandAsync();
    }

    /// <summary>
    ///     删除模型字段
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/field/delete")]
    public async Task ManageTableFieldDelete(BaseId input)
    {
        await _repository.AsSugarClient().Deleteable<TableField>(a => a.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    ///     修改模型字段是否查询
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/field/setCheck")]
    public async Task ManageTableFieldSetCheck(ManageTableFieldSetCheckInput input)
    {
        TableField? field = await _repository.AsSugarClient().Queryable<TableField>().FirstAsync(f => f.Id == input.Id);
        if (field == null)
        {
            throw Oops.Oh("字段已被删除！");
        }

        field.Check = input.Check;
        await _repository.AsSugarClient().Updateable(field).UpdateColumns(w => w.Check).ExecuteCommandAsync();
    }

    /// <summary>
    ///     修改模型字段是否显示
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/field/setDisplay")]
    public async Task ManageTableFieldSetDisplay(ManageTableFieldSetDisplayInput input)
    {
        TableField? field = await _repository.AsSugarClient().Queryable<TableField>().FirstAsync(f => f.Id == input.Id);
        if (field == null)
        {
            throw Oops.Oh("字段已被删除！");
        }

        field.Display = input.Display;
        await _repository.AsSugarClient().Updateable(field).UpdateColumns(w => w.Display).ExecuteCommandAsync();
    }

    /// <summary>
    ///     修改模型字段是否允许空
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageTable/field/setRequired")]
    public async Task ManageTableFieldSetRequired(ManageTableFieldSetRequiredInput input)
    {
        TableField? field = await _repository.AsSugarClient().Queryable<TableField>().FirstAsync(f => f.Id == input.Id);
        if (field == null)
        {
            throw Oops.Oh("字段已被删除！");
        }

        if (field.PrimaryKey && !input.Required)
        {
            throw Oops.Oh("主键是必填的！");
        }

        field.Required = input.Required;
        await _repository.AsSugarClient().Updateable(field).UpdateColumns(w => w.Required).ExecuteCommandAsync();
    }

    /// <summary>
    ///     (业务数据,数据字典,设备管理)-添加数据
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpPost("/manageTable/type/data/add")]
    public async Task ManageTableDataAdd(ManageTableDataAddInput input)
    {
        //数据模型
        ManageTable? table = await _repository.AsQueryable()
            .Where(f => f.Id == input.ManageTableId)
            .Includes(w => w.TableFields)
            .FirstAsync();
        if (table == null)
        {
            throw Oops.Oh("数据模型已被删除！");
        }

        if (!table.Enable)
        {
            throw Oops.Oh("数据模型被禁用,不允许操作数据！");
        }

        foreach (TableField field in table.TableFields)
        {
            try
            {
                object? value = null;
                if (input.FieldData.ContainsKey(field.Code))
                {
                    value = input.FieldData[field.Code];
                }

                if (!field.Required && value == null)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】不允许为空！");
                }

                //查看是否需要设置默认值
                if (value == null)
                {
                    if (!field.Default.IsNotEmptyOrNull())
                    {
                        continue;
                    }

                    switch (field.FieldType)
                    {
                        case TableFieldEnum.Number:
                            break;
                        case TableFieldEnum.String:
                            break;
                        case TableFieldEnum.Double:
                            break;
                        case TableFieldEnum.Decimal:
                            break;
                        case TableFieldEnum.DateTime:
                        {
                            DateTimeModel? dateTimeModel = JSON.Deserialize<DateTimeModel>(field.Config);
                            if (dateTimeModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            if (dateTimeModel.SystemTime)
                            {
                                switch (dateTimeModel.DateType)
                                {
                                    case DateTimeModelTypeEnum.Date:
                                        input.FieldData.Add(field.Code, DateTime.Now.ToString("yyyy-MM-dd"));
                                        break;
                                    case DateTimeModelTypeEnum.DateTime:
                                        input.FieldData.Add(field.Code, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                                        break;
                                    case DateTimeModelTypeEnum.Time:
                                        input.FieldData.Add(field.Code, DateTime.Now.ToString("HH:mm:ss"));
                                        break;
                                    case DateTimeModelTypeEnum.TimeStamp:
                                        input.FieldData.Add(field.Code, Core.Extension.DateTime.ToTsStr());
                                        break;
                                }
                            }
                            else
                            {
                                input.FieldData.Add(field.Code, field.Default);
                            }
                        }
                            break;
                        case TableFieldEnum.Enums:
                            break;
                        case TableFieldEnum.Dimensionality:
                            break;
                        default:
                            input.FieldData.Add(field.Code, field.Default);
                            break;
                    }
                }
                else
                {
                    //校验数据是否合规
                    switch (field.FieldType)
                    {
                        case TableFieldEnum.Number:
                        {
                            NumberModel? numberModel = JSON.Deserialize<NumberModel>(field.Config);
                            if (numberModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            if (numberModel.Min != 0 && Convert.ToInt64(value) < numberModel.Min)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最小值需要超过:【{numberModel.Min}】！");
                            }

                            if (numberModel.Max != 0 && Convert.ToInt64(value) > numberModel.Max)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大值不能超过:【{numberModel.Max}】！");
                            }

                            break;
                        }
                        case TableFieldEnum.String:
                        {
                            StringModel? stringModel = JSON.Deserialize<StringModel>(field.Config);
                            if (stringModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            if (stringModel.Length != 0 && value.ToString().Length > stringModel.Length)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大长度不能超过:【{stringModel.Length}】！");
                            }

                            break;
                        }
                        case TableFieldEnum.Double:
                        case TableFieldEnum.Decimal:
                        {
                            DecimalModel? decimalModel = JSON.Deserialize<DecimalModel>(field.Config);
                            if (decimalModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            if (decimalModel.Min != 0 && Convert.ToInt64(value) < decimalModel.Min)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最小值需要超过:【{decimalModel.Min}】！");
                            }

                            if (decimalModel.Max != 0 && Convert.ToInt64(value) > decimalModel.Max)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大值不能超过:【{decimalModel.Max}】！");
                            }

                            if (decimalModel.Length != 0 && value.ToString().Length > decimalModel.Length)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大长度不能超过:【{decimalModel.Length}】！");
                            }

                            break;
                        }
                        case TableFieldEnum.DateTime:
                        {
                            DateTimeModel? dateTimeModel = JSON.Deserialize<DateTimeModel>(field.Config);
                            if (dateTimeModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            switch (dateTimeModel.DateType)
                            {
                                case DateTimeModelTypeEnum.Date:
                                    //todo 校验格式
                                    break;
                                case DateTimeModelTypeEnum.DateTime:
                                    break;
                                case DateTimeModelTypeEnum.Time:
                                    break;
                                case DateTimeModelTypeEnum.TimeStamp:
                                    break;
                            }

                            break;
                        }
                        case TableFieldEnum.Enums:
                            break;
                        case TableFieldEnum.Dimensionality:
                        {
                            DimensionModel? dimensionModel = JSON.Deserialize<DimensionModel>(field.Config);
                            if (dimensionModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            break;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Error($"自定义模型数据创建校验异常:【{e.Message}】");
            }
        }

        TableData tableData = new()
        {
            Data = JSON.Serialize(input.FieldData),
            ManageTableId = input.ManageTableId
        };
        await _repository.AsSugarClient().Insertable(tableData).ExecuteCommandAsync();
    }

    /// <summary>
    ///     (业务数据,数据字典,设备管理)-修改数据
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpPost("/manageTable/type/data/update")]
    public async Task ManageTableDataUpdate(ManageTableDataUpdateInput input)
    {
        //数据模型
        ManageTable? table = await _repository.AsQueryable()
            .Where(f => f.Id == input.ManageTableId)
            .Includes(w => w.TableFields)
            .Includes(w => w.TableDatas)
            .FirstAsync();
        if (table == null)
        {
            throw Oops.Oh("数据模型已被删除！");
        }

        TableData? tableData = table.TableDatas.FirstOrDefault(f => f.Id == input.Id);
        if (tableData == null)
        {
            throw Oops.Oh("该行模型数据已被删除！");
        }

        if (!table.Enable)
        {
            throw Oops.Oh("数据模型被禁用,不允许操作数据！");
        }

        foreach (TableField field in table.TableFields)
        {
            try
            {
                object? value = null;
                if (input.FieldData.ContainsKey(field.Code))
                {
                    value = input.FieldData[field.Code];
                }

                if (!field.Required && value == null)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】不允许为空！");
                }

                //查看是否需要设置默认值
                if (value == null)
                {
                    if (!field.Default.IsNotEmptyOrNull())
                    {
                        continue;
                    }

                    if (field.FieldType == TableFieldEnum.DateTime)
                    {
                        DateTimeModel? dateTimeModel = JSON.Deserialize<DateTimeModel>(field.Config);
                        if (dateTimeModel == null)
                        {
                            throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                        }

                        if (dateTimeModel.SystemTime)
                        {
                            switch (dateTimeModel.DateType)
                            {
                                case DateTimeModelTypeEnum.Date:
                                    input.FieldData.Add(field.Code, DateTime.Now.ToString("yyyy-MM-dd"));
                                    break;
                                case DateTimeModelTypeEnum.DateTime:
                                    input.FieldData.Add(field.Code, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                                    break;
                                case DateTimeModelTypeEnum.Time:
                                    input.FieldData.Add(field.Code, DateTime.Now.ToString("HH:mm:ss"));
                                    break;
                                case DateTimeModelTypeEnum.TimeStamp:
                                    input.FieldData.Add(field.Code, Core.Extension.DateTime.ToTsStr());
                                    break;
                            }
                        }
                        else
                        {
                            input.FieldData.Add(field.Code, field.Default);
                        }
                    }
                    else
                    {
                        input.FieldData.Add(field.Code, field.Default);
                    }
                }
                else
                {
                    //校验数据是否合规
                    switch (field.FieldType)
                    {
                        case TableFieldEnum.Number:
                        {
                            NumberModel? numberModel = JSON.Deserialize<NumberModel>(field.Config);
                            if (numberModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            if (numberModel.Min != 0 && Convert.ToInt64(value) < numberModel.Min)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最小值需要超过:【{numberModel.Min}】！");
                            }

                            if (numberModel.Max != 0 && Convert.ToInt64(value) > numberModel.Max)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大值不能超过:【{numberModel.Max}】！");
                            }

                            break;
                        }
                        case TableFieldEnum.String:
                        {
                            StringModel? stringModel = JSON.Deserialize<StringModel>(field.Config);
                            if (stringModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            if (stringModel.Length != 0 && value.ToString().Length > stringModel.Length)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大长度不能超过:【{stringModel.Length}】！");
                            }

                            break;
                        }
                        case TableFieldEnum.Double:
                        case TableFieldEnum.Decimal:
                        {
                            DecimalModel? decimalModel = JSON.Deserialize<DecimalModel>(field.Config);
                            if (decimalModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            if (decimalModel.Min != 0 && Convert.ToInt64(value) < decimalModel.Min)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最小值需要超过:【{decimalModel.Min}】！");
                            }

                            if (decimalModel.Max != 0 && Convert.ToInt64(value) > decimalModel.Max)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大值不能超过:【{decimalModel.Max}】！");
                            }

                            if (decimalModel.Length != 0 && value.ToString().Length > decimalModel.Length)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大长度不能超过:【{decimalModel.Length}】！");
                            }

                            break;
                        }
                        case TableFieldEnum.DateTime:
                        {
                            DateTimeModel? dateTimeModel = JSON.Deserialize<DateTimeModel>(field.Config);
                            if (dateTimeModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            switch (dateTimeModel.DateType)
                            {
                                case DateTimeModelTypeEnum.Date:
                                    //todo 校验格式
                                    break;
                                case DateTimeModelTypeEnum.DateTime:
                                    break;
                                case DateTimeModelTypeEnum.Time:
                                    break;
                                case DateTimeModelTypeEnum.TimeStamp:
                                    break;
                            }

                            break;
                        }
                        case TableFieldEnum.Enums:
                            break;
                        case TableFieldEnum.Dimensionality:
                        {
                            DimensionModel? dimensionModel = JSON.Deserialize<DimensionModel>(field.Config);
                            if (dimensionModel == null)
                            {
                                throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                            }

                            break;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Error($"自定义模型数据创建校验异常:【{e.Message}】");
            }
        }

        tableData.Data = JSON.Serialize(input.FieldData);
        await _repository.AsSugarClient().Updateable(tableData).ExecuteCommandAsync();
    }

    /// <summary>
    ///     (业务数据,数据字典,设备管理)-删除数据
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpPost("/manageTable/type/data/delete")]
    public async Task ManageTableDataDelete(BaseId<List<long>> input)
    {
        await _repository.AsSugarClient().Deleteable<TableData>(a => input.Id.Contains(a.Id)).ExecuteCommandAsync();
    }

    /// <summary>
    ///     (业务数据,数据字典,设备管理)-检查添加数据是否达到要求
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpPost("/manageTable/type/data/check")]
    public async Task ManageTableDataCheck(ManageTableDataCheckInput input)
    {
        //数据模型
        TableField? field = await _repository.AsSugarClient().Queryable<TableField>()
            .Where(w => w.ManageTableId == input.ManageTableId)
            .Where(w => w.Code == input.Field)
            .FirstAsync();
        if (field == null)
        {
            throw Oops.Oh("数据模型字段已被删除！");
        }

        if (!field.Required && input.Value.IsNullOrEmpty())
        {
            throw Oops.Oh($"字段【{field.FieldName},{field.Code}】不允许为空！");
        }

        //校验数据是否合规
        switch (field.FieldType)
        {
            case TableFieldEnum.Number:
            {
                NumberModel? numberModel = JSON.Deserialize<NumberModel>(field.Config);
                if (numberModel == null)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                }

                if (numberModel.Min != 0 && Convert.ToInt64(input.Value) < numberModel.Min)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最小值需要超过:【{numberModel.Min}】！");
                }

                if (numberModel.Max != 0 && Convert.ToInt64(input.Value) > numberModel.Max)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大值不能超过:【{numberModel.Max}】！");
                }

                break;
            }
            case TableFieldEnum.String:
            {
                StringModel? stringModel = JSON.Deserialize<StringModel>(field.Config);
                if (stringModel == null)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                }

                if (stringModel.Length != 0 && input.Value.ToString().Length > stringModel.Length)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大长度不能超过:【{stringModel.Length}】！");
                }

                break;
            }
            case TableFieldEnum.Double:
            case TableFieldEnum.Decimal:
            {
                DecimalModel? decimalModel = JSON.Deserialize<DecimalModel>(field.Config);
                if (decimalModel == null)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                }

                if (decimalModel.Min != 0 && Convert.ToInt64(input.Value) < decimalModel.Min)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最小值需要超过:【{decimalModel.Min}】！");
                }

                if (decimalModel.Max != 0 && Convert.ToInt64(input.Value) > decimalModel.Max)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大值不能超过:【{decimalModel.Max}】！");
                }

                if (decimalModel.Length != 0 && input.Value.ToString().Length > decimalModel.Length)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】,最大长度不能超过:【{decimalModel.Length}】！");
                }

                break;
            }
            case TableFieldEnum.DateTime:
            {
                DateTimeModel? dateTimeModel = JSON.Deserialize<DateTimeModel>(field.Config);
                if (dateTimeModel == null)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                }

                switch (dateTimeModel.DateType)
                {
                    case DateTimeModelTypeEnum.Date:
                        //todo 校验格式
                        break;
                    case DateTimeModelTypeEnum.DateTime:
                        break;
                    case DateTimeModelTypeEnum.Time:
                        break;
                    case DateTimeModelTypeEnum.TimeStamp:
                        break;
                }

                break;
            }
            case TableFieldEnum.Enums:
                break;
            case TableFieldEnum.Dimensionality:
            {
                DimensionModel? dimensionModel = JSON.Deserialize<DimensionModel>(field.Config);
                if (dimensionModel == null)
                {
                    throw Oops.Oh($"字段【{field.FieldName},{field.Code}】配置异常,无法解析！");
                }

                break;
            }
        }
    }

    #endregion
}