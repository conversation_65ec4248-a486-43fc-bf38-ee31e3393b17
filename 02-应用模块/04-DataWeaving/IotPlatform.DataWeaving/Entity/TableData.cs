namespace IotPlatform.DataWeaving.Entity;

/// <summary>
///     自定义创建表数据
/// </summary>
[SugarTable("business_tableData", "自定义创建表数据")]
public class TableData : EntityTenantId
{
    /// <summary>
    ///     数据Data
    /// </summary>
    [SugarColumn(ColumnDescription = "Data", ColumnDataType = "longtext,text,clob")]
    public string Data { get; set; }

    /// <summary>
    ///     自定义创建表管理Id
    /// </summary>
    [SugarColumn(ColumnDescription = "自定义创建表管理Id")]
    public long ManageTableId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ManageTableId))]
    public ManageTable ManageTable { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public DateTime? CreatedTime { get; set; }
}