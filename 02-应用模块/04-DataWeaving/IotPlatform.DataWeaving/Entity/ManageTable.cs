namespace IotPlatform.DataWeaving.Entity;

/// <summary>
///     数据建模
/// </summary>
[SugarTable("business_manageTable", "数据建模")]
public class ManageTable : EntityTenant
{
    /// <summary>
    ///     模型名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称")]
    public string ModelName { get; set; }

    /// <summary>
    ///     模型标识(表名称)
    /// </summary>
    [SugarColumn(ColumnDescription = "模型标识(表名称)")]
    public string TableName { get; set; }

    /// <summary>
    ///     模型类型
    /// </summary>
    [SugarColumn(ColumnDescription = "模型类型")]
    public ManageTableTypeEnum Type { get; set; }

    /// <summary>
    ///     模型类型配置
    /// </summary>
    [SugarColumn(ColumnDescription = "模型类型配置")]
    public string? Config { get; set; }

    /// <summary>
    ///     唯一标识(主键)
    /// </summary>
    [SugarColumn(ColumnDescription = "唯一标识(主键)")]
    public string? PrimaryKey { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 1024)]
    public string? Remark { get; set; }

    /// <summary>
    ///     启用状态
    /// </summary>
    [SugarColumn(ColumnDescription = "启用状态")]
    public bool Enable { get; set; }

    /// <summary>
    ///     表字段管理
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(TableField.ManageTableId))]
    public List<TableField> TableFields { get; set; }

    /// <summary>
    ///     自定义创建表数据
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(TableData.ManageTableId))]
    public List<TableData> TableDatas { get; set; }

    /// <summary>
    ///     数据字典
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DataDicModel DataDic { get; set; }
}

/// <summary>
/// </summary>
public enum ManageTableTypeEnum
{
    /// <summary>
    ///     物联设备
    /// </summary>
    [Description("物联设备")] Device = 1,

    /// <summary>
    ///     业务数据
    /// </summary>
    [Description("业务数据")] BusinessData = 2,

    /// <summary>
    ///     数据字典
    /// </summary>
    [Description("数据字典")] DataDic = 3
}

/// <summary>
///     数据字典
/// </summary>
public class DataDicModel
{
    /// <summary>
    ///     值类型
    /// </summary>
    public DataDicTypeEnum DataDicType { get; set; }
}

/// <summary>
/// </summary>
public enum DataDicTypeEnum
{
    /// <summary>
    ///     整型
    /// </summary>
    [Description("整型")] Number = 1,

    /// <summary>
    ///     字符串
    /// </summary>
    [Description("字符串")] String = 2,

    /// <summary>
    ///     布尔型
    /// </summary>
    [Description("布尔型")] Bool = 3
}