namespace IotPlatform.DataWeaving.Entity;

/// <summary>
///     自定义创建表字段
/// </summary>
[SugarTable("business_tableField", "自定义创建表字段")]
public class TableField : EntityTenantId
{
    /// <summary>
    ///     字段名称
    /// </summary>
    [SugarColumn(ColumnDescription = "字段名称")]
    [Required]
    public string FieldName { get; set; }

    /// <summary>
    ///     字段标识
    /// </summary>
    [SugarColumn(ColumnDescription = "字段标识")]
    public string Code { get; set; }

    /// <summary>
    ///     字段类型
    /// </summary>
    [SugarColumn(ColumnDescription = "字段类型")]
    public TableFieldEnum FieldType { get; set; }

    /// <summary>
    ///     类型配置
    /// </summary>
    [SugarColumn(ColumnDescription = "类型配置",IsNullable = true)]
    public string? Config { get; set; }

    /// <summary>
    ///     默认值
    /// </summary>
    [SugarColumn(ColumnDescription = "默认值",IsNullable = true)]
    public string? Default { get; set; }

    /// <summary>
    ///     是否是主键
    /// </summary>
    [SugarColumn(ColumnDescription = "是否是主键")]
    public bool PrimaryKey { get; set; }

    /// <summary>
    ///     必填
    /// </summary>
    [SugarColumn(ColumnDescription = "必填")]
    public bool Required { get; set; }

    /// <summary>
    ///     是否显示
    /// </summary>
    [SugarColumn(ColumnDescription = "是否显示")]
    public bool Display { get; set; }

    /// <summary>
    ///     是否查询字段
    /// </summary>
    [SugarColumn(ColumnDescription = "是否查询字段")]
    public bool Check { get; set; }

    /// <summary>
    ///     自定义创建表管理Id
    /// </summary>
    [SugarColumn(ColumnDescription = "自定义创建表管理Id")]
    public long ManageTableId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public DateTime? CreatedTime { get; set; }

    [Navigate(NavigateType.OneToOne, nameof(ManageTableId))]
    public ManageTable ManageTable { get; set; }

    /// <summary>
    ///     整型配置
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public NumberModel NumberModel { get; set; }

    /// <summary>
    ///     字符串配置
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public StringModel StringModel { get; set; }

    /// <summary>
    ///     高精度配置
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DecimalModel DecimalModel { get; set; }

    /// <summary>
    ///     时间配置
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DateTimeModel DateTimeModel { get; set; }

    /// <summary>
    ///     维度配置
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DimensionModel DimensionModel { get; set; }

    /// <summary>
    ///     排序字段
    /// </summary>
    public int SortOrder { get; set; }
}

/// <summary>
///     整型
/// </summary>
public class NumberModel
{
    /// <summary>
    ///     最小值
    /// </summary>
    public short Min { get; set; }

    /// <summary>
    ///     最大值
    /// </summary>
    public long Max { get; set; }
}

/// <summary>
///     字符串
/// </summary>
public class StringModel
{
    /// <summary>
    ///     长度（最长1024）
    /// </summary>
    public short Length { get; set; }
}

/// <summary>
///     高精度类型
/// </summary>
public class DecimalModel
{
    /// <summary>
    ///     最小值
    /// </summary>
    public short Min { get; set; }

    /// <summary>
    ///     最大值
    /// </summary>
    public long Max { get; set; }

    /// <summary>
    ///     长度
    /// </summary>
    [Required]
    public short Length { get; set; }
}

/// <summary>
///     时间
/// </summary>
public class DateTimeModel
{
    /// <summary>
    ///     1.单日期；2：日期+时间；3：时间；4：时间戳
    /// </summary>
    [Required]
    public DateTimeModelTypeEnum DateType { get; set; }

    /// <summary>
    ///     是否使用系统时间
    /// </summary>
    public bool SystemTime { get; set; }
}

/// <summary>
///     维度
/// </summary>
public class DimensionModel
{
    /// <summary>
    ///     维度Id
    /// </summary>
    public long ManageDimensionId { get; set; }
}

/// <summary>
/// </summary>
public enum TableFieldEnum
{
    /// <summary>
    ///     整型
    /// </summary>
    [Description("整型")] Number = 1,

    /// <summary>
    ///     字符串
    /// </summary>
    [Description("字符串")] String = 2,

    /// <summary>
    ///     浮点型
    /// </summary>
    [Description("浮点型")] Double = 3,

    /// <summary>
    ///     高精度
    /// </summary>
    [Description("高精度")] Decimal = 4,

    /// <summary>
    ///     日期
    /// </summary>
    [Description("日期")] DateTime = 5,

    /// <summary>
    ///     枚举
    /// </summary>
    [Description("枚举")] Enums = 6,

    /// <summary>
    ///     维度
    /// </summary>
    [Description("维度")] Dimensionality = 7,

    /// <summary>
    ///     布尔型
    /// </summary>
    [Description("布尔型")] Bool = 8
}

/// <summary>
///     1.单日期；2：日期+时间；3：时间；4：时间戳
/// </summary>
public enum DateTimeModelTypeEnum
{
    /// <summary>
    ///     单日期
    /// </summary>
    [Description("单日期")] Date = 1,

    /// <summary>
    ///     日期+时间
    /// </summary>
    [Description("日期+时间")] DateTime = 2,

    /// <summary>
    ///     时间
    /// </summary>
    [Description("时间")] Time = 3,

    /// <summary>
    ///     时间戳
    /// </summary>
    [Description("时间戳")] TimeStamp = 4
}