# Feng-CloudIot 更新日志</a>

## 5.0.0

`2023.12.18`

⚡⚡⚡ 此版本为破环性更新，优化内容如下：（谨慎更新！谨慎更新！！谨慎更新！！！）

- 🌟 新增 增加任务模板
-
- 🌟 更新 依赖更新最新版本
- 🎯 优化 工作流执行流程
- 🐞 修复 `开启 Tagsview 图标` 时，`tagsView 右键菜单关闭` 报错问题
- 🌈 重构 任务流执行日志
- 🌈 重构 底层结构,增加租户

## 5.0.1

`2024.02.19`

⚡⚡⚡ 此版本无破环性更新

- 🌟 更新 依赖更新最新版本，.net6升级到.net8

- 🐞 修复 `调用开放api` 时，`401`问题
- 🐞 修复 `脚本调用读取时序库` 时，`异步`问题
- 🐞 修复 `批量删除物模型` 时，`Object`问题
- 🐞 修复 `工作流执行下写属性` 时，`下写不成功`问题
- 🐞 修复 禅道Bug【009】`一次报警会生成多行报警记录` `v5.0.1` ⏱️`2024-02-22`
- 🐞 修复 禅道Bug【010】`取消勾选【报警触发后需手动确认】按钮，还是需要手动确认掉` `v5.0.1` ⏱️`2024-02-22`
- 🐞 修复 `查询历史数据时间显示出错` `修正版本:v5.0.2` ⏱️`2024-02-27`
- 🐞 修复 `物实例编辑异常导致崩溃` `修正版本:v5.0.3` ⏱️`2024-02-29`
- 🐞 修复 `上传图片api404问题` `修正版本:v5.0.4` ⏱️`2024-03-04`
- 🐞 修复 `修复数据统计-数据查看-数据无法正常显示问题`  ⏱️`2024-03-08`
- 🐞 修复 `脚本中执行sql datetime类型无法正确转换问题`  ⏱️`2024-03-18`

## 5.0.5

`2024.03.27`

- 🌟 新增 `增加实体管理模块功能`
- 🌟 新增 `增加用户在线记录，用于后续单设备登录，站内信，消息通知`
- 🌟 更新 `调整了创建报警的逻辑代码，进行了封装`
- 🌟 更新 `默认日志对某些进行忽略`

- 🎯 优化 `工作流解析表达式代码封装`
- 🎯 优化 `增加打印SQL配置，通过配置决定是否打印SQL语句`
- 🎯 优化 `定时任务看板隐藏表头，优化显示内容`
-
- 🐞 修复 `默认SqlSugar中默认雪花id过长问题`  ⏱️`2024-05-06`
- 🐞 修复 `任务定义中多属性触发更新时没有正确全部取消`  ⏱️`2024-05-06`
- 🐞 修复 `定时任务无法正常初始化`  ⏱️`2024-05-06`
- 🐞 修复 `修复定时任务持久化问题`  ⏱️`2024-05-07`
- 🐞 修复 `脚本中操作本地数据库连接无效问题`  ⏱️`2024-05-08`

## 5.1.0

`2024.05.21`
⚡⚡⚡ 此版本为破环性更新，优化内容如下：（谨慎更新！谨慎更新！！谨慎更新！！！）
- 🌟 新增 `程序块模块`
- 🌟 新增 `任务配置增加分组，增加任务复制功能`
- 🌟 新增 `工作流增加数据处理能力`
- 🌟 新增 `任务流执行-错误日志列表Api,动作执行失败插入数据库记录`
- 
- 🌟 更新 `工作流调整触发,不满足时候原地等待`
- 🌟 更新 `更新底层依赖包`
- 🌟 更新 `工作流支持无参数触发`
- 🌟 更新 `工作流，任务定义支持触发程序块`
- 🌟 更新 `菜单种子数据调整`
-
- 🎯 优化 `统一调整了管理任务的启动类`
- 🎯 优化 `调整工作流日志打印格式,支持对象返回`
- 🎯 优化`工作流推送socket优化`  ⏱️`2024-06-11`
- 
- 🌈 重构 `重构任务配置模块` `数据库[business_taskdefinition]表字段有删除和新增,更新需要留意`
- 🌈 重构 `调整底层物相关模块设备,方便扩展后续使用`
-
- 🐞 修复 `底层方法GetJsonElementValue在极端情况下转number异常`  ⏱️`2024-05-31`
- 🐞 修复 `定时任务编辑后无法正常序列化`  ⏱️`2024-06-17`
- 🐞 修复 `定时任务无法正确返回结果`  ⏱️`2024-06-17`
- 🐞 修复 `修复DataBaseManager没有正常切换到默认数据库问题`  ⏱️`2024-06-19`
- 🐞 修复 `实体管理表不存在导致报错问题`  ⏱️`2024-06-20`
- 🐞 修复 `底层切换默认数据库异常问题`  ⏱️`2024-06-20`
- 🐞 修复 `历史数据导出失败问题`  ⏱️`2024-07-05`

## 5.1.1

`2024.07.18`
- 🌟 更新 `移除IotScript表相关功能，替换为程序块`  ⏱️`2024-07-18`

## 5.1.2

⚡⚡⚡ 此版本为破环性更新，优化内容如下：（谨慎更新！谨慎更新！！谨慎更新！！！）
`2024.07.22`
- 🌟 更新 `移除全部表默认字段IsDelete，代码中移除假删除过滤器，大幅度提升初始化效率`  ⏱️`2024-07-22`

## 5.1.3

`2024.07.24`
- 🌟 更新 `移除原来redis库，使用其他库，如果更新该版本需要替换配置中的Cache.json文件`  ⏱️`2024-07-24`
- 
## 5.1.4

`2024.07.30`
- 🐞 修复 `修复趋势图查找时序库字段大小写问题`  ⏱️`2024-07-30`
- 🐞 修复 `修复菜单修改应用导致的问题`  ⏱️`2024-08-26`

## 5.1.5

`2024.07.30`
- 🌟 新增 `增加数据api功能表和相关代码`
- 
- 🌟 更新 `openapi中部分查询api取消物实例必填的限制，不填写返回全部实例` ⏱️`2024-09-20 -wang` 

## 5.1.6

- 🌟 更新 `openapi中部分查询实时数据对字段进行调整，部分字段改为通过标识查询` ⏱️`2024-09-23`
- 🌟 更新 `物实例在线离线情况写入时序库只写入一次，不在进行多次写入` ⏱️`2024-09-23`
- 🌟 更新 `物模型，物实例等页面支持排序` ⏱️`2024-09-23`
- 🌟 更新 `mqtt依赖调整，数据接口对脚本调用依赖改成jint` ⏱️`2024-09-23`
- 
- 🐞 修复 `工作流中使用休眠时，某些情况下导致线程假死`  ⏱️`2024-09-23`

