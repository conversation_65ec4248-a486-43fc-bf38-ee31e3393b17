using System.Reflection;
using Furion.FriendlyException;
using IotPlatform.Core.Extension;
using OfficeOpenXml.FormulaParsing.Excel.Functions.DateTime;
using DateTime = IotPlatform.Core.Extension.DateTime;

Serve.Run(RunOptions.Default.AddWebComponent<WebComponent>());

public class WebComponent : IWebComponent
{
    public void Load(WebApplicationBuilder builder, ComponentContext componentContext)
    {
        StringExtension.Version = $"v{Assembly.GetExecutingAssembly().GetName().Version!.ToString()}"; 
        Console.WriteLine($"版本：{StringExtension.Version}");
        // 设置日志过滤
        builder.Logging.AddFilter((provider, category, logLevel) =>
        {
            return !new[] {"Microsoft.Hosting", "Microsoft.AspNetCore"}.Any(category.StartsWith) && logLevel >= LogLevel.Information;
        });

        // 设置接口超时时间和上传大小
        builder.Configuration.Get<WebHostBuilder>().ConfigureKestrel(u =>
        {
            u.Limits.KeepAliveTimeout = TimeSpan.FromMinutes(30);
            u.Limits.RequestHeadersTimeout = TimeSpan.FromMinutes(30);
            u.Limits.MaxRequestBodySize = null;
        });
    }
}