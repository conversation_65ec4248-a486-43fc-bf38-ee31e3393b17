{"$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json", "TDengine": {"Connection": "Host=***********;Port=6030;Username=root;Password=taosdata;Database=cloudIot"}, "Mqtt": {"Ip": "***********", "Port": 1883, "UserName": "admin", "Password": "fengedge", "Version": "v5", "ApiKey": "4eaaa5c3e882376b", "SecretKey": "CbLw75OuH9A3s4KiCrl9AXeUc4sKr1zApf8PIHxEZxEKE"}, "ConnectionStrings": {"EnableConsoleSql": true, "ConnectionConfigs": [{"ConfigId": "1300000000001", "DBName": "FengCloudIotV5", "DBType": "PostgreSQL", "Host": "**************", "Port": "5432", "UserName": "postgres", "Password": "Fh@201001.", "DBSchema": "public", "EnableInitTable": false, "EnableInitSeed": false}, {"ConfigId": "16545203149510", "DBName": "CloudDataModeling", "DBType": "PostgreSQL", "Host": "**************", "Port": "5432", "UserName": "postgres", "Password": "Fh@201001.", "DBSchema": "public", "EnableInitTable": false, "EnableInitSeed": false}]}}