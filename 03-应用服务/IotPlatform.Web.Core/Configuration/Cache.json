{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "Cache": {
    "Prefix": "fengIot_", // 全局缓存前缀
    "CacheType": "Redis", // Memory、Redis
    "Redis": {
      "Configuration": "server=120.26.67.4:6379;password=**********;db=5;", // Redis连接字符串
      "Prefix": "fengIot_", // Redis前缀（目前没用）
      "MaxMessageSize": "3145728" // 最大消息大小 默认1024 * 1024 *3
    }
  }
}