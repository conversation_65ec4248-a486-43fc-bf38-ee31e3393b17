using Common.Options;
using Extras.DatabaseAccessor.SqlSugar.Options;
using IotPlatform.Core.Option;
using Microsoft.Extensions.DependencyInjection;

namespace IotPlatform.Web.Core;

public static class ProjectOptions
{
    /// <summary>
    ///     注册项目配置选项
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddProjectOptions(this IServiceCollection services)
    {
        services.AddConfigurableOptions<CacheOptions>();
        services.AddConfigurableOptions<UploadOptions>();
        services.AddConfigurableOptions<TenantOptions>();
        services.AddConfigurableOptions<ConnectionStringsOptions>();
        services.AddConfigurableOptions<EventBusOptions>();
        return services;
    }
}