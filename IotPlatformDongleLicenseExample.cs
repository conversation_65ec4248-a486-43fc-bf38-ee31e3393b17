using System;
using System.Text;

/// <summary>
/// 示例：如何在IotPlatform.DongleLicense项目中使用相同的打印格式
/// 这个文件展示了如何将DongleTestConsole的打印格式移植到IotPlatform.DongleLicense项目
/// </summary>
public class IotPlatformDongleLicenseExample
{
    /// <summary>
    /// 在IotPlatform.DongleLicense项目的TestProgram.cs中使用的Main方法示例
    /// </summary>
    public static void ExampleMain(string[] args)
    {
        // 设置控制台编码为UTF8，确保中文字符正确显示
        Console.OutputEncoding = Encoding.UTF8;
        
        // 使用与DongleTestConsole相同的打印格式
        Console.WriteLine("=== 加密锁测试控制台程序 ===");
        Console.WriteLine("程序启动时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"));
        
        // 注意：这里需要调用DongleApiService的静态方法
        // Console.WriteLine("当前运行平台: " + DongleApiService.GetPlatformDescription());
        // Console.WriteLine("当前处理器架构: " + DongleApiService.GetProcessorArchitecture());
        // Console.WriteLine("编译时DLL文件: " + DongleApiService.GetCompileTimeDllName());
        // Console.WriteLine("运行时推荐DLL: " + DongleApiService.GetRuntimeDllName());

        // 检查编译时DLL和运行时推荐DLL是否一致
        // if (!DongleApiService.IsDllNameMatched())
        // {
        //     Console.WriteLine("⚠️  警告: 编译时DLL与运行时推荐DLL不一致!");
        //     Console.WriteLine("   建议使用对应平台的编译版本以获得最佳性能。");
        // }
        // else
        // {
        //     Console.WriteLine("✅ DLL文件匹配当前运行环境");
        // }
        
        Console.WriteLine();

        // 其余的测试逻辑...
        Console.WriteLine("=== 开始加密锁功能测试 ===");
        
        try
        {
            // var dongleService = new DongleApiService();
            // Console.WriteLine("✅ DongleApiService 实例创建成功");
            
            // 尝试枚举设备
            // Console.WriteLine("正在尝试枚举加密锁设备...");
            // var (result, dongleInfo, count) = dongleService.EnumDongle();
            
            // if (result == 0)
            // {
            //     Console.WriteLine($"✅ 枚举成功！找到 {count} 个设备");
            // }
            // else
            // {
            //     Console.WriteLine($"⚠️  枚举失败，错误代码: 0x{result:X8}");
            //     Console.WriteLine("这是正常的，因为可能没有连接加密锁设备或缺少相应的DLL文件");
            // }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 创建DongleApiService时发生异常: {ex.Message}");
            Console.WriteLine("这可能是因为缺少对应平台的DLL文件");
        }

        Console.WriteLine();
        Console.WriteLine("测试完成。按任意键退出...");
        Console.ReadKey();
    }
}

/// <summary>
/// 完整的IotPlatform.DongleLicense项目TestProgram.cs文件内容示例
/// 将此内容复制到IotPlatform.DongleLicense项目的TestProgram.cs文件中
/// </summary>
/*
using System;
using System.Text;
using IotPlatform.DongleLicense.Services;

namespace IotPlatform.DongleLicense
{
    /// <summary>
    /// 测试程序 - 验证跨平台DLL加载功能
    /// </summary>
    public class TestProgram
    {
        public static void Main(string[] args)
        {
            Console.OutputEncoding = Encoding.UTF8;
            Console.WriteLine("=== 加密锁测试控制台程序 ===");
            Console.WriteLine("程序启动时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"));
            Console.WriteLine("当前运行平台: " + DongleApiService.GetPlatformDescription());
            Console.WriteLine("当前处理器架构: " + DongleApiService.GetProcessorArchitecture());
            Console.WriteLine("编译时DLL文件: " + DongleApiService.GetCompileTimeDllName());
            Console.WriteLine("运行时推荐DLL: " + DongleApiService.GetRuntimeDllName());

            // 检查编译时DLL和运行时推荐DLL是否一致
            if (!DongleApiService.IsDllNameMatched())
            {
                Console.WriteLine("⚠️  警告: 编译时DLL与运行时推荐DLL不一致!");
                Console.WriteLine("   建议使用对应平台的编译版本以获得最佳性能。");
            }
            else
            {
                Console.WriteLine("✅ DLL文件匹配当前运行环境");
            }
            Console.WriteLine();

            // 显示详细的平台信息
            var platformInfo = DongleApiService.GetPlatformInfo();
            Console.WriteLine("=== 详细平台信息 ===");
            foreach (var kvp in platformInfo)
            {
                Console.WriteLine($"{kvp.Key}: {kvp.Value}");
            }
            Console.WriteLine();

            // 尝试创建DongleApiService实例
            try
            {
                var dongleService = new DongleApiService();
                Console.WriteLine("✅ DongleApiService 实例创建成功");
                
                // 尝试枚举设备（这可能会失败，因为没有实际的加密锁设备）
                Console.WriteLine("正在尝试枚举加密锁设备...");
                var (result, dongleInfo, count) = dongleService.EnumDongle();
                
                if (result == 0)
                {
                    Console.WriteLine($"✅ 枚举成功！找到 {count} 个设备");
                }
                else
                {
                    Console.WriteLine($"⚠️  枚举失败，错误代码: 0x{result:X8}");
                    Console.WriteLine("这是正常的，因为可能没有连接加密锁设备或缺少相应的DLL文件");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建DongleApiService时发生异常: {ex.Message}");
                Console.WriteLine("这可能是因为缺少对应平台的DLL文件");
            }

            Console.WriteLine();
            Console.WriteLine("测试完成。按任意键退出...");
            Console.ReadKey();
        }
    }
}
*/
