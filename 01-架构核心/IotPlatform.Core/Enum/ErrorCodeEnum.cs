namespace IotPlatform.Core.Enum;

/// <summary>
///     系统错误码
/// </summary>
[ErrorCodeType]
[Description("系统错误码")]
public enum ErrorCode
{
    /// <summary>
    /// 名称不能重复.
    /// </summary>
    [ErrorCodeItemMetadata("名称不能重复")]
    COM1032,
    
    /// <summary>
    /// 视图最多新建5个
    /// </summary>
    [ErrorCodeItemMetadata("视图最多新建5个")]
    D3201,
    
    /// <summary>
    /// 找不到该应用
    /// </summary>
    [ErrorCodeItemMetadata("找不到该应用")]
    D4022,
    
    /// <summary>
    /// 应用不能为空
    /// </summary>
    [ErrorCodeItemMetadata("应用不能为空")]
    D4023,
    
    #region 门户 19

    /// <summary>
    /// 您没有此门户使用权限，请重新设置.
    /// </summary>
    [ErrorCodeItemMetadata("您没有此门户使用权限，请重新设置")]
    D1900,

    /// <summary>
    /// 门户分类不能为空.
    /// </summary>
    [ErrorCodeItemMetadata("门户分类不能为空")]
    D1901,

    /// <summary>
    /// 门户名称不能为空.
    /// </summary>
    [ErrorCodeItemMetadata("门户名称不能为空")]
    D1902,

    /// <summary>
    /// 门户编码不能为空.
    /// </summary>
    [ErrorCodeItemMetadata("门户编码不能为空")]
    D1903,

    /// <summary>
    /// 远程请求发生错误.
    /// </summary>
    [ErrorCodeItemMetadata("远程请求发生错误")]
    D1904,

    /// <summary>
    /// 远程获取不到地图数据.
    /// </summary>
    [ErrorCodeItemMetadata("获取不到数据")]
    D1905,

    /// <summary>
    /// 更新门户布局失败.
    /// </summary>
    [ErrorCodeItemMetadata("更新门户布局失败")]
    D1906,

    /// <summary>
    /// 同步门户失败.
    /// </summary>
    [ErrorCodeItemMetadata("同步门户失败")]
    D1907,

    /// <summary>
    /// 添加日程失败.
    /// </summary>
    [ErrorCodeItemMetadata("添加日程失败")]
    D1908,

    /// <summary>
    /// 添加日程日志失败.
    /// </summary>
    [ErrorCodeItemMetadata("添加日程日志失败")]
    D1909,

    /// <summary>
    /// 修改日程失败.
    /// </summary>
    [ErrorCodeItemMetadata("修改日程失败")]
    D1910,

    /// <summary>
    /// 修改日程日志失败.
    /// </summary>
    [ErrorCodeItemMetadata("修改日程日志失败")]
    D1911,

    /// <summary>
    /// 删除日程失败.
    /// </summary>
    [ErrorCodeItemMetadata("删除日程失败")]
    D1912,

    /// <summary>
    /// 删除日程参与人失败.
    /// </summary>
    [ErrorCodeItemMetadata("删除日程参与人失败")]
    D1913,

    /// <summary>
    /// 删除日程日志失败.
    /// </summary>
    [ErrorCodeItemMetadata("删除日程日志失败")]
    D1914,

    /// <summary>
    /// 门户名称不能重复.
    /// </summary>
    [ErrorCodeItemMetadata("门户名称不能重复")]
    D1915,

    /// <summary>
    /// 门户编码不能重复.
    /// </summary>
    [ErrorCodeItemMetadata("门户编码不能重复")]
    D1916,

    /// <summary>
    /// 门户关联删除提示.
    /// </summary>
    [ErrorCodeItemMetadata("此记录被“【{0}】应用门户”关联引用，不允许被删除")]
    D1917,

    /// <summary>
    /// 该日程已被删除.
    /// </summary>
    [ErrorCodeItemMetadata("该日程已被删除")]
    D1918,

    /// <summary>
    /// 该门户已删除.
    /// </summary>
    [ErrorCodeItemMetadata("该门户已删除")]
    D1919,

    #endregion
    
    /// <summary>
    /// 操作失败,无法移动同一文件下.
    /// </summary>
    [ErrorCodeItemMetadata("操作失败,无法移动同一文件下")]
    Ex0002,
    
    /// <summary>
    /// 操作失败,无法移动子文件.
    /// </summary>
    [ErrorCodeItemMetadata("操作失败,父级文件夹无法移动到子级文件夹中")]
    Ex0005,
    
    /// <summary>
    /// 删除失败,该文件夹下存在数据.
    /// </summary>
    [ErrorCodeItemMetadata("删除失败,该文件夹下存在数据")]
    Ex0006,
    
    /// <summary>
    /// 文件夹名称不能重复.
    /// </summary>
    [ErrorCodeItemMetadata("文件夹名称不能重复")]
    Ex0008,
    
    /// <summary>
    /// 预览失败，文件类型不支持.
    /// </summary>
    [ErrorCodeItemMetadata("预览失败，文件类型不支持")]
    D1802,
    
    /// <summary>
    /// 上传失败，文件格式不允许上传.
    /// </summary>
    [ErrorCodeItemMetadata("上传失败，文件格式不允许上传")]
    D1800,
    
    /// <summary>
    /// 数据超过1000条.
    /// </summary>
    [ErrorCodeItemMetadata("数据超过1000条")]
    D1423,
    
    /// <summary>
    /// 文件下载失败,链接已失效.
    /// </summary>
    [ErrorCodeItemMetadata("文件下载失败,链接已失效")]
    D1805,
    
    /// <summary>
    /// 请在数据库中添加对应的数据表.
    /// </summary>
    [ErrorCodeItemMetadata("请在数据库中添加对应的数据表\r\n\r\n")]
    D1521,
    
    /// <summary>
    /// 表名超过规定长度.
    /// </summary>
    [ErrorCodeItemMetadata("表名超过规定长度")]
    D1514,
    
    /// <summary>
    /// 删除表失败.
    /// </summary>
    [ErrorCodeItemMetadata("删除表失败")]
    D1500,
    
    /// <summary>
    /// 表存在数据,禁止操作.
    /// </summary>
    [ErrorCodeItemMetadata("表存在数据,禁止操作")]
    D1508,
    
    /// <summary>
    /// SQL语句需带上@formid条件
    /// </summary>
    [ErrorCodeItemMetadata("SQL语句需带上@formid条件")]
    D2802,
    
    /// <summary>
    /// 数据集名称已存在
    /// </summary>
    [ErrorCodeItemMetadata("数据集名称已存在")]
    D2801,
    
    /// <summary>
    /// 打印模板不存在.
    /// </summary>
    [ErrorCodeItemMetadata("打印模板不存在")]
    D9010,
    
    /// <summary>
    /// 已到达该模板复制上限，请复制源模板.
    /// </summary>
    [ErrorCodeItemMetadata("已到达该模板复制上限，请复制源模板")]
    COM1009,
    
    /// <summary>
    /// 预览失败,Sql语句包含敏感词汇.
    /// </summary>
    [ErrorCodeItemMetadata("预览失败,Sql语句包含敏感词汇")]
    xg1005,
    
    /// <summary>
    /// 修改状态失败.
    /// </summary>
    [ErrorCodeItemMetadata("修改状态失败")]
    COM1003,
    
    /// <summary>
    /// 密码错误.
    /// </summary>
    [ErrorCodeItemMetadata("密码错误")]
    D1418,
    
    /// <summary>
    ///     新增字段失败.
    /// </summary>
    [ErrorCodeItemMetadata("新增字段失败")] D1510,

    /// <summary>
    ///     列名超过规定长度.
    /// </summary>
    [ErrorCodeItemMetadata("列名超过规定长度")] D1515,

    /// <summary>
    ///     名称重复，请重新输入.
    /// </summary>
    [ErrorCodeItemMetadata("名称重复，请重新输入")] COM1024,

    /// <summary>
    ///     编码重复，请重新输入.
    /// </summary>
    [ErrorCodeItemMetadata("编码重复，请重新输入")] COM1025,

    /// <summary>
    ///     导入预览失败，数据读取错误.
    /// </summary>
    [ErrorCodeItemMetadata("预览失败 : 数据读取错误")]
    D1412,

    /// <summary>
    ///     导入预览失败，表头名称不可更改，表头行不能删除.
    /// </summary>
    [ErrorCodeItemMetadata("预览失败 : 表头名称不可更改,表头行不能删除")]
    D1410,

    /// <summary>
    ///     操作失败，未配置导出模板.
    /// </summary>
    [ErrorCodeItemMetadata("操作失败，未配置导出模板")]
    D1411,

    /// <summary>
    ///     无效链接.
    /// </summary>
    [ErrorCodeItemMetadata("无效链接")] D1420,

    /// <summary>
    ///     导入失败.
    /// </summary>
    [ErrorCodeItemMetadata("导入失败")] D3008,

    /// <summary>
    ///     导入文件功能类型错误.
    /// </summary>
    [ErrorCodeItemMetadata("导入文件功能类型错误")] D3009,

    /// <summary>
    ///     导入文件格式错误.
    /// </summary>
    [ErrorCodeItemMetadata("导入文件格式错误")] D3006,

    /// <summary>
    ///     文件删除失败.
    /// </summary>
    [ErrorCodeItemMetadata("文件流获取失败")] D1804,

    /// <summary>
    ///     文件删除失败.
    /// </summary>
    [ErrorCodeItemMetadata("文件删除失败")] D1803,

    /// <summary>
    ///     主键策略 表主键数据类型不支持.
    /// </summary>
    [ErrorCodeItemMetadata("主键策略:[{0}],表[ {1} ]主键设置不支持!")]
    D1409,

    /// <summary>
    ///     导入失败：{0}.
    /// </summary>
    [ErrorCodeItemMetadata("导入失败：{0}")] COM1020,

    /// <summary>
    ///     {0}.
    /// </summary>
    [ErrorCodeItemMetadata("{0}")] COM1018,

    /// <summary>
    ///     无表转有表失败.
    /// </summary>
    [ErrorCodeItemMetadata("无表转有表失败")] D1414,

    /// <summary>
    ///     未找到同步路径，请刷新页面
    /// </summary>
    [ErrorCodeItemMetadata("未找到同步路径，请刷新页面")]
    D4017,

    /// <summary>
    ///     无法发布空列表.
    /// </summary>
    [ErrorCodeItemMetadata("该模板内列表内容为空，无法发布！")]
    COM1014,

    /// <summary>
    ///     无法发布空表单.
    /// </summary>
    [ErrorCodeItemMetadata("该模板内表单内容为空，无法发布！")]
    COM1013,

    /// <summary>
    ///     复制模板 数据长度超过字段设定长度.
    /// </summary>
    [ErrorCodeItemMetadata("已到达该模板复制上限，请复制源模板")]
    D1403,

    /// <summary>
    ///     请至少选择一个数据表.
    /// </summary>
    [ErrorCodeItemMetadata("请至少选择一个数据表")] D1416,

    /// <summary>
    ///     该功能名称或编码已存在
    /// </summary>
    [ErrorCodeItemMetadata("该功能名称或编码已存在!")]
    D1406,

    /// <summary>
    ///     未能找到线上版本.
    /// </summary>
    [ErrorCodeItemMetadata("回滚失败,未能找到线上版本")]
    D1415,

    /// <summary>
    ///     操作失败.
    /// </summary>
    [ErrorCodeItemMetadata("操作失败")] COM1008,

    /// <summary>
    ///     并发锁定.
    /// </summary>
    [ErrorCodeItemMetadata("当前表单原数据已被调整,请重新进入该页面编辑并提交数据")]
    D1408,

    /// <summary>
    ///     唯一校验失败.
    /// </summary>
    [ErrorCodeItemMetadata("{0} 不能重复")] D1407,

    /// <summary>
    ///     主表未设置主键字段.
    /// </summary>
    [ErrorCodeItemMetadata("主表未设置主键字段")] D1402,

    /// <summary>
    ///     错误的模板设计,表关系不能多对多.
    /// </summary>
    [ErrorCodeItemMetadata("错误的模板设计,表关系不能多对多")]
    D1401,

    /// <summary>
    ///     Sql语法错误.
    /// </summary>
    [ErrorCodeItemMetadata("Sql语法错误")] D1511,

    /// <summary>
    ///     自增长ID字段数据类型必须为整形或长整型.
    /// </summary>
    [ErrorCodeItemMetadata("自增长ID字段数据类型必须为整形或长整型")]
    D1518,

    /// <summary>
    ///     数据库类型不支持自增长ID.
    /// </summary>
    [ErrorCodeItemMetadata("数据库类型不支持自增长ID")]
    D1519,

    /// <summary>
    ///     数据库连接失败
    /// </summary>
    [ErrorCodeItemMetadata("数据库连接失败")] D1507,

    /// <summary>
    ///     上传失败，图片格式不允许上传
    /// </summary>
    [ErrorCodeItemMetadata("上传失败，图片格式不允许上传")]
    D5013,

    /// <summary>
    ///     删除数据失败
    /// </summary>
    [ErrorCodeItemMetadata("删除数据失败")] COM1002,

    /// <summary>
    ///     新增数据失败
    /// </summary>
    [ErrorCodeItemMetadata("新增数据失败")] COM1000,

    /// <summary>
    ///     已存在同名或同编码数据
    /// </summary>
    [ErrorCodeItemMetadata("已存在同名或同编码数据")] Com1004,

    /// <summary>
    ///     修改数据失败
    /// </summary>
    [ErrorCodeItemMetadata("修改数据失败")] COM1001,

    /// <summary>
    ///     账号不存在
    /// </summary>
    [ErrorCodeItemMetadata("账号不存在")] D0009,

    /// <summary>
    ///     密码不正确
    /// </summary>
    [ErrorCodeItemMetadata("密码不正确")] D1000,

    /// <summary>
    ///     非法操作！禁止删除自己
    /// </summary>
    [ErrorCodeItemMetadata("非法操作，禁止删除自己")] D1001,

    /// <summary>
    ///     记录不存在
    /// </summary>
    [ErrorCodeItemMetadata("记录不存在")] D1002,

    /// <summary>
    ///     账号已存在
    /// </summary>
    [ErrorCodeItemMetadata("账号已存在")] D1003,

    /// <summary>
    ///     旧密码不匹配
    /// </summary>
    [ErrorCodeItemMetadata("旧密码输入错误")] D1004,

    /// <summary>
    ///     数据已存在
    /// </summary>
    [ErrorCodeItemMetadata("数据已存在")] D1006,

    /// <summary>
    ///     数据不存在或含有关联引用，禁止删除
    /// </summary>
    [ErrorCodeItemMetadata("数据不存在或含有关联引用，禁止删除")]
    D1007,

    /// <summary>
    ///     非法操作，未登录
    /// </summary>
    [ErrorCodeItemMetadata("非法操作，未登录")] D1011,

    /// <summary>
    ///     禁止删除超级管理员
    /// </summary>
    [ErrorCodeItemMetadata("禁止删除超级管理员")] D1014,

    /// <summary>
    ///     禁止修改超级管理员状态
    /// </summary>
    [ErrorCodeItemMetadata("禁止修改超级管理员状态")] D1015,

    /// <summary>
    ///     没有权限
    /// </summary>
    [ErrorCodeItemMetadata("没有权限")] D1016,

    /// <summary>
    ///     账号已冻结
    /// </summary>
    [ErrorCodeItemMetadata("账号已冻结")] D1017,

    /// <summary>
    ///     禁止删除管理员
    /// </summary>
    [ErrorCodeItemMetadata("禁止删除管理员")] D1018,

    /// <summary>
    ///     禁止删除系统管理员角色
    /// </summary>
    [ErrorCodeItemMetadata("禁止删除系统管理员角色")] D1019,

    /// <summary>
    ///     禁止为超级管理员分配角色
    /// </summary>
    [ErrorCodeItemMetadata("禁止为超级管理员分配角色")]
    D1022,

    /// <summary>
    ///     禁止删除默认租户
    /// </summary>
    [ErrorCodeItemMetadata("禁止删除默认租户")] D1023,

    /// <summary>
    ///     此角色下面存在账号禁止删除
    /// </summary>
    [ErrorCodeItemMetadata("此角色下面存在账号禁止删除")]
    D1025,

    /// <summary>
    ///     父机构不存在
    /// </summary>
    [ErrorCodeItemMetadata("父机构不存在")] D2000,

    /// <summary>
    ///     当前机构Id不能与父机构Id相同
    /// </summary>
    [ErrorCodeItemMetadata("当前机构Id不能与父机构Id相同")]
    D2001,

    /// <summary>
    ///     已有相同组织机构,编码或名称相同
    /// </summary>
    [ErrorCodeItemMetadata("已有相同组织机构,编码或名称相同")]
    D2002,

    /// <summary>
    ///     字典类型已存在
    /// </summary>
    [ErrorCodeItemMetadata("字典类型已存在,名称或编码重复")]
    D3001,

    /// <summary>
    ///     菜单已存在
    /// </summary>
    [ErrorCodeItemMetadata("菜单已存在")] D4000,

    /// <summary>
    ///     权限标识格式为空
    /// </summary>
    [ErrorCodeItemMetadata("权限标识格式为空")] D4003,

    /// <summary>
    ///     权限标识格式错误
    /// </summary>
    [ErrorCodeItemMetadata("权限标识格式错误 如xxx:xxx")]
    D4004,

    /// <summary>
    ///     父级菜单不能为当前节点，请重新选择父级菜单
    /// </summary>
    [ErrorCodeItemMetadata("父级菜单不能为当前节点，请重新选择父级菜单")]
    D4006,

    /// <summary>
    ///     不能移动根节点
    /// </summary>
    [ErrorCodeItemMetadata("不能移动根节点")] D4007,

    /// <summary>
    ///     默认激活系统只能有一个
    /// </summary>
    [ErrorCodeItemMetadata("默认激活系统只能有一个")] D5001,

    /// <summary>
    ///     该应用下有菜单禁止删除
    /// </summary>
    [ErrorCodeItemMetadata("该应用下有菜单禁止删除")] D5002,

    /// <summary>
    ///     文件不存在
    /// </summary>
    [ErrorCodeItemMetadata("文件不存在")] D8000,

    /// <summary>
    ///     不允许的文件类型
    /// </summary>
    [ErrorCodeItemMetadata("不允许的文件类型")] D8001,

    /// <summary>
    ///     文件超过允许大小
    /// </summary>
    [ErrorCodeItemMetadata("文件超过允许大小")] D8002,

    /// <summary>
    ///     文件后缀错误
    /// </summary>
    [ErrorCodeItemMetadata("文件后缀错误")] D8003,

    /// <summary>
    ///     已存在同名或同编码参数配置
    /// </summary>
    [ErrorCodeItemMetadata("已存在同名或同编码参数配置")]
    D9000,

    /// <summary>
    ///     禁止删除系统参数
    /// </summary>
    [ErrorCodeItemMetadata("禁止删除系统参数")] D9001,

    /// <summary>
    ///     已存在同名的租户
    /// </summary>
    [ErrorCodeItemMetadata("已存在同名的租户")] D1300,

    /// <summary>
    ///     已存在同名的租户管理员
    /// </summary>
    [ErrorCodeItemMetadata("已存在同名的租户管理员")] D1301,

    /// <summary>
    ///     该类型不存在
    /// </summary>
    [ErrorCodeItemMetadata("该类型不存在")] D1501,

    /// <summary>
    ///     该字段不存在
    /// </summary>
    [ErrorCodeItemMetadata("该字段不存在")] D1502,

    /// <summary>
    /// 表已存在.
    /// </summary>
    [ErrorCodeItemMetadata("表已存在")]
    D1503,
    
    /// <summary>
    ///     该实体不存在
    /// </summary>
    [ErrorCodeItemMetadata("该实体不存在")] D1504,

    /// <summary>
    /// 数据库类型不支持.
    /// </summary>
    [ErrorCodeItemMetadata("数据库类型不支持")]
    D1505,

    /// <summary>
    /// 单据规则已引用，无法删除.
    /// </summary>
    [ErrorCodeItemMetadata("单据规则已引用，无法删除")]
    BR0001,
    
    /// <summary>
    ///     检测数据不存在
    /// </summary>
    [ErrorCodeItemMetadata("检测数据不存在")] COM1005,

    /// <summary>
    ///     默认租户状态禁止修改
    /// </summary>
    [ErrorCodeItemMetadata("默认租户状态禁止修改")] Z1001,

    /// <summary>
    ///     租户已禁用
    /// </summary>
    [ErrorCodeItemMetadata("租户已禁用")] Z1003,

}