namespace IotPlatform.Core.Attribute;

/// <summary>
///     开放api
/// </summary>
// [SuppressSniffer]
[AttributeUsage(AttributeTargets.Method)]
public class OpenApiAttribute : System.Attribute
{
    /// <summary>
    ///     api描述名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     模块
    /// </summary>
    public string Module { get; set; }

    /// <summary>
    ///     模块编码
    /// </summary>
    public string ModuleCore { get; set; }

    /// <summary>
    ///     Http请求方式:1:GET;2POST
    /// </summary>
    public HttpRequestTypeEnum HttpRequestType { get; set; } = HttpRequestTypeEnum.Get;
}

/// <summary>
///     Http请求方式:1:GET;2:POST
/// </summary>
public enum HttpRequestTypeEnum
{
    [Description("GET")] Get = 1,
    [Description("POST")] Post = 2
}