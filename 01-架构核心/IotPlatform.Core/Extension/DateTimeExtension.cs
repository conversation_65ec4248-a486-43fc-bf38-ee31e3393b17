namespace IotPlatform.Core.Extension;

/// <summary>
///     时间拓展类
/// </summary>
public static class DateTime
{
    public static System.DateTime Now()
    {
        return System.DateTime.Now;
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public static System.DateTime ShangHai()
    {
        try
        {
            // 设置时区为 Asia/Shanghai
            TimeZoneInfo timeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 获取当前时间的 UTC 时间
            System.DateTime utcTime = System.DateTime.UtcNow;
            // 将 UTC 时间转换为指定时区的本地时间
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone);
        }
        catch
        {
            return System.DateTime.Now;
        }
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public static string ShangHaiString()
    {
        try
        {
            // 设置时区为 Asia/Shanghai
            TimeZoneInfo timeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 获取当前时间的 UTC 时间
            System.DateTime utcTime = System.DateTime.UtcNow;
            // 将 UTC 时间转换为指定时区的本地时间
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            return System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    public static string NowString()
    {
        System.DateTime time = System.DateTime.Now;
        return time.ToString("yyyy-MM-dd HH:mm:ss");
    }

    /// <summary>
    ///     时间戳转为C#格式时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static System.DateTime ToTime(double timeStamp)
    {
        System.DateTime dtDateTime = new System.DateTime(1970, 1, 1);
        dtDateTime = dtDateTime.AddMilliseconds(timeStamp).ToLocalTime();
        return dtDateTime;
    }

    /// <summary>
    ///     时间戳转为C#格式时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static System.DateTime ToTime(object timeStamp)
    {
        long corTimeStamp = (long) timeStamp;
        System.DateTime dtDateTime = new System.DateTime(1970, 1, 1);
        dtDateTime = dtDateTime.AddMilliseconds(corTimeStamp).ToLocalTime();
        return dtDateTime;
    }

    /// <summary>
    ///     时间戳转为C#格式时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static System.DateTime ToTime(long timeStamp)
    {
        double douUnixTimeStamp = Convert.ToDouble(timeStamp);
        System.DateTime dtDateTime = new System.DateTime(1970, 1, 1);
        dtDateTime = dtDateTime.AddMilliseconds(douUnixTimeStamp).ToLocalTime();
        return dtDateTime;
    }

    /// <summary>
    /// 将时间戳转为DateTime.
    /// </summary>
    /// <param name="timeStamp">时间戳.</param>
    /// <returns></returns>
    public static System.DateTime TimeStampToDateTime(this string timeStamp)
    {
        try
        {
            DateTimeOffset dto = DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(timeStamp));
            return dto.ToLocalTime().DateTime;
        }
        catch (Exception)
        {
            throw;
        }
    }
    
    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <param name="time"> DateTime时间格式</param>
    /// <returns>Unix时间戳格式</returns>
    public static string ToTsStr(System.DateTime time)
    {
        TimeSpan delta = time - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds).ToString();
    }

    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    public static string ToTsStr()
    {
        System.DateTime time = Now();
        TimeSpan delta = time - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds).ToString();
    }

    /// <summary>
    ///     将时间戳转换成北京时间
    /// </summary>
    /// <param name="timestamp"></param>
    /// <param name="type">返回格式,1:datetime；2string</param>
    /// <returns></returns>
    public static object ToShanghai(long timestamp, int type = 1)
    {
        try
        {
            // 将毫秒时间戳转换为DateTimeOffset对象
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(timestamp);
            // 获取北京时区的TimeZoneInfo对象
            TimeZoneInfo beijingTimeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 将DateTimeOffset对象转换为北京时间
            DateTimeOffset beijingTime = TimeZoneInfo.ConvertTime(dateTimeOffset, beijingTimeZone);
            return type == 1 ? beijingTime : beijingTime.ToString("yyyy-MM-dd HH:mm:ss:fff");
        }
        catch
        {
            return type == 1 ? System.DateTime.Now : System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff");
        }
    }

    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <param name="time"> DateTime时间格式</param>
    /// <returns>Unix时间戳格式</returns>
    public static long ToLong(System.DateTime time)
    {
        TimeSpan delta = time - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <param name="time"> DateTime时间格式</param>
    /// <returns>Unix时间戳格式</returns>
    public static long ToLong(string time)
    {
        TimeSpan delta = Convert.ToDateTime(time) - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒级时间戳
    ///     返回字符串类型
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    public static long ToLong()
    {
        TimeSpan delta = Now() - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒级时间戳
    /// </summary>
    /// <returns></returns>
    public static long TimeStamp()
    {
        TimeSpan delta = Now() - new System.DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒转天时分秒
    /// </summary>
    /// <param name="ms"></param>
    /// <returns></returns>
    public static string FormatTime(long ms)
    {
        int ss = 1000;
        int mi = ss * 60;
        int hh = mi * 60;
        int dd = hh * 24;

        long day = ms / dd;
        long hour = (ms - (day * dd)) / hh;
        long minute = (ms - (day * dd) - (hour * hh)) / mi;
        long second = (ms - (day * dd) - (hour * hh) - (minute * mi)) / ss;
        long milliSecond = ms - (day * dd) - (hour * hh) - (minute * mi) - (second * ss);

        string sDay = day < 10 ? "0" + day : "" + day; //天
        string sHour = hour < 10 ? "0" + hour : "" + hour; //小时
        string sMinute = minute < 10 ? "0" + minute : "" + minute; //分钟
        string sSecond = second < 10 ? "0" + second : "" + second; //秒
        string sMilliSecond = milliSecond < 10 ? "0" + milliSecond : "" + milliSecond; //毫秒
        sMilliSecond = milliSecond < 100 ? "0" + sMilliSecond : "" + sMilliSecond;

        return string.Format("{0} 天 {1} 小时 {2} 分 {3} 秒", sDay, sHour, sMinute, sSecond);
    }
    
    /// <summary>
    ///     毫秒转天时分秒
    /// </summary>
    /// <param name="msd"></param>
    /// <returns></returns>
    public static string FormatTime(double msd)
    {
        int ss = 1000;
        int mi = ss * 60;
        int hh = mi * 60;
        int dd = hh * 24;
        long ms = (long) msd;
        long day = ms / dd;
        long hour = (ms - (day * dd)) / hh;
        long minute = (ms - (day * dd) - (hour * hh)) / mi;
        long second = (ms - (day * dd) - (hour * hh) - (minute * mi)) / ss;
        long milliSecond = ms - (day * dd) - (hour * hh) - (minute * mi) - (second * ss);

        string sDay = day < 10 ? "0" + day : "" + day; //天
        string sHour = hour < 10 ? "0" + hour : "" + hour; //小时
        string sMinute = minute < 10 ? "0" + minute : "" + minute; //分钟
        string sSecond = second < 10 ? "0" + second : "" + second; //秒
        string sMilliSecond = milliSecond < 10 ? "0" + milliSecond : "" + milliSecond; //毫秒
        sMilliSecond = milliSecond < 100 ? "0" + sMilliSecond : "" + sMilliSecond;

        return string.Format("{0} 天 {1} 小时 {2} 分 {3} 秒", sDay, sHour, sMinute, sSecond);
    }

    /// <summary>
    ///     将string转换为DateTime，若失败则返回日期最小值
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    public static System.DateTime ParseToDateTime(this string str)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(str))
            {
                return System.DateTime.MinValue;
            }

            if (str.Contains("-") || str.Contains("/"))
            {
                return System.DateTime.Parse(str);
            }

            int length = str.Length;
            switch (length)
            {
                case 4:
                    return System.DateTime.ParseExact(str, "yyyy", CultureInfo.CurrentCulture);

                case 6:
                    return System.DateTime.ParseExact(str, "yyyyMM", CultureInfo.CurrentCulture);

                case 8:
                    return System.DateTime.ParseExact(str, "yyyyMMdd", CultureInfo.CurrentCulture);

                case 10:
                    return System.DateTime.ParseExact(str, "yyyyMMddHH", CultureInfo.CurrentCulture);

                case 12:
                    return System.DateTime.ParseExact(str, "yyyyMMddHHmm", CultureInfo.CurrentCulture);

                case 14:
                    return System.DateTime.ParseExact(str, "yyyyMMddHHmmss", CultureInfo.CurrentCulture);

                default:
                    return System.DateTime.ParseExact(str, "yyyyMMddHHmmss", CultureInfo.CurrentCulture);
            }
        }
        catch
        {
            return System.DateTime.MinValue;
        }
    }
}