<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <NoWarn>1701;1702;1591;8632</NoWarn>
        <ImplicitUsings>enable</ImplicitUsings>
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <Nullable>disable</Nullable>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
      <DocumentationFile />
    </PropertyGroup>

    <ItemGroup>
        <None Remove="IotPlatform.Core.xml"/>
        <None Remove="IotPlatform.Core.csproj.DotSettings"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="AspectCore.Extensions.Reflection" Version="2.4.0"/>
        <PackageReference Include="DynamicExpresso.Core" Version="2.18.0" />
        <PackageReference Include="Furion.Extras.Authentication.JwtBearer" Version="********" />
        <PackageReference Include="Furion.Extras.ObjectMapper.Mapster" Version="********" />
        <PackageReference Include="Furion.Pure" Version="********" />
        <PackageReference Include="IPTools.China" Version="1.6.0"/>
        <PackageReference Include="Jint" Version="4.2.2" />
        <PackageReference Include="Magicodes.IE.Excel" Version="*******" />
        <PackageReference Include="Magicodes.IE.Pdf" Version="*******" />
        <PackageReference Include="MiniExcel" Version="1.38.0" />
        <PackageReference Include="MQTTnet.AspNetCore" Version="4.3.7.1207" />
        <PackageReference Include="NewLife.Redis" Version="6.1.2025.411" />
        <PackageReference Include="PinYinConverterCore" Version="1.0.2"/>
        <PackageReference Include="SqlSugar.TDengineCore" Version="4.18.6" />
        <PackageReference Include="UAParser" Version="3.1.47"/>
        <PackageReference Include="Yitter.IdGenerator" Version="1.0.15"/>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Services\" />
    </ItemGroup>

</Project>
