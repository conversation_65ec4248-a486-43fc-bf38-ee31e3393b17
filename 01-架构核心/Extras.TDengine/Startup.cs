using Extras.TDengine.Event;
using Microsoft.Extensions.DependencyInjection;

namespace Extras.TDengine;

/// <summary>
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册事件订阅者(支持自定义消息队列组件)
        services.AddEventBus(builder => { builder.AddSubscriber<TDengineEventSubscriber>(); });
    }
}