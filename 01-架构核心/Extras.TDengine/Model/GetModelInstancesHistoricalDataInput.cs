namespace Extras.TDengine.Model;

/// <summary>
///     查询同一模型的一组物实例的历史数据
/// </summary>
public class GetModelInstancesHistoricalDataInput
{
    /// <summary>
    ///     模型Id
    /// </summary>
    [Description("模型Id")]
    [Required]
    public string ModelId { get; set; }

    /// <summary>
    ///     物实例标识集合（空返回全部）
    /// </summary>
    [Description("物实例标识集合（空返回全部）")]
    public List<string> ThingIds { get; set; } = new();

    /// <summary>
    ///     物实例属性集合（空返回全部）
    /// </summary>
    [Description("物实例属性集合（空返回全部）")]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Description("开始时间(毫秒时间戳)")]
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Description("结束时间(毫秒时间戳)")]
    [Required]
    public long EndTime { get; set; }

    /// <summary>
    ///     查询返回的数据最大条数
    /// </summary>
    [Description("查询返回的数据最大条数(默认是1000，默认配置下limit不能超过100000)")]
    public int Limit { get; set; } = 1000;
}

/// <summary>
///     物实例历史数据查询
/// </summary>
public class HistoricalDataInput
{
    /// <summary>
    ///     物标识
    /// </summary>
    [Required]
    public string ThingName { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Required]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Required]
    public long EndTime { get; set; }

    /// <summary>
    ///     查询返回的数据页码
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    ///     查询返回的数据最大条数
    /// </summary>
    public int PageNo { get; set; } = 1;

    /// <summary>
    ///     默认倒序
    /// </summary>
    public string Sort { get; set; } = "DESC";

    /// <summary>
    ///     查询条件
    /// </summary>
    public string? Filter { get; set; }
}

/// <summary>
///     物实例历史数据导出
/// </summary>
public class HistorianExportInput
{
    /// <summary>
    ///     物实例Id
    /// </summary>
    [Required]
    public long ThingId { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Required]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Required]
    public long EndTime { get; set; }

    /// <summary>
    ///     默认倒序
    /// </summary>
    public string Sort { get; set; } = "DESC";
}