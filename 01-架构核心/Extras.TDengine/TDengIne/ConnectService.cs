using DbType = SqlSugar.DbType;

namespace Extras.TDengine.TDengIne;

/// <summary>
///     TDengIne连接
/// </summary>
public class ConnectService : ISingleton
{
    private static readonly SqlSugarScope Db = new(new ConnectionConfig
    {
        ConfigId = "TDengIne",
        DbType = DbType.TDengine,
        ConnectionString = App.Configuration["TDengine:Connection"],
        IsAutoCloseConnection = true,
        ConfigureExternalServices = new ConfigureExternalServices
        {
            EntityService = (type, column) =>
            {
                if ((type.PropertyType.IsGenericType && type.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                    || (type.PropertyType == typeof(string) && type.GetCustomAttribute<RequiredAttribute>() == null))
                {
                    column.IsNullable = true;
                }

                column.SqlParameterDbType ??= typeof(CommonPropertyConvert);
            }
        }
    }, db =>
    {
        //如果用单例配置要统一写在这儿
        db.Aop.OnLogExecuting = (sql, pars) =>
        {
            if (sql.StartsWith("SELECT"))
            {
                Console.ForegroundColor = ConsoleColor.Green;
            }

            if (sql.StartsWith("UPDATE") || sql.StartsWith("INSERT"))
            {
                Console.ForegroundColor = ConsoleColor.White;
            }

            if (sql.StartsWith("DELETE"))
            {
                Console.ForegroundColor = ConsoleColor.Blue;
            }
        };
    });

    public ConnectService()
    {
        try
        {
            // 创建数据库
            Db.DbMaintenance.CreateDatabase();
        }
        catch (Exception)
        {
            // ignored
        }
    }

    public ISqlSugarClient Connect()
    {
        return Db;
    }
}