using DateTime = System.DateTime;

namespace Extras.TDengine.TDengIne;

/// <summary>
///     执行TDengIne的命令
/// </summary>
public class ExecuteService : ISingleton
{
    /// <summary>
    ///     TDengIne的连接
    /// </summary>
    private readonly ConnectService _connectService;

    private readonly ILogger<ExecuteService> _logger;

    public ExecuteService(ConnectService connectService, ILogger<ExecuteService> logger)
    {
        _connectService = connectService;
        _logger = logger;
    }

    /// <summary>
    ///     执行插入sql
    /// </summary>
    /// <param name="sql"></param>
    public bool Insert(string sql)
    {
        try
        {
            if (!sql.ToLower().StartsWith("insert"))
            {
                throw Oops.Oh("禁止使用非insert语法！");
            }

            int res = _connectService.Connect().CopyNew().Ado.ExecuteCommand(sql);
            return res > 0;
        }
        catch (Exception e)
        {
            _logger.LogError($"【TDengIne】 insert Error:{e.Message},SQL：{sql}");
            return false;
        }
    }

    /// <summary>
    ///     执行SQL语句
    /// </summary>
    /// <param name="sql"></param>
    public bool ExecuteCommand(string sql)
    {
        try
        {
            int res = _connectService.Connect().CopyNew().Ado.ExecuteCommand(sql);
            return res > 0;
        }
        catch (Exception e)
        {
            _logger.LogError($"【TDengIne】  执行SQL语句 Error:{e.Message}");
            return false;
        }
    }

    /// <summary>
    ///     执行SQL语句
    /// </summary>
    /// <param name="sql"></param>
    public bool ExecuteCommandNoTry(string sql)
    {
        int res = _connectService.Connect().CopyNew().Ado.ExecuteCommand(sql);
        return res > 0;
    }

    /// <summary>
    ///     执行读取SQL语句,返回一条数据
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public Dictionary<string, object> First(string query)
    {
        Dictionary<string, object> output = new();

        if (!query.ToUpper().StartsWith("SELECT"))
        {
            throw Oops.Oh("仅允许查询数据,SQL必须是SELECT开始！");
        }

        DataTable res = _connectService.Connect().Ado.GetDataTable(query);
        if (res == null)
        {
            return output;
        }

        foreach (DataRow dr in res.Rows)
        {
            foreach (DataColumn dc in res.Columns)
            {
                output.Add(dc.ColumnName, Convert.IsDBNull(dr[dc.ColumnName]) ? null : dr[dc.ColumnName]);
            }

            return output;
        }

        return output;
    }

    /// <summary>
    ///     执行读取SQL语句
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic Columns(string query)
    {
        dynamic output = new ExpandoObject();
        query = query.TrimStart();
        if (!query.ToUpper().StartsWith("SELECT"))
        {
            throw Oops.Oh("仅允许查询数据,SQL必须是SELECT开始！");
        }

        _logger.LogInformation($"TD查询数据SQL：{query}");
        // 指定库中查询数据
        DataTable res = _connectService.Connect().Ado.GetDataTable(query);
        if (res == null)
        {
            output.Columns = Array.Empty<string>();
            output.Rows = Array.Empty<string>();
            return output;
        }

        output.Columns = (from DataColumn dc in res.Columns select dc.ColumnName).ToList();

        output.Rows = (from DataRow dr in res.Rows
                       select (from DataColumn dc in res.Columns
                               select
                                   Convert.IsDBNull(dr[dc.ColumnName])
                                       ? null
                                       : dr[dc.ColumnName] is DateTime
                                           ? ((DateTime)dr[dc.ColumnName]).ToString("yyyy-MM-dd HH:mm:ss:fff")
                                           : dr[dc.ColumnName]
                           ).Cast<dynamic>().ToList()).ToList();
        return output;
    }

    /// <summary>
    ///     执行读取SQL语句
    /// </summary>
    /// <param name="query">执行Sql</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public List<Dictionary<string, object?>> Select(string query)
    {
        query = query.TrimStart();
        if (!query.ToUpper().StartsWith("SELECT"))
        {
            throw Oops.Oh("仅允许查询数据,SQL必须是SELECT开始！");
        }

        List<Dictionary<string, object>> output = new();
        // 指定库中查询数据
        DataTable res = _connectService.Connect().Ado.GetDataTable(query);
        if (res == null)
        {
            return output;
        }

        foreach (DataRow dr in res.Rows)
        {
            Dictionary<string, object> outputDic = new(StringComparer.OrdinalIgnoreCase);
            foreach (DataColumn dc in res.Columns)
            {
                outputDic.Add(dc.ColumnName, Convert.IsDBNull(dr[dc.ColumnName]) ? null : dr[dc.ColumnName]);
            }

            output.Add(outputDic);
        }

        return output;
    }

    /// <summary>
    /// 不限制select开头的查询
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    public List<Dictionary<string, object>> SelectNoLimit(string query)
    {
        query = query.TrimStart();

        List<Dictionary<string, object>> output = new();
        // 指定库中查询数据
        DataTable res = _connectService.Connect().Ado.GetDataTable(query);
        if (res == null)
        {
            return output;
        }

        foreach (DataRow dr in res.Rows)
        {
            Dictionary<string, object> outputDic = new(StringComparer.OrdinalIgnoreCase);
            foreach (DataColumn dc in res.Columns)
            {
                outputDic.Add(dc.ColumnName, Convert.IsDBNull(dr[dc.ColumnName]) ? null : dr[dc.ColumnName]);
            }

            output.Add(outputDic);
        }

        return output;
    }

    /// <summary>
    ///     返回物实例最近一条数据
    /// </summary>
    /// <param name="deviceList">设备名称集合</param>
    /// <param name="properList">点位名称集合</param>
    /// <param name="filter">过滤无效数据</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic Last(object deviceList, object? properList = null, bool filter = false)
    {
        Dictionary<string, Dictionary<string, object>> output = new();
        List<string> deviceNames = deviceList.GetJsonElementValue().Adapt<List<string>>();
        List<string>? propers = null;
        if (properList != null)
        {
            propers = properList.GetJsonElementValue().Adapt<List<string>>();
        }

        foreach (string deviceName in deviceNames)
        {
            try
            {
                string sql = "SELECT last_row(";
                if (propers == null)
                {
                    sql += $"*) FROM {deviceName} WHERE deviceName='{deviceName}'";
                }
                else
                {
                    sql += "ts";

                    sql = propers.Aggregate(sql, (current, propertie) => current + $",`{propertie}` ");
                    sql += $") FROM {deviceName} WHERE deviceName='{deviceName}'";
                }
                sql += filter ? " and `cloudTime` != null;" : ";";

                Dictionary<string, object> data = First(sql);
                Dictionary<string, object> newData = new();
                foreach ((string key, object val) in data)
                {
                    newData.Add(key.Contains("last_row") ? key.Replace("last_row(", "").Replace(")", "") : key, val);
                }

                output.Add(deviceName, newData);
            }
            catch (Exception e)
            {
                output.Add(deviceName, new Dictionary<string, object>());
                _logger.LogError($"【返回物实例最近一条数据】 Error:{e.Message}");
            }
        }

        return output;
    }

    /// <summary>
    ///     返回物实例最近一条数据
    /// </summary>
    /// <param name="deviceName">设备名称</param>
    /// <param name="properList">点位名称集合</param>
    /// <param name="filter">过滤无效数据</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic Last(string deviceName, object? properList = null, bool filter = false)
    {
        Dictionary<string, Dictionary<string, object>> output = new();
        List<string>? propers = null;
        if (properList != null)
        {
            propers = properList.GetJsonElementValue().Adapt<List<string>>();
        }

        string sql = "SELECT last_row(";
        if (propers == null)
        {
            sql += $"*) FROM {deviceName} WHERE deviceName='{deviceName}'";
        }
        else
        {
            sql += "ts";
            sql = propers.Aggregate(sql, (current, propertie) => current + $",`{propertie}` ");
            sql += $") FROM {deviceName} WHERE deviceName='{deviceName}'";
        }
        sql += filter ? " and `cloudTime` != null;" : ";";

        try
        {
            Dictionary<string, object> data = First(sql);
            Dictionary<string, object> newData = new();
            foreach ((string key, object val) in data)
            {
                newData.Add(key.Contains("last_row") ? key.Replace("last_row(", "").Replace(")", "") : key, val);
            }

            output.Add(deviceName, newData);
        }
        catch (Exception e)
        {
            _logger.LogError($"【返回物实例最近一条数据】 Error:{e.Message}");
            output.Add(deviceName, new Dictionary<string, object>());
        }

        return output;
    }

    /// <summary>
    ///     查询一组物实例的历史数据
    /// </summary>
    /// <param name="deviceList">设备名称集合</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="limit">返回数量</param>
    /// <param name="properList">点位名称集合</param>
    /// <returns></returns>
    public dynamic Historical(object deviceList, long startTime, long endTime, int limit = 20, object? properList = null)
    {
        Dictionary<string, List<Dictionary<string, object>>> output = new();
        List<string> deviceNames = deviceList.GetJsonElementValue().Adapt<List<string>>();
        List<string>? propers = null;
        if (properList != null)
        {
            propers = properList.GetJsonElementValue().Adapt<List<string>>();
        }

        foreach (string deviceName in deviceNames)
        {
            try
            {
                string sql = "SELECT ";
                if (propers == null)
                {
                    sql +=
                        $"* FROM {deviceName} WHERE ts>={startTime} and ts<={endTime} and deviceName='{deviceName}' limit {limit};";
                }
                else
                {
                    sql += "ts";
                    sql = propers.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
                    sql +=
                        $"FROM {deviceName} WHERE ts>={startTime} and ts<={endTime} and deviceName='{deviceName}' limit {limit};";
                }

                output.Add(deviceName, Select(sql));
            }
            catch (Exception e)
            {
                output.Add(deviceName, new List<Dictionary<string, object>>());
                _logger.LogError($"【查询一组物实例的历史数据】 Error:{e.Message}");
            }
        }

        return output;
    }

    /// <summary>
    ///     查询指定物实例的历史数据
    /// </summary>
    /// <param name="tableName">设备名称</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="limit">返回数量</param>
    /// <param name="properList">点位名称集合</param>
    /// <returns></returns>
    public dynamic Historical(string tableName, long startTime, long endTime, int limit = 20, object? properList = null)
    {
        List<string>? propers = null;
        if (properList != null)
        {
            propers = properList.GetJsonElementValue().Adapt<List<string>>();
        }

        string sql = "SELECT ";
        if (propers == null)
        {
            sql +=
                $"* FROM {tableName} WHERE ts>={startTime} and ts<={endTime} and deviceName='{tableName}' limit {limit};";
        }
        else
        {
            sql += "ts";
            sql = propers.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
            sql +=
                $"FROM {tableName} WHERE ts>={startTime} and ts<={endTime} and deviceName='{tableName}' limit {limit};";
        }

        try
        {
            List<Dictionary<string, object>> output = Select(sql);
            return output;
        }
        catch (Exception e)
        {
            _logger.LogError($"【查询指定物实例的历史数据】 Error:{e.Message}");
            return new List<Dictionary<string, object>>();
        }
    }

    /// <summary>
    ///     查询物实例的时间窗口数据
    /// </summary>
    /// <param name="deviceList">设备名称集合</param>
    /// <param name="properList">点位名称集合</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns></returns>
    public dynamic Window(object deviceList, long startTime, long endTime, object properList)
    {
        Dictionary<string, object> output = new();
        List<string> deviceNames = deviceList.GetJsonElementValue().Adapt<List<string>>();
        List<string> propers = properList.GetJsonElementValue().Adapt<List<string>>();
        foreach (string deviceName in deviceNames)
        {
            try
            {
                Dictionary<string, List<Dictionary<string, object>>> propertiesDic = new();
                foreach (string properties in propers)
                {
                    string sql =
                        $"SELECT * FROM (SELECT _wstart as fst,_WEND as ent,_WDURATION as totalTime, COUNT(*) AS cnt,  {properties} FROM {deviceName} where ts >= {startTime} and ts <= {endTime} and deviceName='{deviceName}'  STATE_WINDOW({properties})) t ; ";
                    propertiesDic.Add(properties, Select(sql));
                }

                output.Add(deviceName, propertiesDic);
            }
            catch (Exception e)
            {
                _logger.LogError($"【查询物实例的时间窗口数据】 Error:{e.Message}");
                output.Add(deviceName, new object());
            }
        }

        return output;
    }

    /// <summary>
    ///     查询指定物实例的时间聚集数据
    /// </summary>
    /// <param name="deviceList">设备名称集合</param>
    /// <param name="properList">点位名称集合</param>
    /// <param name="aggFunc">函数</param>
    /// <param name="interval">间隔</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="limit">返回数量</param>
    /// <returns></returns>
    public dynamic Aggregated(object deviceList, object properList, string aggFunc, string interval, long startTime, long endTime, int limit)
    {
        Dictionary<string, List<Dictionary<string, object>>> output = new();
        List<string> deviceNames = deviceList.GetJsonElementValue().Adapt<List<string>>();
        List<string> propers = properList.GetJsonElementValue().Adapt<List<string>>();
        foreach (string thingId in deviceNames)
        {
            foreach (string properties in propers)
            {
                string sql =
                    $"SELECT _wstart as time,{aggFunc}({properties}) FROM {thingId} WHERE ts>={startTime} and ts<={endTime} and deviceName='{thingId}' ";
                sql += $"INTERVAL({interval})";
                sql += $" limit {limit};";

                List<Dictionary<string, object>> dataList = Select(sql);
                foreach (Dictionary<string, object> data in dataList)
                {
                    Dictionary<string, object> newData = new();
                    foreach ((string key, object val) in data)
                    {
                        newData.Add(
                            key.Contains($"{aggFunc.ToLower()}")
                                ? key.Replace($"{aggFunc.ToLower()}(", "").Replace(")", "")
                                : key, val);
                    }

                    if (!output.ContainsKey(thingId))
                    {
                        output.TryAdd(thingId, new List<Dictionary<string, object>> { newData });
                    }
                    else
                    {
                        List<Dictionary<string, object>> thingDic = output[thingId];
                        thingDic.Add(newData);
                        output[thingId] = thingDic;
                    }
                }
            }
        }

        return output;
    }
}