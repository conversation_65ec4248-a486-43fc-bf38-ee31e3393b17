using Extras.MQTT.Dto;

namespace Extras.MQTT.Models;
public class Driver
{
    /// <summary>
    /// 驱动名称
    /// </summary>
    public string DriverName { get; set; }
    /// <summary>
    /// 驱动ID
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// 驱动类型
    /// </summary>
    public int DriverType { get; set; }
    /// <summary>
    /// 方法
    /// </summary>
    public List<Method> Methods { get; set; }
    /// <summary>
    /// 功能
    /// </summary>
    public List<Func> Funcs { get; set; }
    /// <summary>
    /// 配置
    /// </summary>
    public List<Config> Configs { get; set; }
}

public class Config
{
    public string DeviceConfigName { get; set; }
    public string Description { get; set; }
    public string Value { get; set; }
    public string EnumInfo { get; set; }
    public long DeviceId { get; set; }
    public string Device { get; set; }
    public string GroupName { get; set; }
    public string Remark { get; set; }
    public bool IsRequired { get; set; }
    public bool Display { get; set; }
    public string DisplayExpress { get; set; }
    public int Order { get; set; }
    public string Type { get; set; }
    public long Id { get; set; }
}


public class Func
{
    public string Code { get; set; }
    public string Text { get; set; }
    public string Value { get; set; }
    public string Type { get; set; }
    public bool Required { get; set; }
    public bool Display { get; set; }
    public string DisplayExpress { get; set; }
    public string DefaultValue { get; set; }
}

public class Method
{
    public string Identifier { get; set; }
    public string Text { get; set; }
    public string Value { get; set; }
    public string Desc { get; set; }
    public TransPondDataTypeEnum TransitionType { get; set; }
    public bool Filter { get; set; }
    public string FilterField { get; set; }
    public string FilterValue { get; set; }
}
