using System.Text;
using Common.Hubs;
using Common.Models;
using FengLink_MQTT;
using FengLink_MQTT.Model;
using Furion.JsonSerialization;
using Furion.TaskQueue;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Extensions.Rpc;
using MQTTnet.Protocol;
using MQTTnet.Server;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ParamValue = Common.Models.ParamValue;

namespace Extras.MQTT;

public class MqttService : IHostedService
{
    /// <summary>
    /// </summary>
    private readonly MqttServer _mqttService;

    /// <summary>
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    ///     Socket推送
    /// </summary>
    private readonly SocketSingleton _socketService;

    private readonly string _dataStoragePath;
    private readonly ILogger<MqttService> _logger;
    private readonly MqttConfig _mqttConfig;

    private Dictionary<string, dynamic> _gatewayConfigs = new();
    private const string GATEWAY_CONFIG_FILE = "gateway_configs.json";

    /// <summary>
    /// </summary>
    public MqttService(ISqlSugarClient db, ILogger<MqttService> logger, IConfiguration configuration, SocketSingleton socketService, MqttConfig mqttConfig)
    {
        _db = db;
        _logger = logger;
        _socketService = socketService;
        _mqttConfig = mqttConfig;
        _dataStoragePath = configuration.GetValue<string>("DataStorage:BasePath") ?? "Data";

        // 确保存储目录存在
        Directory.CreateDirectory(_dataStoragePath);

        // Configure MQTT server.
        MqttServerOptionsBuilder? optionsBuilder = new MqttServerOptionsBuilder()
            .WithDefaultEndpoint()
            .WithDefaultEndpointPort(_mqttConfig.Port)
            .WithConnectionBacklog(_mqttConfig.ConnectionBacklog);

        MqttServerOptions? options = optionsBuilder.Build();
        _mqttService = new MqttFactory().CreateMqttServer(options);
        _mqttService.ClientConnectedAsync += MqttServer_ClientConnectedAsync;
        _mqttService.ClientDisconnectedAsync += MqttServer_ClientDisconnectedAsync;
        _mqttService.ValidatingConnectionAsync += MqttServer_ValidatingConnectionAsync;
        // _mqttService.ApplicationMessageNotConsumedAsync += MqttServer_ApplicationMessageNotConsumedAsync;
        _mqttService.InterceptingPublishAsync += MqttServer_InterceptingPublishAsync;
        _mqttService.StartedAsync += MqttServer_StartedAsync;

        // 加载网关配置
        LoadGatewayConfigs().Wait();
    }

    #region 开放方法

    /// <summary>
    ///     获取MQTT连接信息
    /// </summary>
    /// <param name="clientId"></param>
    /// <returns></returns>
    public async Task<MqttClientStatus?> GetMqttClientStatus(string clientId)
    {
        return (await _mqttService.GetClientsAsync()).FirstOrDefault(w => w.Id == clientId);
    }

    /// <summary>
    ///     获取全部MQTT连接信息
    /// </summary>
    /// <returns></returns>
    public async Task<IList<MqttClientStatus>> GetClientsAsync()
    {
        return await _mqttService.GetClientsAsync();
    }

    /// <summary>
    ///     使用服务端发送RPC消息
    /// </summary>
    /// <param name="method">方法名</param>
    /// <param name="payload">消息内容</param>
    /// <param name="sn">设备序列号</param>
    /// <param name="timeout">超时时间(秒)</param>
    /// <returns></returns>
    public async Task<string> PublishRpc(string method, string payload, string? sn = null, int timeout = 60)
    {
        using var mqttClient = new MqttFactory().CreateMqttClient();
        var mqttClientOptions = new MqttClientOptionsBuilder()
            .WithClientId($"server_rpc_{Guid.NewGuid():N}")
            .WithTcpServer(_mqttConfig.Host, _mqttConfig.Port)
            .WithCredentials(_mqttConfig.Username, _mqttConfig.Password)
            .Build();

        await mqttClient.ConnectAsync(mqttClientOptions);
        if (!mqttClient.IsConnected)
        {
            throw new Exception("RPC客户端连接失败");
        }

        try
        {
            var topicStrategy = new TgMqttRpcClientTopicGenerationStrategy { sn = sn };
            var rpcClient = new MqttRpcClient(mqttClient, new MqttRpcClientOptions
            {
                TopicGenerationStrategy = topicStrategy
            });

            var response = await rpcClient.ExecuteAsync(
                TimeSpan.FromSeconds(timeout),
                method,
                payload,
                MqttQualityOfServiceLevel.AtMostOnce);

            return Encoding.UTF8.GetString(response);
        }
        finally
        {
            await mqttClient.DisconnectAsync();
        }
    }

    /// <summary>
    ///     新建一个连接，发送消息
    /// </summary>
    /// <param name="topic"></param>
    /// <param name="payload"></param>
    /// <returns></returns>
    public async Task<MqttClientPublishResult> Publish(string topic, string payload)
    {
        MqttFactory mqttFactory = new();
        using IMqttClient? mqttClient = mqttFactory.CreateMqttClient();
        MqttClientOptions? mqttClientOptions = new MqttClientOptionsBuilder()
            .WithClientId("admin:" + Guid.NewGuid().ToString("N"))
            .WithTcpServer(_mqttConfig.Host, _mqttConfig.Port)
            .WithCredentials(_mqttConfig.Username, _mqttConfig.Password)
            .Build();

        await mqttClient.ConnectAsync(mqttClientOptions);
        if (!mqttClient.IsConnected)
        {
            throw Oops.Oh("连接失败");
        }

        return await mqttClient.PublishAsync(new MqttApplicationMessage
        {
            PayloadSegment = Encoding.UTF8.GetBytes(payload),
            Retain = false,
            Topic = topic,
            QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce
        });
    }

    /// <summary>
    ///     将数据保存为JSON文件
    /// </summary>
    /// <param name="fileName">文件名</param>
    /// <param name="data">要保存的数据</param>
    /// <returns></returns>
    public async Task SaveDataToJsonFileAsync<T>(string fileName, T data)
    {
        try
        {
            string filePath = Path.Combine(_dataStoragePath, fileName);
            string jsonContent = JsonConvert.SerializeObject(data, Formatting.Indented);
            await File.WriteAllTextAsync(filePath, jsonContent);
            _logger.LogInformation($"数据已成功保存到文件: {filePath}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"保存数据到JSON文件时发生错误: {fileName}");
            throw;
        }
    }

    /// <summary>
    ///     从JSON文件读取数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="fileName">文件名</param>
    /// <returns></returns>
    public async Task<T?> LoadDataFromJsonFileAsync<T>(string fileName)
    {
        try
        {
            string filePath = Path.Combine(_dataStoragePath, fileName);
            if (!File.Exists(filePath))
            {
                return default;
            }

            string jsonContent = await File.ReadAllTextAsync(filePath);
            return JsonConvert.DeserializeObject<T>(jsonContent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"从JSON文件读取数据时发生错误: {fileName}");
            throw;
        }
    }

    #endregion

    #region IHostedService

    /// <summary>
    ///     启动服务
    /// </summary>
    /// <param name="cancellationToken"></param>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // Start a MQTT server.
        await _mqttService.StartAsync();
    }

    /// <summary>
    ///     停止服务
    /// </summary>
    /// <param name="cancellationToken"></param>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        await _mqttService.StopAsync();
    }

    #endregion

    #region MQTT事件

    private Task MqttServer_StartedAsync(EventArgs arg)
    {
        _logger.LogInformation("MQTT服务端已启动");
        return Task.CompletedTask;
    }

    /// <summary>
    ///     消息接收事件
    /// </summary>
    /// <param name="arg"></param>
    private async Task MqttServer_InterceptingPublishAsync(InterceptingPublishEventArgs arg)
    {
        await TaskQueued.EnqueueAsync(async (provider, token) =>
        {
            string? topic = arg.ApplicationMessage.Topic;
            string payload = Encoding.UTF8.GetString(arg.ApplicationMessage.PayloadSegment);

            if (topic.Contains("/up/dev/set/config"))
            {
                // 解析为 dynamic 对象
                dynamic dynamicObj = JsonConvert.DeserializeObject<dynamic>(payload);

                // 同时保存格式化的 JSON 到文件
                JObject jObject = JObject.Parse(payload);


                string gatewaySn = topic.Split("/")[1];

                // 更新内存中的配置
                _gatewayConfigs[gatewaySn] = jObject;

                // 保存所有配置到统一的JSON文件
                await SaveDataToJsonFileAsync(GATEWAY_CONFIG_FILE, _gatewayConfigs);

                // 同时保存单独的配置文件(用于向后兼容)
                await SaveDataToJsonFileAsync($"{gatewaySn}.json", dynamicObj);
            }
            else if (topic.Contains("/up/dev/upload/online"))
            {
                var data = JsonConvert.DeserializeObject<PayLoad>(payload);
                if (data != null) await _socketService.Send(payload, $"{data.DeviceId}_console");
            }
        });
    }

    /// <summary>
    ///     客户端连接校验
    /// </summary>
    /// <param name="arg"></param>
    /// <returns></returns>
    private async Task MqttServer_ValidatingConnectionAsync(ValidatingConnectionEventArgs arg)
    {
        arg.ReasonCode = MqttConnectReasonCode.Success;
        if ((arg.UserName ?? string.Empty) != "fengedge" || (arg.Password ?? string.Empty) != "123456")
        {
            arg.ReasonCode = MqttConnectReasonCode.BadUserNameOrPassword;
            Console.WriteLine($"ValidatingConnectionAsync：客户端Id=【{arg.ClientId}】用户名或密码校验不通过！");
        }
    }

    /// <summary>
    ///     客户端连接的时候触发
    /// </summary>
    /// <param name="arg"></param>
    /// <returns></returns>
    private async Task MqttServer_ClientConnectedAsync(ClientConnectedEventArgs arg)
    {
        _logger.LogInformation($"客户端连接成功 - ID: {arg.ClientId}, 用户名: {arg.UserName}, 地址: {arg.Endpoint}");
        if (arg.ClientId.StartsWith("admin:"))
        {
        }

        // await _db.Insertable(new EdgeGatewayLog
        // {
        //     Id = YitIdHelper.NextId(),
        //     EdgeGatewayId = gateway.Id,
        //     Message = new
        //     {
        //         eventType = "上线"
        //     },
        //     Type = "linkNetLog",
        //     CreatedTime = DateTime.Now
        // }).ExecuteCommandAsync();
    }

    /// <summary>
    ///     客户端断开连接的时候触发
    /// </summary>
    /// <param name="arg"></param>
    /// <returns></returns>
    private async Task MqttServer_ClientDisconnectedAsync(ClientDisconnectedEventArgs arg)
    {
        Console.WriteLine($"客户端Id=【{arg.ClientId}】已断开， 地址=【{arg.Endpoint}】");
        if (arg.ClientId.StartsWith("admin:"))
        {
        }

        // if (_edgeGateways.TryGetValue(arg.ClientId, out var gateway))
        // {
        //     await _db.Insertable(new EdgeGatewayLog
        //     {
        //         Id = YitIdHelper.NextId(),
        //         EdgeGatewayId = gateway.Id,
        //         Message = new
        //         {
        //             eventType = "下线"
        //         },
        //         Type = "linkNetLog",
        //         CreatedTime = DateTime.Now
        //     }).ExecuteCommandAsync();
        // }
    }

    #endregion

    #region 私有方法

    private async Task LoadGatewayConfigs()
    {
        var configs = await LoadDataFromJsonFileAsync<Dictionary<string, dynamic>>(GATEWAY_CONFIG_FILE);
        if (configs != null)
        {
            _gatewayConfigs = configs;
        }
    }

    #endregion
}