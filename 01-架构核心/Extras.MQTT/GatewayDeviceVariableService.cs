using System.ComponentModel;
using System.Dynamic;
using Common.Models;
using Extras.DatabaseAccessor.SqlSugar.Internal;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Extras.MQTT.Dto;
using Extras.MQTT.Models;
using Furion.DatabaseAccessor;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.JsonSerialization;
using IotPlatform.Core.Extension;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using Yitter.IdGenerator;

namespace Extras.MQTT;

/// <summary>
///     子设备属性
/// </summary>
public class GatewayDeviceVariableService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     MQTT服务
    /// </summary>
    private readonly MqttService _mqttServer;

    public GatewayDeviceVariableService(MqttService mqttServer)
    {
        _mqttServer = mqttServer;
    }

    /// <summary>
    ///     设备属性下拉
    /// </summary>
    [HttpGet("/gatewayDeviceVariable/{sn}/select")]
    [DisplayName("网关子设备属性-下拉")]
    public async Task<List<dynamic>> Select(string sn, [FromQuery] long deviceId)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            return new List<dynamic>();
        }

        GatewayDevice? device = config.Device.FirstOrDefault(f => f.Id == deviceId);
        if (device == null)
        {
            throw Oops.Oh("设备已经被删除");
        }

        // 
        var deviceVariables = device.DeviceVariable
            .Select(s => new
            {
                s.Identifier,
                s.Id,
                s.Name,
                s.Description,
                DataType = s.ValueSource == ValueSourceEnum.Get ? s.DeviceVariableEx.DataType
                    : s.TransitionType == TransPondDataTypeEnum.String ? GatewayDataTypeEnum.String
                    : s.TransitionType == TransPondDataTypeEnum.Bool ? GatewayDataTypeEnum.Bool : GatewayDataTypeEnum.Int32,
            })
            .ToList();

        return [deviceVariables];
    }

    /// <summary>
    ///     子设备属性列表
    /// </summary>
    [HttpGet("/gatewayDeviceVariable/{sn}/page")]
    [DisplayName("网关子设备属性-列表")]
    public async Task<SqlSugarPagedList<DeviceVariable>> GatewayDeviceVariablePage(string sn, [FromQuery] GatewayDeviceVariablePageInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            return new SqlSugarPagedList<DeviceVariable>();
        }

        GatewayDevice? device = config.Device.FirstOrDefault(f => f.Id == input.DeviceId);
        if (device == null)
        {
            throw Oops.Oh("设备已经被删除");
        }

        if (!string.IsNullOrEmpty(input.SearchValue))
        {
            device.DeviceVariable = device.DeviceVariable.Where(w =>
                w.Name.Contains(input.SearchValue) ||
                w.Identifier.Contains(input.SearchValue)).ToList();
        }

        int total = device.DeviceVariable.Count();
        List<DeviceVariable> items = device.DeviceVariable
            .Skip((input.PageNo - 1) * input.PageSize)
            .Take(input.PageSize)
            .ToList();

        var totalPages = (int)Math.Ceiling(total / (double)input.PageSize);
        return new SqlSugarPagedList<DeviceVariable>()
        {
            PageSize = input.PageSize,
            PageNo = input.PageNo,
            Rows = items,
            TotalPage = totalPages,
            TotalRows = total,
            HasNextPage = input.PageNo < totalPages,
            HasPrevPage = input.PageNo - 1 > 0
        };
    }

    /// <summary>
    ///     子设备属性详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/gatewayDeviceVariable/{sn}/detail")]
    [DisplayName("网关子设备属性-详情")]
    public async Task<DeviceVariable> GatewayDeviceVariableDetail(string sn, [FromQuery] GatewayDeviceVariableDetailInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("配置文件不存在");

        var device = config.Device.FirstOrDefault(f => f.Id == input.DeviceId);
        if (device == null)
        {
            throw Oops.Oh("设备已经被删除！");
        }
        var variable = device.DeviceVariable.FirstOrDefault(f => f.Id == input.Id);
        if (variable == null)
        {
            throw Oops.Oh("属性已经被删除！");
        }

        return variable;
    }

    /// <summary>
    ///     添加设备属性
    /// </summary>
    [HttpPost("/gatewayDeviceVariable/{sn}/add")]
    [DisplayName("网关子设备属性-新增")]
    public async Task Add(string sn, GatewayDeviceVariableAdd input)
    {
        var config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("配置文件不存在");

        // 查找并更新设备
        int deviceIndex = config.Device.FindIndex(d => d.Id == input.DeviceId);
        if (deviceIndex == -1)
        {
            throw Oops.Oh("设备已经被删除！");
        }

        // 检查标识符是否存在
        if (config.Device[deviceIndex].DeviceVariable.Any(x => x.Identifier == input.Identifier && x.DeviceId == input.DeviceId))
            throw Oops.Oh($"标识符:{input.Identifier},已存在！");

        // 创建新属性
        DeviceVariable newVariable = input.Adapt<DeviceVariable>();
        newVariable.Id = YitIdHelper.NextId();
        newVariable.DeviceId = input.DeviceId;
        newVariable.Name = input.Name;
        newVariable.Identifier = input.Identifier;
        newVariable.Description = input.Description;
        newVariable.DeviceVariableFilter = new DeviceVariableFilter();
        newVariable.DeviceVariableEx = new DeviceVariableEx();

        config.Device[deviceIndex].DeviceVariable.Add(newVariable);

        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     修改设备属性
    /// </summary>
    [HttpPost("/gatewayDeviceVariable/{sn}/update")]
    [DisplayName("网关子设备属性-修改")]
    public async Task Update(string sn, DeviceVariable input)
    {
        var config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("配置文件不存在");

        // 查找并更新设备
        int deviceIndex = config.Device.FindIndex(d => d.Id == input.DeviceId);
        if (deviceIndex == -1)
        {
            throw Oops.Oh("设备不存在！");
        }

        // 查找并更新设备属性
        int variableIndex = config.Device[deviceIndex].DeviceVariable.FindIndex(d => d.Id == input.Id);
        if (variableIndex == -1)
        {
            throw Oops.Oh("属性不存在！");
        }

        // 检查标识符是否与其他属性重复（排除自身）
        if (config.Device[deviceIndex].DeviceVariable.Any(x =>
            x.Identifier == input.Identifier &&
            x.Id != input.Id))
        {
            throw Oops.Oh($"标识符:{input.Identifier},已存在！");
        }

        // 更新属性
        config.Device[deviceIndex].DeviceVariable[variableIndex].Name = input.Name;
        config.Device[deviceIndex].DeviceVariable[variableIndex].Identifier = input.Identifier;
        config.Device[deviceIndex].DeviceVariable[variableIndex].Description = input.Description;
        config.Device[deviceIndex].DeviceVariable[variableIndex].DeviceVariableFilter = input.DeviceVariableFilter ?? new();
        config.Device[deviceIndex].DeviceVariable[variableIndex].DeviceVariableEx = input.DeviceVariableEx ?? new();

        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     删除子设备属性
    /// </summary>
    [HttpPost("/gatewayDeviceVariable/{sn}/delete")]
    [DisplayName("网关子设备属性-删除")]
    public async Task Delete(string sn, BaseId<List<long>> input)
    {
        var config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("配置文件不存在");

        // 遍历所有设备
        foreach (var device in config.Device)
        {
            // 删除匹配的设备变量
            device.DeviceVariable.RemoveAll(x => input.Id.Contains(x.Id));
        }

        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     设备属性批量启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/gatewayDeviceVariable/{sn}/batchEnable")]
    [DisplayName("网关子设备属性-批量启用/禁用")]
    public async Task<bool> BatchEnable(string sn, GatewayDeviceVariableBatchEnableInput input)
    {
        var config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("配置文件不存在");

        // 遍历所有设备查找目标变量
        foreach (var device in config.Device.Where(w => w.Id == input.DeviceId))
        {
            foreach (var variable in device.DeviceVariable.Where(w => input.IdList.Contains(w.Id)))
            {
                variable.Enable = input.Enable;
            }
        }

        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
        return true;
    }

    /// <summary>
    ///     导入设备属性
    /// </summary>
    [HttpPost("/gatewayDeviceVariable/{sn}/inPort")]
    [DisplayName("网关子设备属性-导入")]
    public async Task<DeviceVariableOutput> InPort(string sn, [FromForm] DeviceVariableInPortInput input)
    {
        var config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("配置文件不存在");

        // 查找目标设备
        var device = config.Device.FirstOrDefault(f => f.Id == input.DeviceId);
        if (device == null)
        {
            throw Oops.Oh("设备不存在");
        }

        var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);

        // 返回结果
        var result = new DeviceVariableOutput { ErrorColumn = new List<ErrorColumn>() };
        // 需要新增的属性
        var deviceVariableList = new List<DeviceVariable>();
        // 当前所在行
        var line = 2;

        // 解析Excel数据
        var excelData = await stream.QueryAsync<DeviceVariableInPortDto>(startCell: "A2");
        foreach (var deviceVariableExPort in excelData)
        {
            try
            {
                var deviceVariable = deviceVariableExPort.Adapt<DeviceVariable>();
                deviceVariable.Id = YitIdHelper.NextId();
                deviceVariable.DeviceId = input.DeviceId;
                deviceVariable.Custom = string.IsNullOrEmpty(deviceVariableExPort.Custom) || deviceVariableExPort.Custom == "\"\""
                    ? null
                    : deviceVariableExPort.Custom;
                deviceVariable.Length = (ushort)(!string.IsNullOrEmpty(deviceVariableExPort.LengthEx)
                    ? Convert.ToUInt32(deviceVariableExPort.LengthEx)
                    : 0);
                deviceVariable.Period = (ushort)(!string.IsNullOrEmpty(deviceVariableExPort.PeriodEx)
                    ? Convert.ToUInt32(deviceVariableExPort.PeriodEx)
                    : 0);
                deviceVariable.ArchiveTime = (ushort)(!string.IsNullOrEmpty(deviceVariableExPort.ArchiveTimeEx)
                    ? Convert.ToUInt32(deviceVariableExPort.ArchiveTimeEx)
                    : 0);
                deviceVariable.Tags = !string.IsNullOrEmpty(deviceVariableExPort.Tag)
                    ? JSON.Deserialize<List<string>>(deviceVariableExPort.Tag)
                    : new List<string>();
                deviceVariable.DeviceVariableFilter = JSON.Deserialize<DeviceVariableFilter>(deviceVariableExPort.DeviceVariableFilterExtend);
                deviceVariable.DeviceVariableEx = JSON.Deserialize<DeviceVariableEx>(deviceVariableExPort.VariableExtend);
                deviceVariableList.Add(deviceVariable);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 Error:【{e.Message}】 " });
                line++;
            }
        }

        // 根据导入类型处理数据
        switch (input.InPortType)
        {
            case GatewayDeviceVariableInPortTypeEnum.OverLook:
                // 忽略模式：移除已存在标识符的记录
                deviceVariableList.RemoveAll(x =>
                    device.DeviceVariable.Any(existing => existing.Identifier == x.Identifier));
                break;

            case GatewayDeviceVariableInPortTypeEnum.Coverage:
                // 覆盖模式：更新已存在的记录，保留新记录
                foreach (var newVariable in deviceVariableList.ToList())
                {
                    var existingVariable = device.DeviceVariable
                        .FirstOrDefault(x => x.Identifier == newVariable.Identifier);

                    if (existingVariable != null)
                    {
                        // 保留原有ID，更新其他属性
                        newVariable.Id = existingVariable.Id;
                        // 从设备中移除旧记录
                        device.DeviceVariable.Remove(existingVariable);
                    }
                }
                break;
        }

        // 添加新记录到设备
        device.DeviceVariable.AddRange(deviceVariableList);

        // 保存更改
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);

        // 计算成功数量
        result.SuccessCount = deviceVariableList.Count;

        return result;
    }
    /// <summary>
    ///     导出设备属性
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/gatewayDeviceVariable/{sn}/exPort")]
    [DisplayName("网关子设备属性-导出")]
    public async Task<IActionResult> ExPort(string sn, DeviceVariableExPortInput input)
    {
        var config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("配置文件不存在");


        var device = config.Device.FirstOrDefault(f => f.Id == input.Id);
        if (input.DeviceVariableIds.Any())
        {
            device.DeviceVariable = device.DeviceVariable.Where(w => input.DeviceVariableIds.Contains(w.Id)).ToList();
        }
        if (device == null)
            throw Oops.Oh("设备不存在！");

        var value = new
        {
            deviceVariable = device.DeviceVariable.Select(s => new
            {
                s.Name,
                s.Identifier,
                TransitionType = s.TransitionType.GetDescription(),
                ValueSource = s.ValueSource.GetDescription(),
                SendType = s.SendType.GetDescription(),
                DeviceVariableEx = JSON.Serialize(s.DeviceVariableEx),
                DeviceVariableFilter = JSON.Serialize(s.DeviceVariableFilter),
                s.DefaultValue,
                Period = s.Period == 0 ? "" : s.Period.ToString(),
                Tag = s.Tags.Any() ? JSON.Serialize(s.Tags) : "",
                Length = s.Length == 0 ? "" : s.Length.ToString(),
                s.Unit,
                s.Expressions,
                s.Script,
                ArchiveTime = s.ArchiveTime == 0 ? "" : s.ArchiveTime.ToString(),
                Custom = s.Custom ?? "",
                s.Persistence,
                s.Description,
                s.Enable
            })
        };

        try
        {
            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsByTemplateAsync("Templates/DeviceVariableImport.xlsx", value);
            memoryStream.Seek(0, SeekOrigin.Begin);
            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{System.DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"导出失败：{ex.Message}");
        }
    }

    /// <summary>
    ///     导出设备属性-简单模板
    /// </summary>
    /// <returns></returns>
    [HttpPost("/gatewayDeviceVariable/exPortTemplate")]
    [DisplayName("网关子设备属性-简单模板")]
    public async Task<IActionResult> ExPortTemplate()
    {
        var value = new
        {
            managers = new[]
            {
                new {Ident = "product", Name = "产量", Addr = "D100", DataType = "int32"}
            }
        };

        try
        {
            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsByTemplateAsync("Templates/DeviceVariableSimpleImport.xlsx", value);
            // await memoryStream.SaveAsAsync(values );
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{System.DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception ex)
        {
            throw Oops.Bah($"导出失败：{ex.Message}");
        }
    }

    /// <summary>
    ///     导入设备属性-简单模板
    /// </summary>
    [HttpPost("/gatewayDeviceVariable/{sn}/inPortTemplate")]
    [DisplayName("网关子设备属性-导入简单模板")]
    public async Task<DeviceVariableOutput> InPortTemplate(string sn, [FromForm] DeviceVariableInPortInput input)
    {
        var config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("配置文件不存在");

        // 查找目标设备
        var device = config.Device.FirstOrDefault(f => f.Id == input.DeviceId);
        if (device == null)
            throw Oops.Oh("设备不存在");

        var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);

        //返回结果
        var result = new DeviceVariableOutput { ErrorColumn = new List<ErrorColumn>() };
        //需要新增的属性
        var deviceVariableList = new List<DeviceVariable>();
        //当前所在行
        var line = 2;

        foreach (var deviceVariableExPort in await stream.QueryAsync<DeviceVariableExPortTemplateInput>(startCell: "A2"))
        {
            try
            {
                //检查必填项
                var success = CheckInPortDataTem(deviceVariableExPort, line, result);
                if (!success)
                    continue;

                var deviceVariable = deviceVariableExPort.Adapt<DeviceVariable>();
                deviceVariable.Id = YitIdHelper.NextId();
                deviceVariable.DeviceId = device.Id;
                deviceVariable.Tags = new List<string>();
                deviceVariable.DeviceVariableFilter = new DeviceVariableFilter();
                deviceVariable.DeviceVariableEx = new DeviceVariableEx
                {
                    DataType = deviceVariableExPort.DataType.Adapt<GatewayDataTypeEnum>(),
                    RegisterAddress = deviceVariableExPort.RegisterAddress,
                    Encoding = StringEnum.Utf8,
                    Method = "Debug",
                    ProtectType = ProtectTypeEnum.ReadOnly
                };

                if (deviceVariable.DeviceVariableEx.DataType == GatewayDataTypeEnum.String)
                    deviceVariable.Length = 10;

                switch (deviceVariable.DeviceVariableEx.DataType)
                {
                    case GatewayDataTypeEnum.Bool:
                        deviceVariable.TransitionType = TransPondDataTypeEnum.Bool;
                        break;
                    case GatewayDataTypeEnum.Uint16:
                    case GatewayDataTypeEnum.Int16:
                    case GatewayDataTypeEnum.Uint32:
                    case GatewayDataTypeEnum.Int32:
                    case GatewayDataTypeEnum.Uint64:
                    case GatewayDataTypeEnum.Int64:
                    case GatewayDataTypeEnum.Bit:
                    case GatewayDataTypeEnum.Bcd:
                        deviceVariable.TransitionType = TransPondDataTypeEnum.Int;
                        break;
                    case GatewayDataTypeEnum.Float:
                    case GatewayDataTypeEnum.Double:
                        deviceVariable.TransitionType = TransPondDataTypeEnum.Double;
                        break;
                    case GatewayDataTypeEnum.String:
                    default:
                        deviceVariable.TransitionType = TransPondDataTypeEnum.String;
                        break;
                }

                deviceVariable.ValueSource = ValueSourceEnum.Get;
                deviceVariable.SendType = SendTypeEnum.Always;
                deviceVariable.Enable = true;
                deviceVariableList.Add(deviceVariable);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 Error:【{e.Message}】 " });
                line++;
            }
        }

        // 根据导入类型处理数据
        foreach (var newVariable in deviceVariableList.ToList())
        {
            var existingVariable = device.DeviceVariable
                .FirstOrDefault(x => x.Identifier == newVariable.Identifier);

            if (existingVariable != null)
            {
                if (input.InPortType == GatewayDeviceVariableInPortTypeEnum.OverLook)
                {
                    // 忽略模式：移除重复的记录
                    deviceVariableList.Remove(newVariable);
                }
                else
                {
                    // 覆盖模式：更新已存在的记录
                    deviceVariableList.Remove(newVariable);
                    newVariable.Id = existingVariable.Id;
                    device.DeviceVariable.Remove(existingVariable);
                    deviceVariableList.Add(newVariable);
                }
            }
        }

        // 添加新记录到设备
        device.DeviceVariable.AddRange(deviceVariableList);

        // 保存更改
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);

        // 计算成功数量
        result.SuccessCount = deviceVariableList.Count;

        return result;
    }

    /// <summary>
    ///     检查属性导入必填项
    /// </summary>
    private bool CheckInPortData(DeviceVariableInPortDto deviceVariableExPort, int line, DeviceVariableOutput result)
    {
        if (string.IsNullOrEmpty(deviceVariableExPort.Identifier))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【Identifier】 不能是空！ ", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(deviceVariableExPort.Name))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【Name】 不能是空！ ", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(deviceVariableExPort.TransitionType))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【DataType】 不能是空！ ", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(deviceVariableExPort.ValueSource))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【ValueSource】 不能是空！", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(deviceVariableExPort.SendType))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【SendType】 不能是空！ ", Line = line });
            return false;
        }

        return true;
    }

    /// <summary>
    ///     检查属性导入必填项-简单模板
    /// </summary>
    private bool CheckInPortDataTem(DeviceVariableExPortTemplateInput deviceVariableExPort, int line, DeviceVariableOutput result)
    {
        if (string.IsNullOrEmpty(deviceVariableExPort.Identifier))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【Identifier】 不能是空！ ", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(deviceVariableExPort.Name))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【Name】 不能是空！ ", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(deviceVariableExPort.DataType))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【DataType】 不能是空！ ", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(deviceVariableExPort.RegisterAddress))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【RegisterAddress】 不能是空！", Line = line });
            return false;
        }

        return true;
    }

    /// <summary>
    ///     表达式函数
    /// </summary>
    /// <returns></returns>
    [HttpGet("/gatewayDeviceVariable/function/meta")]
    public async Task<dynamic> FunctionMeta()
    {
        var functionInfos = new[]
        {
            new
            {
                DisplayName = "CEILING()",
                DisplayValue = "Math.Ceiling()",
                FunctionMetaDesc = new
                {
                    Desc = "CEILING()：返回大于或等于指定数字的最小整数值。",
                    Usage = "CEILING(数字)",
                    Example = "CEILING(3.1415926) 返回4"
                }
            },
            new
            {
                DisplayName = "FLOOR()",
                DisplayValue = "Math.Floor()",
                FunctionMetaDesc = new
                {
                    Desc = "FLOOR()：返回小于或等于指定数字的最大整数值。",
                    Usage = "FLOOR(数字)",
                    Example = "FLOOR(3.1415926)返回3"
                }
            },
            new
            {
                DisplayName = "ROUND()",
                DisplayValue = "Math.Round()",
                FunctionMetaDesc = new
                {
                    Desc = "ROUND()：通过使用舍入到最接近的约定，将数字舍入到指定的小数位数。",
                    Usage = "ROUND(数字,保留长度)",
                    Example = "ROUND(3.1415926, 3)返回3.142，因为3.1415926四舍五入保留3位小数是3.142。"
                }
            }
        };
        return functionInfos;
    }


}