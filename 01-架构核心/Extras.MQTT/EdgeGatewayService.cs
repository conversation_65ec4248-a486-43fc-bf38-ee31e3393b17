using System.ComponentModel;
using Extras.MQTT.Dto;
using Extras.MQTT.Models;
using Furion.DynamicApiController;
using Furion.Shapeless;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Extras.MQTT;

/// <summary>
///     网关
/// </summary>
public class EdgeGatewayService : IDynamicApiController
{
    private readonly MqttService _mqttServer;
    private const string GATEWAY_CONFIG_FILE = "gateway_configs.json";

    public EdgeGatewayService(MqttService serverHosted)
    {
        _mqttServer = serverHosted;
    }

    #region 网关脚本

    /// <summary>
    ///     网关脚本列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeGateway/{sn}/script/list")]
    [DisplayName("网关脚本列表")]
    public async Task<List<Script>> EdgeGatewayScriptList(string sn, [FromQuery] EdgeGatewayScriptListInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            return new List<Script>();
        }

        Gateway gateway = config.Gateway;
        if (gateway == null)
        {
            return new List<Script>();
        }

        if (gateway.Script == null)
        {
            return new List<Script>();
        }

        if (input.Method != 0)
        {
            return gateway.Script
                .Where(w => w.Method == input.Method)
                .ToList();
        }

        return gateway.Script;
    }

    #endregion

    #region 网关-网络配置

    /// <summary>
    ///     网关网卡下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeGateway/{sn}/network/select")]
    [DisplayName("网关网卡下拉")]
    public async Task<NetworkConfig> EdgeGatewayNetworkSelect(string sn)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        return config == null ? null : JsonConvert.DeserializeObject<NetworkConfig>(config.NetWork);
    }

    /// <summary>
    ///     网关网络配置
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeGateway/{sn}/network/list")]
    [DisplayName("网关网络配置")]
    public async Task<dynamic> EdgeGatewayNetworkList(string sn)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        return config == null ? null : JsonConvert.DeserializeObject<NetworkConfig>(config.NetWork);
    }

    /// <summary>
    ///     网关网络配置保存
    /// </summary>
    /// <returns></returns>
    [HttpPost("/edgeGateway/{sn}/network/save")]
    [DisplayName("网关网络配置保存")]
    public async Task EdgeGatewayNetworkSave(string sn, NetworkConfig input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            // 如果配置不存在，创建新的配置对象
            config = new GatewayConfig
            {
                NetWork = JsonConvert.SerializeObject(input)
            };
        }
        else
        {
            // 将network对象序列化成字符串后赋值
            config.NetWork = JsonConvert.SerializeObject(input);
        }

        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    #endregion

    // #region 网关日志
    //
    // /// <summary>
    // ///     网关诊断-日志
    // /// </summary>
    // /// <returns></returns>
    // [HttpGet("/edgeGateway/log/page")]
    // [DisplayName("网关诊断日志")]
    // public async Task<dynamic> EdgeGatewayLogPage([FromQuery] EdgeGatewayLogPageInput input)
    // {
    //     var edgeGatewayLogs = await _edgeGateway.AsSugarClient()
    //         .Queryable<EdgeGatewayLog>()
    //         .Where(w => w.EdgeGatewayId == input.EdgeGatewayId)
    //         .Where(w => w.Type == input.Type)
    //         .ToPagedListAsync(input.PageNo, input.PageSize);
    //     return edgeGatewayLogs;
    // }
    //
    // #endregion

    #region 网关配置同步

    /// <summary>
    ///     同步网关配置
    /// </summary>
    /// <param name="sn"></param>
    /// <returns></returns>
    [HttpPost("/edgeGateway/{sn}/synchronization")]
    [DisplayName("同步网关配置")]
    public async Task EdgeGatewaySynchronization(string sn)
    {
        bool result = Convert.ToBoolean(await _mqttServer.PublishRpc("getConfig", "", sn));
        if (!result)
        {
            throw Oops.Oh("同步配置失败！");
        }
    }

    /// <summary>
    ///     同步下发网关配置
    /// </summary>
    /// <param name="sn"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeGateway/{sn}/down/synchronization")]
    [DisplayName("同步下发网关配置")]
    public async Task EdgeGatewayDownSynchronization(string sn, EdgeGatewayDownSynchronizationInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        dynamic? clay = new Clay();
        clay.editCon = input.EditCon;

        // 设备配置
        if (input.EditCon.Contains("deviceList"))
        {
            clay.deviceListConfigStr = JsonConvert.SerializeObject(config.Device);
        }

        // 网络配置
        if (input.EditCon.Contains("netWorkConfig"))
        {
            clay.netWorkConfigStr = config.NetWork;
        }

        // 脚本配置
        if (input.EditCon.Contains("scriptConfig"))
        {
            clay.scriptConfigStr = JsonConvert.SerializeObject(config.Script);
        }

        // 脚本执行策略配置
        if (input.EditCon.Contains("scriptStrategyConfig"))
        {
            clay.scriptStrategyConfigStr = JsonConvert.SerializeObject(config.ScriptStrategy);
        }

        // 转发配置
        if (input.EditCon.Contains("transPondConfig"))
        {
            clay.transPondConfigStr = JsonConvert.SerializeObject(config.TransPond);
        }

        dynamic? result = Convert.ToBoolean(await _mqttServer.PublishRpc("setConfig", clay.ToString(), sn));
        if (!result)
        {
            throw Oops.Oh("下发配置失败！");
        }
    }

    /// <summary>
    ///     网关重启
    /// </summary>
    /// <param name="sn"></param>
    /// <returns></returns>
    [HttpPost("/edgeGateway/{sn}/reboot")]
    [DisplayName("网关重启")]
    public async Task EdgeGatewayReboot(string sn)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        bool result = Convert.ToBoolean(await _mqttServer.PublishRpc("reboot", "", sn));
        if (!result)
        {
            throw Oops.Oh("指令下发失败！");
        }
    }

    /// <summary>
    ///     网关ping
    /// </summary>
    /// <param name="sn"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeGateway/{sn}/ping")]
    [DisplayName("网关ping")]
    public async Task<dynamic> EdgeGatewayPing(string sn, EdgeGatewayPingInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        if (input.NetWorkName?.Trim() == "不指定")
        {
            input.NetWorkName = "";
        }

        string result = await _mqttServer.PublishRpc("ping", JsonConvert.SerializeObject(new
        {
            networkName = input.NetWorkName,
            ip = input.Ip
        }), sn);

        return string.IsNullOrEmpty(input.NetWorkName) ? result : JsonConvert.DeserializeObject<List<string>>(result);
    }

    /// <summary>
    ///     属性实时值
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeGateway/{sn}/lastValue")]
    public async Task GetLastOnline(string sn, [FromQuery] EdgeGetLastOnlineInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        GatewayDevice? devices = config.Device.FirstOrDefault(d => d.Id == input.DeviceId);
        if (devices == null)
        {
            throw Oops.Oh("设备已经被删除");
        }

        await _mqttServer.Publish($"online/{sn}", devices.DeviceName);
    }

    #endregion
}