using System.ComponentModel;
using Common.Models;
using Extras.MQTT.Dto;
using Extras.MQTT.Models;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Microsoft.AspNetCore.Mvc;

namespace Extras.MQTT;

/// <summary>
///     网关支持协议列表
/// </summary>
public class GatewayDriverService : IDynamicApiController, ITransient
{
    private readonly MqttService _mqttServer;

    public GatewayDriverService(MqttService mqttServer)
    {
        _mqttServer = mqttServer;
    }

    /// <summary>
    ///     网关支持协议集合
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeGateway/{sn}/driver/list")]
    [DisplayName("网关协议-集合")]
    public async Task<dynamic> GatewayDriverList(string sn, [FromQuery] int driverType)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }

        if (config.Driver == null)
        {
            return new List<Driver>();
        }

        List<Driver> drivers = config.Driver.Where(w => w.DriverType == driverType).ToList();
        return drivers.Select(s => new
        {
            s.Id,
            s.DriverType,
            Name = s.DriverName
        })
            .ToList();
    }

    /// <summary>
    ///     网关支持协议详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeGateway/{sn}/driver/detail")]
    [DisplayName("网关协议-详情")]
    public async Task<Driver> GatewayDriverDetail(string sn, [FromQuery] BaseId input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }

        if (config.Driver == null)
        {
            throw Oops.Oh("暂不支持该协议!");
        }

        Driver? driver = config.Driver.FirstOrDefault(w => w.Id == input.Id);
        return driver;
    }

    /// <summary>
    ///     网关支持协议-读取方法列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeGateway/{sn}/driver/methods")]
    [DisplayName("网关协议-方法")]
    public async Task<dynamic> GatewayDriverMethods(string sn, [FromQuery] GatewayDriverMethodsInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }

        // 网关子设备
        GatewayDevice? device = config.Device.FirstOrDefault(w => w.Id == input.DeviceId);
        if (device == null)
        {
            throw Oops.Oh("网关子设备已经删除！");
        }

        Driver? driver = config.Driver.FirstOrDefault(w => w.Id == input.DriverId);
        if (driver == null)
        {
            throw Oops.Oh("暂不支持该协议");
        }

        if (driver.Methods == null)
        {
            return new List<dynamic>();
        }

        List<dynamic> output = new();
        // 过滤协议
        foreach (Method method in driver.Methods)
        {
            // 过滤条件
            if (method.Filter)
            {
                // 设备配置
                DeviceConfig? deviceConfig = device.DeviceConfigs.FirstOrDefault(f => f.DeviceConfigName == method.FilterField);
                if (deviceConfig == null)
                {
                    continue;
                }

                if (method.FilterValue == null || !method.FilterValue.Contains(deviceConfig.Value))
                {
                    continue;
                }
            }

            output.Add(new
            {
                method.Text,
                method.Value,
                method.Desc
            });
        }

        return output;
    }

    /// <summary>
    ///     网关支持协议-驱动配置
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeGateway/{sn}/driver/func")]
    [DisplayName("网关协议-驱动配置")]
    public async Task<dynamic> GatewayDriverFunc(string sn,[FromQuery] GatewayDriverMethodsInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }

        // 网关子设备
        var device = config.Device.FirstOrDefault(f => f.Id == input.DeviceId);
        if (device == null)
        {
            throw Oops.Oh("网关子设备已经删除！");
        }

        // 网关支持协议
        Driver? driver = config.Driver.FirstOrDefault(w => w.Id == input.DriverId);
        if (driver == null)
        {
            throw Oops.Oh("暂不支持该协议");
        }

        return driver.Funcs;
    }
}