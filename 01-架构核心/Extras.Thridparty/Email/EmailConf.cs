using System.ComponentModel.DataAnnotations;

namespace Extras.Thridparty.Email;

/// <summary>
///     消息配置 邮件发送配置
/// </summary>
public class EmailConf
{
    /// <summary>
    ///     发送人邮箱
    /// </summary>
    [Required]
    public string SenderEmail { get; set; }

    /// <summary>
    ///     发送人账号
    /// </summary>
    [Required]
    public string SenderNumber { get; set; }

    /// <summary>
    ///     发送人密码
    /// </summary>
    [Required]
    public string SenderPassword { get; set; }

    /// <summary>
    ///     消息发送服务器
    /// </summary>
    public string Host { get; set; }

    /// <summary>
    ///     发送消息服务器端口
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    ///     SSL加密
    /// </summary>
    public bool Ssl { get; set; }
}