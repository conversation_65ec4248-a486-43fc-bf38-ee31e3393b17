using System.ComponentModel;
using SqlSugar;

namespace Extras.DatabaseAccessor.SqlSugar.Internal;

/// <summary>
///     分页泛型集合
/// </summary>
/// <typeparam name="T"></typeparam>
public class SqlSugarPagedList<T> where T : new()
{
    /// <summary>
    ///     页码
    /// </summary>
    [Description("页码")]
    public int PageNo { get; set; } = 1;

    /// <summary>
    ///     页容量
    /// </summary>
    [Description("页容量")]
    public int PageSize { get; set; } = 20;

    /// <summary>
    ///     总条数
    /// </summary>
    [Description("总条数")]
    public int TotalRows { get; set; }

    /// <summary>
    ///     总页数
    /// </summary>
    [Description("总页数")]
    public int TotalPage { get; set; }

    /// <summary>
    ///     当前页集合
    /// </summary>
    [Description("当前页集合")]
    public IEnumerable<T> Rows { get; set; } = new List<T>();

    /// <summary>
    ///     是否有上一页
    /// </summary>
    [Description("是否有上一页")]
    public bool HasPrevPage { get; set; }

    /// <summary>
    ///     是否有下一页
    /// </summary>
    [Description("是否有下一页")]
    public bool HasNextPage { get; set; }
}

/// <summary>
///     分页拓展类
/// </summary>
public static class SqlSugarPagedExtensions
{
    /// <summary>
    ///     分页拓展
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="pageIndex"></param>
    /// <param name="pageSize"></param>
    /// <returns></returns>
    public static SqlSugarPagedList<TEntity> ToPagedList<TEntity>(this ISugarQueryable<TEntity> entity, int pageIndex, int pageSize)
        where TEntity : new()
    {
        var total = 0;
        var items = entity.ToPageList(pageIndex, pageSize, ref total);
        var totalPages = (int) Math.Ceiling(total / (double) pageSize);
        return new SqlSugarPagedList<TEntity>
        {
            PageNo = pageIndex,
            PageSize = pageSize,
            Rows = items,
            TotalRows = total,
            TotalPage = totalPages,
            HasNextPage = pageIndex < totalPages,
            HasPrevPage = pageIndex - 1 > 0
        };
    }

    /// <summary>
    ///     分页拓展
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="pageIndex"></param>
    /// <param name="pageSize"></param>
    /// <returns></returns>
    public static async Task<SqlSugarPagedList<TEntity>> ToPagedListAsync<TEntity>(this ISugarQueryable<TEntity> entity, int pageIndex, int pageSize)
        where TEntity : new()
    {
        RefAsync<int> total = 0;
        var items = await entity.ToPageListAsync(pageIndex, pageSize, total);
        var totalPages = (int) Math.Ceiling(total / (double) pageSize);
        return new SqlSugarPagedList<TEntity>
        {
            PageNo = pageIndex,
            PageSize = pageSize,
            Rows = items,
            TotalRows = total,
            TotalPage = totalPages,
            HasNextPage = pageIndex < totalPages,
            HasPrevPage = pageIndex - 1 > 0
        };
    }
}