using Furion.ConfigurableOptions;
using IotPlatform.Core.Const;
using Microsoft.Extensions.Configuration;
using SqlSugar;

namespace Extras.DatabaseAccessor.SqlSugar.Options;

/// <summary>
/// 数据库配置.
/// </summary>
public sealed class ConnectionStringsOptions : IConfigurableOptions<ConnectionStringsOptions>
{
    /// <summary>
    ///     启用控制台打印SQL
    /// </summary>
    public bool EnableConsoleSql { get; set; }
    
    /// <summary>
    /// 数据库集合
    /// </summary>
    public List<DbConnectionConfig> ConnectionConfigs { get; set; }

    /// <summary>
    /// 默认数据库
    /// </summary>
    public DbConnectionConfig DefaultConnectionConfig { get; set; }

    public void PostConfigure(ConnectionStringsOptions options, IConfiguration configuration)
    {
        foreach (var dbConfig in options.ConnectionConfigs)
        {
            if (string.IsNullOrWhiteSpace(dbConfig.ConfigId.ToString()))
                dbConfig.ConfigId = SqlSugarConst.MainConfigId;
        }

        DefaultConnectionConfig = ConnectionConfigs.FirstOrDefault(x => x.ConfigId.ToString() == SqlSugarConst.MainConfigId);
    }
}

/// <summary>
/// 数据库连接配置.
/// </summary>
public sealed class DbConnectionConfig : ConnectionConfig
{
    /// <summary>
    /// 
    /// </summary>
    public bool EnableInitTable { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool EnableInitSeed { get; set; }
    
    /// <summary>
    /// 数据库名称.
    /// </summary>
    public string DBName { get; set; }

    /// <summary>
    /// 数据库地址.
    /// </summary>
    public string Host { get; set; }

    /// <summary>
    /// 数据库端口号.
    /// </summary>
    public int Port { get; set; }

    /// <summary>
    /// 账号.
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// 密码.
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    /// 模式.
    /// </summary>
    public string DBSchema { get; set; }

}