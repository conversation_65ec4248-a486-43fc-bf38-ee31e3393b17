using System.Reflection;
using System.Diagnostics;
using Feng.IotGateway.Core.Const;
using Feng.IotGateway.Core.Util;
using Furion.DataEncryption;
using HslCommunication;
using System.Net.NetworkInformation;
using Furion.FriendlyException;
using IotGateway.Core.StartupOptimization;
using Microsoft.Extensions.Logging;

Console.WriteLine("┌──────────────────────────────────────────────────┐");
Console.WriteLine($"│            正在设置系统版本...                    │");

var version = Assembly.GetExecutingAssembly().GetName().Version;
MachineUtil.Version = version?.ToString() ?? "1.0.0";

Console.WriteLine($"│            系统版本设置完成: {MachineUtil.Version}     │");
Console.WriteLine("└──────────────────────────────────────────────────┘");

var totalStopwatch = Stopwatch.StartNew();

// 初始化性能监控
var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger<StartupPerformanceMonitor>();
StartupPerformanceExtensions.InitializePerformanceMonitoring(logger);

using (StartupPerformanceExtensions.MonitorStep("应用程序启动"))
{
    Serve.Run(RunOptions.Default.ConfigureBuilder(builder =>
    {
        using (StartupPerformanceExtensions.MonitorStep("基础配置"))
        {
            Authorization.SetAuthorizationCode(DESEncryption.Decrypt(CommonConst.Authorization, "Feng"));
            builder.WebHost.UseUrls("http://*:8093");
            builder.Logging.AddConsole();
            builder.Services.AddLogging(logging =>
            {
                logging.AddConsole();
                logging.AddDebug();
            });

            // 添加启动优化服务
            builder.Services.AddStartupOptimization();
        }
    }));
}

totalStopwatch.Stop();
Console.WriteLine($"应用程序总启动耗时: {totalStopwatch.ElapsedMilliseconds}ms");

// 输出详细性能报告
StartupPerformanceExtensions.PrintReport();

public partial class Program
{
}