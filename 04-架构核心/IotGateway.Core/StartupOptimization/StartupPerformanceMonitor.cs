using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq;

namespace IotGateway.Core.StartupOptimization;

/// <summary>
/// 启动性能监控器
/// </summary>
public class StartupPerformanceMonitor
{
    private readonly ILogger<StartupPerformanceMonitor> _logger;
    private readonly ConcurrentDictionary<string, StartupMetric> _metrics = new();
    private readonly Stopwatch _totalStopwatch = Stopwatch.StartNew();

    public StartupPerformanceMonitor(ILogger<StartupPerformanceMonitor> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 开始监控某个启动步骤
    /// </summary>
    public IDisposable StartStep(string stepName)
    {
        var metric = new StartupMetric
        {
            StepName = stepName,
            StartTime = DateTime.Now,
            Stopwatch = Stopwatch.StartNew()
        };

        _metrics[stepName] = metric;
        _logger.LogInformation($"开始执行: {stepName}");

        return new StepDisposable(this, stepName);
    }

    /// <summary>
    /// 结束监控某个启动步骤
    /// </summary>
    public void EndStep(string stepName)
    {
        if (_metrics.TryGetValue(stepName, out var metric))
        {
            metric.Stopwatch.Stop();
            metric.EndTime = DateTime.Now;
            metric.Duration = metric.Stopwatch.Elapsed;

            _logger.LogInformation($"完成执行: {stepName}, 耗时: {metric.Duration.TotalMilliseconds:F0}ms");
        }
    }

    /// <summary>
    /// 输出启动性能报告
    /// </summary>
    public void PrintPerformanceReport()
    {
        _totalStopwatch.Stop();
        
        Console.WriteLine();
        Console.WriteLine("┌────────────────────────────────────────────────────────────────┐");
        Console.WriteLine("│                        启动性能报告                            │");
        Console.WriteLine("├────────────────────────────────────────────────────────────────┤");
        Console.WriteLine($"│ 总启动时间: {_totalStopwatch.Elapsed.TotalSeconds:F2} 秒                                    │");
        Console.WriteLine("├────────────────────────────────────────────────────────────────┤");

        var sortedMetrics = _metrics.Values
            .Where(m => m.Duration.HasValue)
            .OrderByDescending(m => m.Duration.Value.TotalMilliseconds)
            .ToList();

        foreach (var metric in sortedMetrics)
        {
            var percentage = (metric.Duration?.TotalMilliseconds ?? 0) / _totalStopwatch.Elapsed.TotalMilliseconds * 100;
            var stepName = metric.StepName.Length > 30 ? metric.StepName.Substring(0, 27) + "..." : metric.StepName;
            Console.WriteLine($"│ {stepName,-30} {metric.Duration?.TotalMilliseconds ?? 0,6:F0}ms ({percentage,4:F1}%) │");
        }

        Console.WriteLine("└────────────────────────────────────────────────────────────────┘");
        Console.WriteLine();

        // 输出优化建议
        PrintOptimizationSuggestions(sortedMetrics);
    }

    /// <summary>
    /// 输出优化建议
    /// </summary>
    private void PrintOptimizationSuggestions(List<StartupMetric> sortedMetrics)
    {
        Console.WriteLine("┌────────────────────────────────────────────────────────────────┐");
        Console.WriteLine("│                        优化建议                                │");
        Console.WriteLine("├────────────────────────────────────────────────────────────────┤");

        var slowSteps = sortedMetrics.Where(m => m.Duration.Value.TotalMilliseconds > 1000).ToList();
        
        if (slowSteps.Any())
        {
            Console.WriteLine("│ 以下步骤耗时较长，建议优化:                                    │");
            foreach (var step in slowSteps.Take(3))
            {
                Console.WriteLine($"│ • {step.StepName} ({step.Duration.Value.TotalMilliseconds:F0}ms)");
                
                // 根据步骤名称提供具体建议
                if (step.StepName.Contains("数据库"))
                {
                    Console.WriteLine("│   建议: 使用数据库连接池、异步初始化、跳过已存在的表");
                }
                else if (step.StepName.Contains("驱动"))
                {
                    Console.WriteLine("│   建议: 并行加载驱动、延迟加载非关键驱动");
                }
                else if (step.StepName.Contains("服务"))
                {
                    Console.WriteLine("│   建议: 使用懒加载、异步初始化");
                }
            }
        }
        else
        {
            Console.WriteLine("│ 启动性能良好，无需特别优化                                      │");
        }

        Console.WriteLine("└────────────────────────────────────────────────────────────────┘");
    }

    private class StepDisposable : IDisposable
    {
        private readonly StartupPerformanceMonitor _monitor;
        private readonly string _stepName;

        public StepDisposable(StartupPerformanceMonitor monitor, string stepName)
        {
            _monitor = monitor;
            _stepName = stepName;
        }

        public void Dispose()
        {
            _monitor.EndStep(_stepName);
        }
    }
}

/// <summary>
/// 启动指标
/// </summary>
public class StartupMetric
{
    public string StepName { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration { get; set; }
    public Stopwatch Stopwatch { get; set; }
}

/// <summary>
/// 启动性能监控扩展
/// </summary>
public static class StartupPerformanceExtensions
{
    private static StartupPerformanceMonitor _monitor;

    /// <summary>
    /// 初始化性能监控
    /// </summary>
    public static void InitializePerformanceMonitoring(ILogger logger)
    {
        _monitor = new StartupPerformanceMonitor(logger as ILogger<StartupPerformanceMonitor>);
    }

    /// <summary>
    /// 监控启动步骤
    /// </summary>
    public static IDisposable MonitorStep(string stepName)
    {
        return _monitor?.StartStep(stepName) ?? new EmptyDisposable();
    }

    /// <summary>
    /// 输出性能报告
    /// </summary>
    public static void PrintReport()
    {
        _monitor?.PrintPerformanceReport();
    }

    private class EmptyDisposable : IDisposable
    {
        public void Dispose() { }
    }
}
