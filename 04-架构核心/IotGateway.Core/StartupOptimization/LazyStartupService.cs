using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace IotGateway.Core.StartupOptimization;

/// <summary>
/// 懒加载启动服务，用于延迟启动非关键服务以加快程序启动速度
/// </summary>
public class LazyStartupService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<LazyStartupService> _logger;
    private readonly List<Func<IServiceProvider, Task>> _lazyServices = new();
    private Timer _startupTimer;

    public LazyStartupService(IServiceProvider serviceProvider, ILogger<LazyStartupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    /// <summary>
    /// 注册需要延迟启动的服务
    /// </summary>
    /// <param name="serviceInitializer">服务初始化委托</param>
    public void RegisterLazyService(Func<IServiceProvider, Task> serviceInitializer)
    {
        _lazyServices.Add(serviceInitializer);
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        // 延迟2秒后开始启动非关键服务
        _startupTimer = new Timer(async _ => await StartLazyServices(), null, TimeSpan.FromSeconds(2), Timeout.InfiniteTimeSpan);
        return Task.CompletedTask;
    }

    private async Task StartLazyServices()
    {
        var sw = Stopwatch.StartNew();
        _logger.LogInformation("开始启动延迟加载服务...");

        var tasks = new List<Task>();
        foreach (var serviceInitializer in _lazyServices)
        {
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    await serviceInitializer(_serviceProvider);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "延迟服务启动失败");
                }
            }));
        }

        await Task.WhenAll(tasks);
        
        _logger.LogInformation($"延迟加载服务启动完成，耗时: {sw.ElapsedMilliseconds}ms");
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _startupTimer?.Dispose();
        return Task.CompletedTask;
    }
}

/// <summary>
/// 启动优化扩展方法
/// </summary>
public static class StartupOptimizationExtensions
{
    /// <summary>
    /// 添加启动优化服务
    /// </summary>
    public static IServiceCollection AddStartupOptimization(this IServiceCollection services)
    {
        services.AddSingleton<LazyStartupService>();
        services.AddHostedService(provider => provider.GetRequiredService<LazyStartupService>());
        return services;
    }

    /// <summary>
    /// 注册延迟启动服务
    /// </summary>
    public static IServiceCollection AddLazyService<T>(this IServiceCollection services, int delaySeconds = 2) 
        where T : class, IHostedService
    {
        services.AddSingleton<T>();
        
        // 注册到懒加载服务中
        services.Configure<LazyStartupOptions>(options =>
        {
            options.LazyServices.Add(async provider =>
            {
                await Task.Delay(TimeSpan.FromSeconds(delaySeconds));
                var service = provider.GetRequiredService<T>();
                await service.StartAsync(CancellationToken.None);
            });
        });

        return services;
    }
}

/// <summary>
/// 懒加载启动选项
/// </summary>
public class LazyStartupOptions
{
    public List<Func<IServiceProvider, Task>> LazyServices { get; } = new();
}

/// <summary>
/// 数据库初始化优化器
/// </summary>
public static class DatabaseInitializationOptimizer
{
    /// <summary>
    /// 检查数据库是否需要初始化
    /// </summary>
    public static bool ShouldInitializeDatabase(string configId, string currentVersion)
    {
        var versionFile = Path.Combine(AppContext.BaseDirectory, "wwwroot", "db_version", $"{configId}.txt");
        
        if (!File.Exists(versionFile))
            return true;

        var savedVersion = File.ReadAllText(versionFile).Trim();
        return savedVersion != currentVersion;
    }

    /// <summary>
    /// 标记数据库初始化完成
    /// </summary>
    public static void MarkDatabaseInitialized(string configId, string version)
    {
        var versionFile = Path.Combine(AppContext.BaseDirectory, "wwwroot", "db_version", $"{configId}.txt");
        Directory.CreateDirectory(Path.GetDirectoryName(versionFile));
        File.WriteAllText(versionFile, version);
    }
}
